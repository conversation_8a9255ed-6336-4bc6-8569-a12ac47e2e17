<?php
// Database cleanup script to identify and fix duplicate records
require_once('proc/config.php');

echo "<h2>Database Duplicate Cleanup Tool</h2>";
echo "<style>
    table { border-collapse: collapse; width: 100%; margin: 20px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    .duplicate { background-color: #ffebee; }
    .action-btn { padding: 5px 10px; margin: 2px; border: none; border-radius: 3px; cursor: pointer; }
    .btn-danger { background-color: #f44336; color: white; }
    .btn-success { background-color: #4CAF50; color: white; }
    .btn-info { background-color: #2196F3; color: white; }
</style>";

// Check for duplicate employees
echo "<h3>🔍 Checking for Duplicate Employees</h3>";

$duplicate_sql = "SELECT AccessID, COUNT(*) as count, GROUP_CONCAT(EmployeeID) as employee_ids, GROUP_CONCAT(FullName) as names, GROUP_CONCAT(AgencyCompany) as departments
                  FROM tbl_personnel 
                  GROUP BY AccessID 
                  HAVING COUNT(*) > 1
                  ORDER BY AccessID";

$result = mysql_query($duplicate_sql);

if($result && mysql_num_rows($result) > 0) {
    echo "<p style='color: red;'>❌ Found duplicate employees:</p>";
    echo "<table>";
    echo "<tr><th>Access ID</th><th>Count</th><th>Employee IDs</th><th>Names</th><th>Departments</th><th>Action</th></tr>";
    
    while($row = mysql_fetch_assoc($result)) {
        echo "<tr class='duplicate'>";
        echo "<td>" . $row['AccessID'] . "</td>";
        echo "<td>" . $row['count'] . "</td>";
        echo "<td>" . $row['employee_ids'] . "</td>";
        echo "<td>" . $row['names'] . "</td>";
        echo "<td>" . $row['departments'] . "</td>";
        echo "<td>";
        echo "<button class='action-btn btn-info' onclick='showDetails(\"" . $row['AccessID'] . "\")'>View Details</button>";
        echo "<button class='action-btn btn-danger' onclick='cleanupEmployee(\"" . $row['AccessID'] . "\")'>Auto Cleanup</button>";
        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<div style='margin: 20px 0; padding: 15px; background: #fff3cd; border-radius: 5px;'>";
    echo "<h4>🛠️ Cleanup Options:</h4>";
    echo "<button class='action-btn btn-success' onclick='autoCleanupAll()'>Auto Cleanup All Duplicates</button>";
    echo "<button class='action-btn btn-info' onclick='showCleanupPreview()'>Preview Cleanup</button>";
    echo "<p><small><strong>Auto Cleanup Logic:</strong> Keeps the record with the most complete information (non-null values) and most recent data.</small></p>";
    echo "</div>";
    
} else {
    echo "<p style='color: green;'>✅ No duplicate employees found!</p>";
}

// Check for duplicate attendance records
echo "<h3>🔍 Checking for Duplicate Attendance Records</h3>";

$att_duplicate_sql = "SELECT AccessID, DATE(TimeRecord) as date, TimeFlag, COUNT(*) as count, GROUP_CONCAT(TimeRecord) as times
                      FROM tbl_in_out 
                      GROUP BY AccessID, DATE(TimeRecord), TimeFlag, HOUR(TimeRecord), MINUTE(TimeRecord)
                      HAVING COUNT(*) > 1
                      ORDER BY AccessID, date DESC
                      LIMIT 20";

$att_result = mysql_query($att_duplicate_sql);

if($att_result && mysql_num_rows($att_result) > 0) {
    echo "<p style='color: red;'>❌ Found duplicate attendance records:</p>";
    echo "<table>";
    echo "<tr><th>Access ID</th><th>Date</th><th>Type</th><th>Count</th><th>Times</th><th>Action</th></tr>";
    
    while($row = mysql_fetch_assoc($att_result)) {
        echo "<tr class='duplicate'>";
        echo "<td>" . $row['AccessID'] . "</td>";
        echo "<td>" . $row['date'] . "</td>";
        echo "<td>" . $row['TimeFlag'] . "</td>";
        echo "<td>" . $row['count'] . "</td>";
        echo "<td>" . $row['times'] . "</td>";
        echo "<td>";
        echo "<button class='action-btn btn-danger' onclick='cleanupAttendance(\"" . $row['AccessID'] . "\", \"" . $row['date'] . "\", \"" . $row['TimeFlag'] . "\")'>Cleanup</button>";
        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<div style='margin: 20px 0; padding: 15px; background: #fff3cd; border-radius: 5px;'>";
    echo "<button class='action-btn btn-success' onclick='cleanupAllAttendance()'>Cleanup All Attendance Duplicates</button>";
    echo "<p><small><strong>Attendance Cleanup:</strong> Keeps the first record of each duplicate set and removes the rest.</small></p>";
    echo "</div>";
    
} else {
    echo "<p style='color: green;'>✅ No duplicate attendance records found!</p>";
}

// Database statistics
echo "<h3>📊 Database Statistics</h3>";
$stats_sql = "SELECT 
    (SELECT COUNT(*) FROM tbl_personnel) as total_personnel,
    (SELECT COUNT(DISTINCT AccessID) FROM tbl_personnel) as unique_personnel,
    (SELECT COUNT(*) FROM tbl_in_out) as total_attendance,
    (SELECT COUNT(DISTINCT CONCAT(AccessID, DATE(TimeRecord), TimeFlag)) FROM tbl_in_out) as unique_attendance";

$stats_result = mysql_query($stats_sql);
if($stats_result && mysql_num_rows($stats_result) > 0) {
    $stats = mysql_fetch_assoc($stats_result);
    
    echo "<table>";
    echo "<tr><th>Metric</th><th>Count</th><th>Status</th></tr>";
    echo "<tr><td>Total Personnel Records</td><td>" . $stats['total_personnel'] . "</td><td>" . ($stats['total_personnel'] == $stats['unique_personnel'] ? '✅ Clean' : '❌ Has Duplicates') . "</td></tr>";
    echo "<tr><td>Unique Personnel</td><td>" . $stats['unique_personnel'] . "</td><td>-</td></tr>";
    echo "<tr><td>Total Attendance Records</td><td>" . $stats['total_attendance'] . "</td><td>" . ($stats['total_attendance'] == $stats['unique_attendance'] ? '✅ Clean' : '❌ Has Duplicates') . "</td></tr>";
    echo "<tr><td>Unique Attendance Records</td><td>" . $stats['unique_attendance'] . "</td><td>-</td></tr>";
    echo "</table>";
}

echo "<hr>";
echo "<p><a href='index.php'>← Back to Login</a> | <a href='reports_dashboard.php'>Reports Dashboard</a></p>";
?>

<script>
function showDetails(accessId) {
    alert('Showing details for AccessID: ' + accessId + '\n\nThis will open a detailed view of all records for this employee.');
    // You can implement a detailed view here
}

function cleanupEmployee(accessId) {
    if(confirm('Are you sure you want to cleanup duplicates for AccessID: ' + accessId + '?\n\nThis will keep the most complete record and remove duplicates.')) {
        // AJAX call to cleanup specific employee
        fetch('cleanup_duplicates.php?action=cleanup_employee&access_id=' + accessId)
        .then(response => response.text())
        .then(data => {
            alert('Cleanup completed for ' + accessId);
            location.reload();
        });
    }
}

function autoCleanupAll() {
    if(confirm('Are you sure you want to cleanup ALL duplicate employees?\n\nThis action cannot be undone!')) {
        // AJAX call to cleanup all employees
        fetch('cleanup_duplicates.php?action=cleanup_all_employees')
        .then(response => response.text())
        .then(data => {
            alert('All employee duplicates have been cleaned up!');
            location.reload();
        });
    }
}

function cleanupAttendance(accessId, date, timeFlag) {
    if(confirm('Cleanup attendance duplicates for ' + accessId + ' on ' + date + ' (' + timeFlag + ')?')) {
        fetch('cleanup_duplicates.php?action=cleanup_attendance&access_id=' + accessId + '&date=' + date + '&flag=' + timeFlag)
        .then(response => response.text())
        .then(data => {
            alert('Attendance duplicates cleaned up!');
            location.reload();
        });
    }
}

function cleanupAllAttendance() {
    if(confirm('Are you sure you want to cleanup ALL duplicate attendance records?\n\nThis action cannot be undone!')) {
        fetch('cleanup_duplicates.php?action=cleanup_all_attendance')
        .then(response => response.text())
        .then(data => {
            alert('All attendance duplicates have been cleaned up!');
            location.reload();
        });
    }
}

function showCleanupPreview() {
    alert('Cleanup Preview:\n\n1. Identifies records with same AccessID\n2. Keeps record with most non-null values\n3. Removes duplicate records\n4. Updates references if needed');
}
</script>
