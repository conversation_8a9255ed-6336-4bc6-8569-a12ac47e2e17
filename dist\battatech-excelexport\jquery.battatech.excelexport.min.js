/*! battatech_excelexport 2014-11-25 */
!function(a){var b={containerid:null,datatype:"table",dataset:null,columns:null,returnUri:!1,worksheetName:"My Worksheet",encoding:"utf-8"},c=b;a.fn.battatech_excelexport=function(d){function e(){var a=c.datatype.toLowerCase();switch(f(a),a){case"table":l=i(g());break;case"json":l=i(h());break;case"xml":l=i(h());break;case"jqgrid":l=i(h())}return c.returnUri?l:void window.open(l)}function f(b){switch(b){case"table":break;case"json":m=c.dataset;break;case"xml":a(c.dataset).find("row").each(function(){var b={};null!=this.attributes&&this.attributes.length>0&&(a(this.attributes).each(function(){b[this.name]=this.value}),m.push(b))});break;case"jqgrid":a(c.dataset).find("rows > row").each(function(){var b={};null!=this.children&&this.children.length>0&&(a(this.children).each(function(){b[this.tagName]=a(this).text()}),m.push(b))})}}function g(){var b=a("<div>").append(a("#"+c.containerid).clone()).html();return b}function h(){var b="<table>";return b+="<thead><tr>",a(c.columns).each(function(){1!=this.ishidden&&(b+="<th",null!=this.width&&(b+=" style='width: "+this.width+"'"),b+=">",b+=this.headertext,b+="</th>")}),b+="</tr></thead>",b+="<tbody>",a(m).each(function(d,e){b+="<tr>",a(c.columns).each(function(){e.hasOwnProperty(this.datafield)&&1!=this.ishidden&&(b+="<td",null!=this.width&&(b+=" style='width: "+this.width+"'"),b+=">",b+=e[this.datafield],b+="</td>")}),b+="</tr>"}),b+="</tbody>",b+="</table>"}function i(a){var d="<html xmlns:o='urn:schemas-microsoft-com:office:office' xmlns:x='urn:schemas-microsoft-com:office:excel' xmlns='http://www.w3.org/TR/REC-html40'>";d+="<head>",d+='<meta http-equiv="Content-type" content="text/html;charset='+b.encoding+'" />',d+="<!--[if gte mso 9]>",d+="<xml>",d+="<x:ExcelWorkbook>",d+="<x:ExcelWorksheets>",d+="<x:ExcelWorksheet>",d+="<x:Name>",d+="{worksheet}",d+="</x:Name>",d+="<x:WorksheetOptions>",d+="<x:DisplayGridlines/>",d+="</x:WorksheetOptions>",d+="</x:ExcelWorksheet>",d+="</x:ExcelWorksheets>",d+="</x:ExcelWorkbook>",d+="</xml>",d+="<![endif]-->",d+="</head>",d+="<body>",d+=a.replace(/"/g,"'"),d+="</body>",d+="</html>";var e="data:application/vnd.ms-excel;base64,",f={worksheet:c.worksheetName,table:a};return e+j(k(d,f))}function j(a){return window.btoa(unescape(encodeURIComponent(a)))}function k(a,b){return a.replace(/{(\w+)}/g,function(a,c){return b[c]})}c=a.extend({},b,d);var l,m=[];return e()}}(jQuery);