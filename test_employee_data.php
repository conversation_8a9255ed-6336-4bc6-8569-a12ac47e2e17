<?php
// Simple test to check if employee data can be retrieved
include('proc/config.php');

echo "<h3>Testing Employee Data Retrieval</h3>";

// First, let's see what employees exist
$sql = "SELECT EntryID, EmployeeID, FullName FROM tbl_personnel LIMIT 5";
$result = mysql_query($sql);

if($result) {
    echo "<h4>Available Employees:</h4>";
    echo "<table border='1'>";
    echo "<tr><th>EntryID</th><th>EmployeeID</th><th>FullName</th><th>Test Edit</th></tr>";
    
    while($row = mysql_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['EntryID'] . "</td>";
        echo "<td>" . $row['EmployeeID'] . "</td>";
        echo "<td>" . $row['FullName'] . "</td>";
        echo "<td><a href='#' onclick='testEdit(" . $row['EntryID'] . ")'>Test Edit</a></td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "Error: " . mysql_error();
}

// Test the getEmployeeData function
if(isset($_GET['test_id'])) {
    $test_id = $_GET['test_id'];
    echo "<h4>Testing Employee ID: $test_id</h4>";
    
    // Simulate the AJAX call
    $_POST['employee_id'] = $test_id;
    include('function/getEmployeeData.php');
}
?>

<script>
function testEdit(employeeId) {
    window.location.href = 'test_employee_data.php?test_id=' + employeeId;
}
</script>

<style>
table { border-collapse: collapse; margin: 10px 0; }
th, td { padding: 8px; border: 1px solid #ccc; }
th { background: #f0f0f0; }
</style>
