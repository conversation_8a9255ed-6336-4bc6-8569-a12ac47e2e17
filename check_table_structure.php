<?php
// Check the actual table structure
include('proc/config.php');

echo "<h3>Table Structure Check</h3>";

// Show the current table structure
$sql = "DESCRIBE tbl_personnel";
$result = mysql_query($sql);

if($result) {
    echo "<h4>Current Columns in tbl_personnel:</h4>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr style='background: #f0f0f0;'><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    $columns = array();
    while($row = mysql_fetch_assoc($result)) {
        $columns[] = $row['Field'];
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . ($row['Default'] ?? 'NULL') . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check if Address column exists
    echo "<h4>Column Check:</h4>";
    echo "<p><strong>Address column exists:</strong> " . (in_array('Address', $columns) ? 'YES' : 'NO') . "</p>";
    echo "<p><strong>ContactNo column exists:</strong> " . (in_array('ContactNo', $columns) ? 'YES' : 'NO') . "</p>";
    
    // Show SQL to add missing columns
    echo "<h4>SQL to Add Missing Columns:</h4>";
    if(!in_array('Address', $columns)) {
        echo "<code>ALTER TABLE tbl_personnel ADD COLUMN Address TEXT;</code><br>";
    }
    if(!in_array('ContactNo', $columns)) {
        echo "<code>ALTER TABLE tbl_personnel ADD COLUMN ContactNo VARCHAR(50);</code><br>";
    }
    
} else {
    echo "Error describing table: " . mysql_error();
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { border-collapse: collapse; margin: 10px 0; }
th, td { padding: 8px; border: 1px solid #ccc; text-align: left; }
th { background: #f0f0f0; }
code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; font-family: monospace; }
</style>
