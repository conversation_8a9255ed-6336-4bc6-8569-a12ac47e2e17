<?php
include('auth.php');
require_once('proc/config.php');

// Get user's assigned branches
$user_branches = array();
$assigned_branches_str = '';
$sql = "SELECT assigned_branches FROM tb_user WHERE IDno = '".$_SESSION['SESS_USER_ID']."'";
$result = mysql_query($sql);
if($result && mysql_num_rows($result) > 0) {
    $row = mysql_fetch_assoc($result);
    $assigned_branches_str = $row['assigned_branches'];
    if($assigned_branches_str) {
        $user_branches = explode(',', $assigned_branches_str);
    }
}

// Handle branch parameter from login redirect
if(isset($_GET['branch'])) {
    $branch_code = clean($_GET['branch']);

    // Find branch ID by branch code
    $branch_sql = "SELECT branch_id FROM tbl_branches WHERE LOWER(branch_code) = LOWER('$branch_code') LIMIT 1";
    $branch_result = mysql_query($branch_sql);
    if($branch_result && mysql_num_rows($branch_result) > 0) {
        $branch_row = mysql_fetch_assoc($branch_result);
        $_SESSION['CURRENT_BRANCH_ID'] = $branch_row['branch_id'];
    }
}

// Get current selected branch (set during login, no switching allowed)
$current_branch_id = isset($_SESSION['CURRENT_BRANCH_ID']) ? $_SESSION['CURRENT_BRANCH_ID'] : 1;

// Get current branch details for display only
$current_branch = array(
    'branch_id' => $current_branch_id,
    'branch_name' => 'Main Branch',
    'branch_code' => 'MAIN',
    'branch_location' => ''
);

$branch_sql = "SELECT * FROM tbl_branches WHERE branch_id = '$current_branch_id' LIMIT 1";
$branch_result = mysql_query($branch_sql);
if($branch_result && mysql_num_rows($branch_result) > 0) {
    $current_branch = mysql_fetch_assoc($branch_result);
    if(!isset($current_branch['branch_location'])) {
        $current_branch['branch_location'] = '';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Enhanced Dashboard - Biometric Access System</title>
    
    <!-- Bootstrap CSS -->
    <link href="assets/css/bootstrap.css" rel="stylesheet">
    <!-- Font Awesome CDN for better performance -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" crossorigin="anonymous">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Preload critical fonts -->
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/webfonts/fa-solid-900.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/webfonts/fa-regular-400.woff2" as="font" type="font/woff2" crossorigin>
    
    <style>
        /* Font loading optimization */
        @font-face {
            font-family: 'FontAwesome';
            font-display: swap; /* Use fallback font while loading */
        }

        /* Fallback for Font Awesome icons */
        .fa, .fas, .far, .fab {
            font-family: 'Font Awesome 6 Free', 'Font Awesome 5 Free', 'FontAwesome', sans-serif;
            font-display: swap;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }
        
        .dashboard-container {
            padding: 20px;
            margin-top: 70px;
        }
        
        .glass-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        
        .glass-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }
        
        .stats-card {
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .stats-icon {
            font-size: 3.5rem;
            margin-bottom: 15px;
            opacity: 0.8;
        }
        
        .stats-number {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 5px;
            counter-reset: num var(--num);
        }
        
        .stats-label {
            color: #666;
            font-size: 1rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 500;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        
        .navbar-brand {
            font-weight: bold;
            font-size: 1.3rem;
        }
        
        .welcome-header {
            color: white;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .welcome-header h1 {
            font-size: 2.8rem;
            font-weight: 300;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .welcome-header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .quick-action-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .action-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-decoration: none;
            transition: all 0.3s ease;
            text-align: center;
            border: none;
        }
        
        .action-card:hover {
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
            color: white;
            text-decoration: none;
        }
        
        .action-card i {
            font-size: 2rem;
            margin-bottom: 10px;
            display: block;
        }
        
        .activity-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #eee;
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin-right: 15px;
        }
        
        .activity-content {
            flex: 1;
        }
        
        .activity-time {
            font-size: 0.85rem;
            color: #999;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-online { background-color: #4CAF50; }
        .status-offline { background-color: #f44336; }
        
        @keyframes countUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .animate-count {
            animation: countUp 0.8s ease-out;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-inverse navbar-fixed-top" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-collapse">
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="dashboard_enhanced.php">
                    <i class="fa fa-fingerprint"></i> Biometric Access Control System
                </a>
            </div>
            <div class="navbar-collapse collapse">
                <ul class="nav navbar-nav navbar-right">
                    <!-- Current Branch Display -->
                    <li class="navbar-text" style="color: #fff; padding: 15px;">
                        <i class="fa fa-building"></i> <?php echo $current_branch['branch_name']; ?>
                    </li>
                    <li><a href="#" id="refresh-data"><i class="fa fa-refresh"></i> Refresh</a></li>
                    <?php if($_SESSION['ACCESSLEVEL']=='Admin') { ?>
                        <li><a href="branch_management.php"><i class="fa fa-building"></i> Branches</a></li>
                        <li><a href="pg_accounts.php"><i class="fa fa-users"></i> Employees</a></li>
                        <li><a href="biometrics_logs.php"><i class="fa fa-clock-o"></i> Attendance</a></li>
                        <li><a href="reports_dashboard.php"><i class="fa fa-bar-chart"></i> Reports</a></li>
                        <li><a href="pg_settings.php"><i class="fa fa-cog"></i> Settings</a></li>
                        <li><a href="logout.php"><i class="fa fa-sign-out"></i> Logout</a></li>
                    <?php } ?>
                </ul>
            </div>
        </div>
    </nav>

    <div class="dashboard-container">
        <!-- Welcome Header -->
        <div class="welcome-header">
            <h1>Welcome, <?php echo $_SESSION['SESS_USER_FULL_NAME']; ?>!</h1>
            <p>Enhanced Biometric Access Control Dashboard</p>
            <p style="font-size: 2.5rem; opacity: 0.9; margin-bottom: 5px;">
                <i class="fa fa-building"></i><?php echo $current_branch['branch_name']; ?>
            </p>
            <p style="font-size: 2.0rem; opacity: 0.8; margin-bottom: 10px;">
                <i class="fa fa-map-marker"></i> <?php echo $current_branch['branch_location']; ?>
            </p>
            <p id="current-time" style="font-size: 1.5rem; opacity: 0.8;"></p>
            <p style="font-size: 0.9rem; opacity: 0.7;">
                <span class="status-indicator status-online"></span>
                System Status: Online
            </p>
        </div>

        <div class="container-fluid">
            <!-- Statistics Cards -->
            <div class="row" id="stats-row">
                <div class="col-md-3 col-sm-6">
                    <div class="glass-card stats-card">
                        <div class="stats-icon text-primary">
                            <i class="fa fa-users"></i>
                        </div>
                        <div class="stats-number text-primary animate-count" id="total-personnel">
                            <div class="loading">Loading...</div>
                        </div>
                        <div class="stats-label">Total Employees</div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="glass-card stats-card">
                        <div class="stats-icon text-success">
                            <i class="fa fa-user-check"></i>
                        </div>
                        <div class="stats-number text-success animate-count" id="active-personnel">
                            <div class="loading">Loading...</div>
                        </div>
                        <div class="stats-label">Active Employees</div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="glass-card stats-card">
                        <div class="stats-icon text-warning">
                            <i class="fa fa-clock-o"></i>
                        </div>
                        <div class="stats-number text-warning animate-count" id="today-attendance">
                            <div class="loading">Loading...</div>
                        </div>
                        <div class="stats-label">Today's Attendance</div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="glass-card stats-card">
                        <div class="stats-icon text-info">
                            <i class="fa fa-key"></i>
                        </div>
                        <div class="stats-number text-info animate-count" id="total-users">
                            <div class="loading">Loading...</div>
                        </div>
                        <div class="stats-label">System Users</div>
                    </div>
                </div>
            </div>

            <!-- Charts and Quick Actions -->
            <div class="row">
                <div class="col-md-8">
                    <div class="glass-card">
                        <h3><i class="fa fa-bar-chart"></i> Weekly Attendance Overview</h3>
                        <div class="chart-container">
                            <canvas id="attendanceChart"></canvas>
                        </div>
                    </div>
                    
                    <div class="glass-card">
                        <h3><i class="fa fa-bolt"></i> Quick Actions</h3>
                        <div class="quick-action-grid">
                            <a href="pg_accounts.php" class="action-card">
                                <i class="fa fa-user-plus"></i>
                                <div>Manage Employees</div>
                            </a>
                            <a href="biometrics_logs.php" class="action-card">
                                <i class="fa fa-clock-o"></i>
                                <div>View Attendance</div>
                            </a>
                            <a href="reports_dashboard.php" class="action-card">
                                <i class="fa fa-bar-chart"></i>
                                <div>Comprehensive Reports</div>
                            </a>
                            <a href="pg_settings.php" class="action-card">
                                <i class="fa fa-cog"></i>
                                <div>System Settings</div>
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="glass-card">
                        <h3><i class="fa fa-history"></i> Recent Activity</h3>
                        <div id="recent-activities">
                            <div class="loading">Loading activities...</div>
                        </div>
                    </div>
                    
                    <div class="glass-card">
                        <h3><i class="fa fa-building"></i> Department Overview</h3>
                        <div class="chart-container" style="height: 200px;">
                            <canvas id="departmentChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="assets/js/jquery.js"></script>
    <script src="assets/js/bootstrap.min.js"></script>
    <script>
        let attendanceChart, departmentChart;
        
        $(document).ready(function() {
            loadDashboardData();
            updateTime();
            setInterval(updateTime, 1000);
            setInterval(loadDashboardData, 30000); // Refresh every 30 seconds
            
            $('#refresh-data').click(function(e) {
                e.preventDefault();
                loadDashboardData();
            });
        });
        
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString();
            $('#current-time').text(timeString);
        }
        
        function loadDashboardData() {
            $.ajax({
                url: 'api/dashboard_data.php',
                method: 'GET',
                data: { branch_id: <?php echo $current_branch_id; ?> },
                dataType: 'json',
                success: function(data) {
                    updateStats(data);
                    updateRecentActivities(data.recent_activities);
                    updateCharts(data);
                },
                error: function(xhr, status, error) {
                    console.error('Error loading dashboard data:', error);
                }
            });
        }

        // Branch switching removed - branches are selected at login
        
        function updateStats(data) {
            animateNumber('#total-personnel', data.total_personnel);
            animateNumber('#active-personnel', data.active_personnel);
            animateNumber('#today-attendance', data.today_attendance);
            animateNumber('#total-users', data.total_users);
        }
        
        function animateNumber(selector, value) {
            const element = $(selector);
            element.html(value);
            element.addClass('animate-count');
            setTimeout(() => element.removeClass('animate-count'), 800);
        }
        
        function updateRecentActivities(activities) {
            const container = $('#recent-activities');
            if (activities.length === 0) {
                container.html('<p class="text-muted">No recent activity found.</p>');
                return;
            }
            
            let html = '';
            activities.forEach(activity => {
                const initials = activity.name.split(' ').map(n => n[0]).join('').substring(0, 2);
                html += `
                    <div class="activity-item">
                        <div class="activity-avatar">${initials}</div>
                        <div class="activity-content">
                            <strong>${activity.name}</strong>
                            <div class="activity-time">${activity.action} - ${activity.formatted_time}</div>
                        </div>
                    </div>
                `;
            });
            container.html(html);
        }
        
        function updateCharts(data) {
            updateAttendanceChart(data.weekly_attendance);
            updateDepartmentChart(data.department_breakdown);
        }
        
        function updateAttendanceChart(weeklyData) {
            const ctx = document.getElementById('attendanceChart').getContext('2d');
            
            if (attendanceChart) {
                attendanceChart.destroy();
            }
            
            attendanceChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: weeklyData.map(d => d.day),
                    datasets: [{
                        label: 'Daily Attendance',
                        data: weeklyData.map(d => d.count),
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }
        
        function updateDepartmentChart(departmentData) {
            const ctx = document.getElementById('departmentChart').getContext('2d');
            
            if (departmentChart) {
                departmentChart.destroy();
            }
            
            if (departmentData.length === 0) return;
            
            departmentChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: departmentData.map(d => d.department),
                    datasets: [{
                        data: departmentData.map(d => d.count),
                        backgroundColor: [
                            '#FF6384',
                            '#36A2EB',
                            '#FFCE56',
                            '#4BC0C0',
                            '#9966FF'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }
    </script>
</body>
</html>
