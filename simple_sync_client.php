<?php
/**
 * Simple Sync Client for your existing simple_db_api.php
 * This works with your shared hosting API
 */

// Configuration - Update these with your online domain details
$config = array(
    'online_api_url' => 'https://whatsapp.ipd.ac.pk/api/simple_db_api.php',  // Replace with your domain
    'api_key' => '',  // Optional - if you enable API key in your API
    'timeout' => 30
);

// Local database config
require_once('proc/config.php');

// Handle AJAX requests
if (isset($_GET['action'])) {
    // Clean any output buffers first
    while (ob_get_level()) {
        ob_end_clean();
    }

    // Set JSON header
    header('Content-Type: application/json');

    $method = $_SERVER['REQUEST_METHOD'];
    $action = $_GET['action'];

    // Start fresh output buffer
    ob_start();

    if ($method === 'POST') {
        handleLocalPostRequest($action);
    } else {
        handleLocalRequest($action);
    }

    // Get the output and ensure it's clean
    $output = ob_get_clean();

    // Make sure we only output JSON
    if (!empty($output)) {
        echo $output;
    }

    exit;
}

function handleLocalRequest($action) {
    switch ($action) {
        case 'local_stats':
            getLocalStats();
            break;
        case 'get_local_employees':
            getLocalEmployees();
            break;
        case 'get_local_attendance':
            getLocalAttendance();
            break;
        default:
            echo json_encode(['success' => false, 'message' => 'Unknown action: ' . $action]);
    }
}

function handleLocalPostRequest($action) {
    $input = json_decode(file_get_contents('php://input'), true);

    // Log the request for debugging
    error_log("POST Request - Action: $action, Input: " . print_r($input, true));

    switch ($action) {
        case 'save_employees':
            if (!$input || !isset($input['data'])) {
                echo json_encode(['success' => false, 'message' => 'Invalid input data']);
                return;
            }
            saveEmployeesToLocal($input['data']);
            break;
        case 'save_attendance':
            if (!$input || !isset($input['data'])) {
                echo json_encode(['success' => false, 'message' => 'Invalid input data']);
                return;
            }
            saveAttendanceToLocal($input['data']);
            break;
        default:
            echo json_encode(['success' => false, 'message' => 'Unknown action: ' . $action]);
    }
}

function getLocalStats() {
    global $link; // Use the connection from config.php
    $stats = ['employees' => 0, 'attendance' => 0];

    $emp_result = mysqli_query($link, "SELECT COUNT(*) as count FROM tbl_personnel");
    if ($emp_result) {
        $emp_row = mysqli_fetch_assoc($emp_result);
        $stats['employees'] = $emp_row['count'];
    }

    $att_result = mysqli_query($link, "SELECT COUNT(*) as count FROM tbl_in_out");
    if ($att_result) {
        $att_row = mysqli_fetch_assoc($att_result);
        $stats['attendance'] = $att_row['count'];
    }

    echo json_encode($stats);
}

function getLocalEmployees() {
    global $link;
    $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 100;
    $sql = "SELECT * FROM tbl_personnel ORDER BY AccessID LIMIT $limit";
    $result = mysqli_query($link, $sql);

    $employees = [];
    if ($result) {
        while ($row = mysqli_fetch_assoc($result)) {
            $employees[] = $row;
        }
    }

    echo json_encode(['success' => true, 'data' => $employees]);
}

function getLocalAttendance() {
    global $link;
    $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 100;
    $days = isset($_GET['days']) ? intval($_GET['days']) : 30;

    $sql = "SELECT * FROM tbl_in_out WHERE DATE(TimeRecord) >= DATE_SUB(CURDATE(), INTERVAL $days DAY) ORDER BY TimeRecord DESC LIMIT $limit";
    $result = mysqli_query($link, $sql);

    $attendance = [];
    if ($result) {
        while ($row = mysqli_fetch_assoc($result)) {
            $attendance[] = $row;
        }
    }

    echo json_encode(['success' => true, 'data' => $attendance]);
}

function saveEmployeesToLocal($employees) {
    $created = 0;
    $updated = 0;
    $errors = 0;

    // Log the function call
    error_log("saveEmployeesToLocal called with " . count($employees) . " employees");

    // Ensure we have a valid array
    if (!is_array($employees)) {
        $response = ['success' => false, 'message' => 'Invalid employee data format'];
        error_log("saveEmployeesToLocal error: " . json_encode($response));
        echo json_encode($response);
        return;
    }

    foreach ($employees as $employee) {
        if (!isset($employee['AccessID']) || !$employee['AccessID']) {
            $errors++;
            continue;
        }

        global $link;

        // Clean the AccessID (remove whitespace and newlines)
        $access_id = mysqli_real_escape_string($link, trim($employee['AccessID']));

        // Check if exists
        $check_sql = "SELECT COUNT(*) as count FROM tbl_personnel WHERE AccessID = '$access_id'";
        $check_result = @mysqli_query($link, $check_sql);
        $exists = false;

        if ($check_result) {
            $check_row = mysqli_fetch_assoc($check_result);
            $exists = $check_row['count'] > 0;
        } else {
            $errors++;
            continue;
        }
        
        // Prepare data
        $employee_id = mysqli_real_escape_string($link, $employee['EmployeeID'] ?? '');
        $full_name = mysqli_real_escape_string($link, $employee['FullName'] ?? '');
        $position = mysqli_real_escape_string($link, $employee['Position'] ?? '');
        $agency = mysqli_real_escape_string($link, $employee['AgencyCompany'] ?? '');
        $contact = mysqli_real_escape_string($link, $employee['ContactNo'] ?? '');
        $date_hired = mysqli_real_escape_string($link, $employee['DateHired'] ?? '');
        $status = mysqli_real_escape_string($link, $employee['Status'] ?? '');
        $time_in = mysqli_real_escape_string($link, $employee['TimeIN'] ?? '');
        $time_out = mysqli_real_escape_string($link, $employee['TimeOut'] ?? '');
        
        if ($exists) {
            // Update existing employee by AccessID
            $sql = "UPDATE tbl_personnel SET
                FullName = '$full_name',
                Position = '$position',
                AgencyCompany = '$agency',
                ContactNo = '$contact',
                DateHired = '$date_hired',
                Status = '$status',
                TimeIN = '$time_in',
                TimeOut = '$time_out'
                WHERE AccessID = '$access_id'";

            if (@mysqli_query($link, $sql)) {
                $updated++;
            } else {
                $errors++;
                error_log("Update failed for AccessID $access_id: " . mysqli_error($link));
            }
        } else {
            // For new employees, use AccessID as EmployeeID if EmployeeID is empty or conflicts
            if (empty($employee_id)) {
                $employee_id = $access_id;
            }

            // Check if EmployeeID already exists and modify if needed
            $emp_check_sql = "SELECT COUNT(*) as count FROM tbl_personnel WHERE EmployeeID = '$employee_id'";
            $emp_check_result = @mysqli_query($link, $emp_check_sql);
            if ($emp_check_result) {
                $emp_check_row = mysqli_fetch_assoc($emp_check_result);
                if ($emp_check_row['count'] > 0) {
                    // EmployeeID exists, use AccessID instead
                    $employee_id = $access_id;
                }
            }

            $sql = "INSERT INTO tbl_personnel (EmployeeID, AccessID, FullName, Position, AgencyCompany, ContactNo, DateHired, Status, TimeIN, TimeOut)
                VALUES ('$employee_id', '$access_id', '$full_name', '$position', '$agency', '$contact', '$date_hired', '$status', '$time_in', '$time_out')";

            if (@mysqli_query($link, $sql)) {
                $created++;
            } else {
                $errors++;
                error_log("Insert failed for AccessID $access_id: " . mysqli_error($link));
            }
        }
    }
    
    $response = [
        'success' => $errors === 0,
        'created' => $created,
        'updated' => $updated,
        'errors' => $errors
    ];

    // Log the response
    error_log("saveEmployeesToLocal response: " . json_encode($response));

    echo json_encode($response);
}

function saveAttendanceToLocal($attendance_records) {
    $created = 0;
    $skipped = 0;
    $errors = 0;
    
    foreach ($attendance_records as $record) {
        global $link;

        if (!isset($record['AccessID']) || !isset($record['TimeRecord']) || !isset($record['TimeFlag'])) {
            $errors++;
            continue;
        }

        $access_id = mysqli_real_escape_string($link, $record['AccessID']);
        $time_record = mysqli_real_escape_string($link, $record['TimeRecord']);
        $time_flag = mysqli_real_escape_string($link, $record['TimeFlag']);

        // Check if exists
        $check_sql = "SELECT COUNT(*) as count FROM tbl_in_out WHERE AccessID = '$access_id' AND TimeRecord = '$time_record' AND TimeFlag = '$time_flag'";
        $check_result = mysqli_query($link, $check_sql);
        $exists = false;

        if ($check_result) {
            $check_row = mysqli_fetch_assoc($check_result);
            $exists = $check_row['count'] > 0;
        }

        if (!$exists) {
            $full_name = mysqli_real_escape_string($link, $record['FullName'] ?? '');
            $access_area = mysqli_real_escape_string($link, $record['AccessArea'] ?? '');

            $sql = "INSERT INTO tbl_in_out (AccessID, FullName, TimeRecord, TimeFlag, AccessArea)
                VALUES ('$access_id', '$full_name', '$time_record', '$time_flag', '$access_area')";

            if (mysqli_query($link, $sql)) {
                $created++;
            } else {
                $errors++;
            }
        } else {
            $skipped++;
        }
    }
    
    echo json_encode([
        'success' => $errors === 0,
        'created' => $created,
        'skipped' => $skipped,
        'errors' => $errors
    ]);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Database Sync</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sync-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            margin: 20px auto;
            max-width: 1000px;
            padding: 30px;
        }
        .sync-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            margin: 15px 0;
            padding: 20px;
        }
        .btn-sync {
            background: linear-gradient(135deg, #28a745, #20c997);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .btn-sync:hover {
            background: linear-gradient(135deg, #218838, #1ea085);
            transform: translateY(-2px);
            color: white;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-online { background-color: #28a745; }
        .status-offline { background-color: #dc3545; }
        .status-testing { background-color: #ffc107; animation: pulse 1s infinite; }
        .log-container {
            background: #f8f9fa;
            border-radius: 8px;
            max-height: 300px;
            overflow-y: auto;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }
        .stat-box {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            border-left: 4px solid #007bff;
            margin: 10px 0;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #007bff;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="sync-container">
            <div class="text-center mb-4">
                <h1><i class="fas fa-sync-alt me-3"></i>Simple Database Sync</h1>
                <p class="text-muted">Sync data with your online server using your existing API</p>
            </div>

            <!-- API Configuration -->
            <div class="sync-card">
                <h4><i class="fas fa-cog me-2"></i>API Configuration</h4>
                <div class="row">
                    <div class="col-md-8">
                        <label class="form-label">Online API URL:</label>
                        <input type="url" class="form-control" id="api-url" value="<?php echo $config['online_api_url']; ?>">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">Status:</label>
                        <div class="mt-2">
                            <span class="status-indicator" id="api-status"></span>
                            <span id="api-status-text">Not tested</span>
                        </div>
                    </div>
                </div>
                <div class="mt-3">
                    <button class="btn btn-sync" onclick="testConnection()">
                        <i class="fas fa-plug me-2"></i>Test Connection
                    </button>
                </div>
            </div>

            <!-- Statistics -->
            <div class="sync-card">
                <h4><i class="fas fa-chart-bar me-2"></i>Database Statistics</h4>
                <div class="row">
                    <div class="col-md-3">
                        <div class="stat-box">
                            <div class="stat-number" id="local-employees">-</div>
                            <div class="text-muted">Local Employees</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-box">
                            <div class="stat-number" id="online-employees">-</div>
                            <div class="text-muted">Online Employees</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-box">
                            <div class="stat-number" id="local-attendance">-</div>
                            <div class="text-muted">Local Attendance</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-box">
                            <div class="stat-number" id="online-attendance">-</div>
                            <div class="text-muted">Online Attendance</div>
                        </div>
                    </div>
                </div>
                <button class="btn btn-outline-primary" onclick="refreshStats()">
                    <i class="fas fa-refresh me-2"></i>Refresh
                </button>
            </div>

            <!-- Sync Operations -->
            <div class="sync-card">
                <h4><i class="fas fa-exchange-alt me-2"></i>Sync Operations</h4>
                <div class="row">
                    <div class="col-md-6">
                        <h5><i class="fas fa-users me-2"></i>Employee Data</h5>
                        <div class="d-grid gap-2">
                            <button class="btn btn-sync" onclick="syncEmployees('upload')">
                                <i class="fas fa-upload me-2"></i>Upload Local → Online
                            </button>
                            <button class="btn btn-sync" onclick="syncEmployees('download')">
                                <i class="fas fa-download me-2"></i>Download Online → Local
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h5><i class="fas fa-clock me-2"></i>Attendance Data</h5>
                        <div class="d-grid gap-2">
                            <button class="btn btn-sync" onclick="syncAttendance('upload')">
                                <i class="fas fa-upload me-2"></i>Upload Local → Online
                            </button>
                            <button class="btn btn-sync" onclick="syncAttendance('download')">
                                <i class="fas fa-download me-2"></i>Download Online → Local
                            </button>
                        </div>
                    </div>
                </div>
                <hr>
                <div class="text-center">
                    <h5>Complete Sync</h5>
                    <div class="btn-group">
                        <button class="btn btn-sync btn-lg" onclick="fullSync('upload')">
                            <i class="fas fa-cloud-upload-alt me-2"></i>Full Upload
                        </button>
                        <button class="btn btn-sync btn-lg" onclick="fullSync('download')">
                            <i class="fas fa-cloud-download-alt me-2"></i>Full Download
                        </button>
                    </div>
                </div>
            </div>

            <!-- Sync Log -->
            <div class="sync-card">
                <h5><i class="fas fa-list-alt me-2"></i>Sync Log</h5>
                <div class="log-container" id="sync-log">
                    <div class="text-muted">Sync operations will be logged here...</div>
                </div>
                <div class="mt-3">
                    <button class="btn btn-outline-secondary btn-sm" onclick="clearLog()">
                        <i class="fas fa-trash me-1"></i>Clear Log
                    </button>
                </div>
            </div>

            <!-- Navigation -->
            <div class="text-center mt-4">
                <a href="index.php" class="btn btn-outline-primary me-2">
                    <i class="fas fa-home me-1"></i>Home
                </a>
                <a href="reports_dashboard.php" class="btn btn-outline-primary">
                    <i class="fas fa-chart-bar me-1"></i>Reports
                </a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let apiUrl = document.getElementById('api-url').value;
        let isConnected = false;

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            refreshStats();
        });

        // Test API connection
        function testConnection() {
            updateStatus('testing', 'Testing...');
            apiUrl = document.getElementById('api-url').value;

            fetch(apiUrl + '?action=test')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        updateStatus('online', 'Connected');
                        isConnected = true;
                        logMessage('✅ API connection successful');
                        refreshStats();
                    } else {
                        updateStatus('offline', 'Failed');
                        isConnected = false;
                        logMessage('❌ API connection failed: ' + data.message);
                    }
                })
                .catch(error => {
                    updateStatus('offline', 'Error');
                    isConnected = false;
                    logMessage('❌ Connection error: ' + error.message);
                });
        }

        // Update status indicator
        function updateStatus(status, text) {
            document.getElementById('api-status').className = 'status-indicator status-' + status;
            document.getElementById('api-status-text').textContent = text;
        }

        // Refresh statistics
        function refreshStats() {
            // Local stats
            fetch('simple_sync_client.php?action=local_stats')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('local-employees').textContent = data.employees || '0';
                    document.getElementById('local-attendance').textContent = data.attendance || '0';
                });

            // Online stats (if connected)
            if (isConnected) {
                fetch(apiUrl + '?action=get_stats')
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            document.getElementById('online-employees').textContent = data.data.employees || '0';
                            document.getElementById('online-attendance').textContent = data.data.attendance || '0';
                        }
                    });
            }
        }

        // Sync employees
        function syncEmployees(direction) {
            if (!isConnected) {
                logMessage('⚠️ Please test connection first');
                return;
            }

            logMessage(`🔄 Starting employee sync: ${direction}`);

            if (direction === 'upload') {
                uploadEmployees();
            } else {
                downloadEmployees();
            }
        }

        // Upload employees to online
        function uploadEmployees() {
            logMessage('📤 Fetching local employees...');

            fetch('simple_sync_client.php?action=get_local_employees&limit=1000')
                .then(response => response.json())
                .then(localData => {
                    if (localData.success) {
                        logMessage(`📤 Uploading ${localData.data.length} employees...`);

                        return fetch(apiUrl + '?action=sync_employees', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ employees: localData.data })
                        });
                    } else {
                        throw new Error('Failed to fetch local employees');
                    }
                })
                .then(response => response.json())
                .then(result => {
                    if (result.status === 'success') {
                        logMessage(`✅ Upload completed: ${result.created} created, ${result.updated} updated, ${result.errors} errors`);
                    } else {
                        logMessage(`❌ Upload failed: ${result.message}`);
                    }
                    refreshStats();
                })
                .catch(error => {
                    logMessage(`❌ Upload error: ${error.message}`);
                });
        }

        // Download employees from online
        function downloadEmployees() {
            logMessage('📥 Fetching online employees...');

            fetch(apiUrl + '?action=get_employees&limit=1000')
                .then(response => response.json())
                .then(onlineData => {
                    if (onlineData.status === 'success') {
                        logMessage(`📥 Downloading ${onlineData.data.length} employees...`);

                        return fetch('simple_sync_client.php?action=save_employees', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ data: onlineData.data })
                        });
                    } else {
                        throw new Error('Failed to fetch online employees');
                    }
                })
                .then(response => {
                    // Debug: Log the raw response before trying to parse JSON
                    return response.text().then(text => {
                        logMessage(`🔍 Raw response (${text.length} chars): ${text.substring(0, 100)}...`);

                        try {
                            const result = JSON.parse(text);
                            return result;
                        } catch (e) {
                            logMessage(`❌ JSON Parse Error: ${e.message}`);
                            logMessage(`🔍 First 200 chars: ${text.substring(0, 200)}`);
                            throw new Error(`JSON Parse Error: ${e.message}`);
                        }
                    });
                })
                .then(result => {
                    if (result.success) {
                        logMessage(`✅ Download completed: ${result.created} created, ${result.updated} updated, ${result.errors} errors`);
                    } else {
                        logMessage(`❌ Download failed: ${result.message}`);
                    }
                    refreshStats();
                })
                .catch(error => {
                    logMessage(`❌ Download error: ${error.message}`);
                });
        }

        // Sync attendance
        function syncAttendance(direction) {
            if (!isConnected) {
                logMessage('⚠️ Please test connection first');
                return;
            }

            logMessage(`🔄 Starting attendance sync: ${direction}`);

            if (direction === 'upload') {
                uploadAttendance();
            } else {
                downloadAttendance();
            }
        }

        // Upload attendance
        function uploadAttendance() {
            logMessage('📤 Fetching local attendance...');

            fetch('simple_sync_client.php?action=get_local_attendance&limit=1000&days=30')
                .then(response => response.json())
                .then(localData => {
                    if (localData.success) {
                        logMessage(`📤 Uploading ${localData.data.length} attendance records...`);

                        return fetch(apiUrl + '?action=sync_attendance', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ attendance: localData.data })
                        });
                    } else {
                        throw new Error('Failed to fetch local attendance');
                    }
                })
                .then(response => response.json())
                .then(result => {
                    if (result.status === 'success') {
                        logMessage(`✅ Upload completed: ${result.created} created, ${result.skipped} skipped, ${result.errors} errors`);
                    } else {
                        logMessage(`❌ Upload failed: ${result.message}`);
                    }
                    refreshStats();
                })
                .catch(error => {
                    logMessage(`❌ Upload error: ${error.message}`);
                });
        }

        // Download attendance
        function downloadAttendance() {
            logMessage('📥 Fetching online attendance...');

            const dateFrom = new Date();
            dateFrom.setDate(dateFrom.getDate() - 30);
            const dateFromStr = dateFrom.toISOString().split('T')[0];

            fetch(apiUrl + `?action=get_attendance&limit=1000&date_from=${dateFromStr}`)
                .then(response => response.json())
                .then(onlineData => {
                    if (onlineData.status === 'success') {
                        logMessage(`📥 Downloading ${onlineData.data.length} attendance records...`);

                        return fetch('simple_sync_client.php?action=save_attendance', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ data: onlineData.data })
                        });
                    } else {
                        throw new Error('Failed to fetch online attendance');
                    }
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        logMessage(`✅ Download completed: ${result.created} created, ${result.skipped} skipped, ${result.errors} errors`);
                    } else {
                        logMessage(`❌ Download failed: ${result.message}`);
                    }
                    refreshStats();
                })
                .catch(error => {
                    logMessage(`❌ Download error: ${error.message}`);
                });
        }

        // Full sync
        function fullSync(direction) {
            if (!confirm(`Perform full ${direction} sync?\nThis will sync both employees and attendance.`)) {
                return;
            }

            logMessage(`🔄 Starting full ${direction} sync...`);

            if (direction === 'upload') {
                uploadEmployees();
                setTimeout(() => uploadAttendance(), 3000);
            } else {
                downloadEmployees();
                setTimeout(() => downloadAttendance(), 3000);
            }
        }

        // Log message
        function logMessage(message) {
            const log = document.getElementById('sync-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            log.appendChild(logEntry);
            log.scrollTop = log.scrollHeight;
        }

        // Clear log
        function clearLog() {
            document.getElementById('sync-log').innerHTML = '<div class="text-muted">Sync operations will be logged here...</div>';
        }
    </script>
</body>
</html>
