/*!
 * FormValidation (http://formvalidation.io)
 * The best jQuery plugin to validate form fields. Support Bootstrap, Foundation, Pure, SemanticUI, UIKit and custom frameworks
 *
 * @version     v0.6.2-dev, built on 2015-03-13 8:15:46 AM
 * <AUTHOR>
 * @copyright   (c) 2013 - 2015 <PERSON><PERSON><PERSON>
 * @license     http://formvalidation.io/license/
 */
!function(a){FormValidation.Framework.Semantic=function(b,c){c=a.extend(!0,{button:{selector:'[type="submit"]',disabled:"disabled"},control:{valid:"",invalid:""},err:{clazz:"ui red pointing label transition",parent:"^.*(field|column).*$"},icon:{valid:null,invalid:null,validating:null,feedback:"fv-control-feedback"},row:{selector:".field",valid:"fv-has-success",invalid:"error",feedback:"fv-has-feedback"}},c),FormValidation.Base.apply(this,[b,c])},FormValidation.Framework.Semantic.prototype=a.extend({},FormValidation.Base.prototype,{_fixIcon:function(a,b){var c=a.attr("type");if("checkbox"===c||"radio"===c){var d=a.parent();d.hasClass(c)&&b.insertAfter(d)}},_createTooltip:function(a,b,c){var d=a.data("fv.icon");if(d)switch(d.popup("exists")&&d.popup("remove popup").popup("destroy"),c){case"popover":d.css({cursor:"pointer"}).popup({content:b,position:"top center"});break;case"tooltip":default:d.css({cursor:"pointer"}).popup({content:b,position:"top center",variation:"inverted"})}},_destroyTooltip:function(a){var b=a.data("fv.icon");b&&b.popup("exists")&&b.css({cursor:""}).popup("remove popup").popup("destroy")},_hideTooltip:function(a){var b=a.data("fv.icon");b&&b.popup("hide")},_showTooltip:function(a){var b=a.data("fv.icon");b&&b.popup("show")}})}(jQuery);