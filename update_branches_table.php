<?php
// Update branches table to include branch URLs
include('proc/config.php');

echo "<h3>Updating Branches Table for External URLs</h3>";

// Add branch_url column if it doesn't exist
$add_column_sql = "ALTER TABLE tbl_branches ADD COLUMN branch_url VARCHAR(500) DEFAULT NULL";
$result = mysql_query($add_column_sql);

if($result) {
    echo "<p>✅ branch_url column added successfully!</p>";
} else {
    if(strpos(mysql_error(), 'Duplicate column name') !== false) {
        echo "<p>ℹ️ branch_url column already exists</p>";
    } else {
        echo "<p>❌ Error adding column: " . mysql_error() . "</p>";
    }
}

// Update sample branches with URLs
$update_branches = array(
    array(
        'branch_id' => 1,
        'branch_name' => 'Main Branch',
        'branch_code' => 'MAIN',
        'branch_url' => 'dashboard_enhanced.php' // Local URL
    ),
    array(
        'branch_id' => 2,
        'branch_name' => 'AJ Wagar Garden Branch',
        'branch_code' => 'AJWAGAR',
        'branch_url' => 'https://ajwagardenbwp.com/dashboard_enhanced.php'
    ),
    array(
        'branch_id' => 3,
        'branch_name' => 'Remote Branch 2',
        'branch_code' => 'REMOTE2',
        'branch_url' => 'https://branch2.example.com/dashboard_enhanced.php'
    )
);

echo "<h4>Updating Branch URLs:</h4>";

foreach($update_branches as $branch) {
    $update_sql = "INSERT INTO tbl_branches (branch_id, branch_name, branch_code, branch_url) 
                   VALUES ('{$branch['branch_id']}', '{$branch['branch_name']}', '{$branch['branch_code']}', '{$branch['branch_url']}')
                   ON DUPLICATE KEY UPDATE 
                   branch_name = VALUES(branch_name),
                   branch_code = VALUES(branch_code),
                   branch_url = VALUES(branch_url)";
    
    if(mysql_query($update_sql)) {
        echo "<p>✅ Updated: {$branch['branch_name']} → {$branch['branch_url']}</p>";
    } else {
        echo "<p>❌ Error updating {$branch['branch_name']}: " . mysql_error() . "</p>";
    }
}

// Show current branches
echo "<h4>Current Branches Configuration:</h4>";
$show_sql = "SELECT * FROM tbl_branches ORDER BY branch_id";
$show_result = mysql_query($show_sql);

if($show_result && mysql_num_rows($show_result) > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th>Branch ID</th><th>Branch Name</th><th>Branch Code</th><th>Branch URL</th>";
    echo "</tr>";
    
    while($row = mysql_fetch_assoc($show_result)) {
        echo "<tr>";
        echo "<td>" . $row['branch_id'] . "</td>";
        echo "<td>" . $row['branch_name'] . "</td>";
        echo "<td>" . $row['branch_code'] . "</td>";
        echo "<td>" . ($row['branch_url'] ?: 'Not set') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No branches found.</p>";
}

echo "<h4>Manual Branch URL Setup:</h4>";
if(isset($_POST['add_branch'])) {
    $branch_name = clean($_POST['branch_name']);
    $branch_code = clean($_POST['branch_code']);
    $branch_url = clean($_POST['branch_url']);
    
    $insert_sql = "INSERT INTO tbl_branches (branch_name, branch_code, branch_url) 
                   VALUES ('$branch_name', '$branch_code', '$branch_url')";
    
    if(mysql_query($insert_sql)) {
        echo "<p>✅ Branch added successfully!</p>";
        echo "<script>window.location.reload();</script>";
    } else {
        echo "<p>❌ Error adding branch: " . mysql_error() . "</p>";
    }
}
?>

<form method="POST" style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
    <h5>Add New Branch with URL:</h5>
    <div style="margin: 10px 0;">
        <label>Branch Name:</label><br>
        <input type="text" name="branch_name" required style="width: 300px; padding: 8px;" placeholder="e.g., Remote Branch Name">
    </div>
    <div style="margin: 10px 0;">
        <label>Branch Code:</label><br>
        <input type="text" name="branch_code" required style="width: 300px; padding: 8px;" placeholder="e.g., REMOTE1">
    </div>
    <div style="margin: 10px 0;">
        <label>Branch URL:</label><br>
        <input type="url" name="branch_url" required style="width: 500px; padding: 8px;" placeholder="https://yourdomain.com/dashboard_enhanced.php">
    </div>
    <button type="submit" name="add_branch" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px;">
        Add Branch
    </button>
</form>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { border-collapse: collapse; margin: 10px 0; }
th, td { padding: 8px; border: 1px solid #ccc; text-align: left; }
th { background: #f0f0f0; }
</style>
