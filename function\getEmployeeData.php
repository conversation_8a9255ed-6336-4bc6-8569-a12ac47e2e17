<?php
include('../proc/config.php');

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Log the request for debugging
error_log("getEmployeeData.php called with: " . print_r($_POST, true));

if(isset($_POST['employee_id']) && !empty($_POST['employee_id'])) {
    $employee_id = clean($_POST['employee_id']);

    $response = array();

    try {
        $sql = "SELECT p.*, b.branch_name FROM tbl_personnel p LEFT JOIN tbl_branches b ON p.branch_id = b.branch_id WHERE p.EntryID='$employee_id'";
        error_log("SQL Query: " . $sql);

        $result = mysql_query($sql);

        if(!$result) {
            $response['success'] = false;
            $response['message'] = 'Database query failed: ' . mysql_error();
        } else if(mysql_num_rows($result) > 0) {
            $employee = mysql_fetch_assoc($result);

            $response['success'] = true;
            $response['data'] = array(
                'EntryID' => $employee['EntryID'],
                'EmployeeID' => $employee['EmployeeID'],
                'AccessID' => $employee['AccessID'],
                'FullName' => $employee['FullName'],
                'DateHired' => $employee['DateHired'],
                'Position' => $employee['Position'],
                'address' => $employee['address'],
                'ProjectAssigned' => $employee['ProjectAssigned'],
                'ContactNo' => $employee['ContactNo'],
                'branch_id' => $employee['branch_id'],
                'TimeIN' => $employee['TimeIN'],
                'TimeOut' => $employee['TimeOut'],
                'schedule_dates' => $employee['schedule_dates'],
                'Status' => $employee['Status'],
                'Remarks' => $employee['Remarks']
            );
        } else {
            $response['success'] = false;
            $response['message'] = 'Employee not found with ID: ' . $employee_id;
        }

    } catch(Exception $e) {
        $response['success'] = false;
        $response['message'] = 'Error: ' . $e->getMessage();
    }

    header('Content-Type: application/json');
    echo json_encode($response);
} else {
    $response = array(
        'success' => false,
        'message' => 'Invalid request - employee_id parameter is missing or empty'
    );
    header('Content-Type: application/json');
    echo json_encode($response);
}
?>
