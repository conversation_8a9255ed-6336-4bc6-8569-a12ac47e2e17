<?php
session_start();
include('proc/config.php');

// Check if branches table exists
$branches_exist = false;
$branches = array();

$check_table = mysql_query("SHOW TABLES LIKE 'tbl_branches'");
if($check_table && mysql_num_rows($check_table) > 0) {
    $branches_exist = true;
    
    // Get all active branches
    $branches_sql = "SELECT * FROM tbl_branches WHERE branch_status = 'Active' ORDER BY branch_name";
    $branches_result = mysql_query($branches_sql);
    
    if($branches_result && mysql_num_rows($branches_result) > 0) {
        while($branch = mysql_fetch_assoc($branches_result)) {
            $branches[] = $branch;
        }
    }
}

// If no branches found, use default branches
if(empty($branches)) {
    $branches = array(
        array(
            'branch_id' => 1,
            'branch_name' => 'Ajwa Garden Multan',
            'branch_code' => 'AGM',
            'branch_location' => 'Multan',
            'branch_status' => 'Active'
        ),
        array(
            'branch_id' => 2,
            'branch_name' => 'Ajwa Garden Bahawalpur',
            'branch_code' => 'AGB',
            'branch_location' => 'Bahawalpur',
            'branch_status' => 'Active'
        ),
        array(
            'branch_id' => 3,
            'branch_name' => 'Ajwa Garden Sahiwal',
            'branch_code' => 'AGS',
            'branch_location' => 'Sahiwal',
            'branch_status' => 'Active'
        )
    );
}

// Handle branch selection
if(isset($_POST['select_branch'])) {
    $selected_branch_id = (int)$_POST['branch_id'];
    $selected_branch_url = clean($_POST['branch_url']);
    
    // Set branch in session
    $_SESSION['CURRENT_BRANCH_ID'] = $selected_branch_id;
    
    // Get branch details
    foreach($branches as $branch) {
        if($branch['branch_id'] == $selected_branch_id) {
            $_SESSION['CURRENT_BRANCH_NAME'] = $branch['branch_name'];
            $_SESSION['CURRENT_BRANCH_CODE'] = $branch['branch_code'];
            break;
        }
    }
    
    // Redirect to branch URL
    if(!empty($selected_branch_url)) {
        header("Location: " . $selected_branch_url);
        exit();
    } else {
        // Default redirect to dashboard
        header("Location: dashboard_enhanced.php");
        exit();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Select Branch - Biometric System</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .branch-selector-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .branch-selector-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 800px;
            width: 100%;
        }
        
        .header-section {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header-section h1 {
            color: #333;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .header-section p {
            color: #666;
            font-size: 1.1rem;
        }
        
        .branches-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .branch-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        
        .branch-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border-color: #667eea;
        }
        
        .branch-card.selected {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: #667eea;
        }
        
        .branch-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            color: #667eea;
        }
        
        .branch-card.selected .branch-icon {
            color: white;
        }
        
        .branch-name {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .branch-location {
            font-size: 1rem;
            opacity: 0.8;
            margin-bottom: 15px;
        }
        
        .branch-code {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .branch-card.selected .branch-code {
            background: rgba(255,255,255,0.2);
            color: white;
        }
        
        .url-input-section {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .action-buttons {
            text-align: center;
            margin-top: 30px;
        }
        
        .btn-select {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-select:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
        }
        
        .btn-select:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .quick-links {
            margin-top: 20px;
            text-align: center;
        }
        
        .quick-links a {
            color: #667eea;
            text-decoration: none;
            margin: 0 15px;
            font-weight: 500;
        }
        
        .quick-links a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="branch-selector-container">
        <div class="branch-selector-card">
            <div class="header-section">
                <h1><i class="fa fa-building"></i> Select Your Branch</h1>
                <p>Choose your branch location to access the biometric system</p>
            </div>
            
            <form method="POST" id="branchForm">
                <div class="branches-grid">
                    <?php foreach($branches as $branch): ?>
                    <div class="branch-card" onclick="selectBranch(<?php echo $branch['branch_id']; ?>)">
                        <div class="branch-icon">
                            <i class="fa fa-building"></i>
                        </div>
                        <div class="branch-name"><?php echo htmlspecialchars($branch['branch_name']); ?></div>
                        <div class="branch-location">
                            <i class="fa fa-map-marker-alt"></i> <?php echo htmlspecialchars($branch['branch_location']); ?>
                        </div>
                        <div class="branch-code"><?php echo htmlspecialchars($branch['branch_code']); ?></div>
                        <input type="radio" name="branch_id" value="<?php echo $branch['branch_id']; ?>" style="display: none;">
                    </div>
                    <?php endforeach; ?>
                </div>
                
                <div class="url-input-section">
                    <label for="branch_url" class="form-label">
                        <i class="fa fa-link"></i> Branch URL (Optional)
                    </label>
                    <input type="url" 
                           class="form-control" 
                           id="branch_url" 
                           name="branch_url" 
                           placeholder="https://example.com/dashboard_enhanced.php"
                           style="border-radius: 10px; padding: 12px 15px;">
                    <small class="form-text text-muted">
                        Leave empty to use default dashboard, or enter specific branch URL
                    </small>
                </div>
                
                <div class="action-buttons">
                    <button type="submit" name="select_branch" class="btn btn-select" id="selectBtn" disabled>
                        <i class="fa fa-arrow-right"></i> Access Selected Branch
                    </button>
                </div>
                
                <div class="quick-links">
                    <a href="dashboard_enhanced.php">
                        <i class="fa fa-tachometer-alt"></i> Default Dashboard
                    </a>
                    <a href="index.php">
                        <i class="fa fa-sign-in-alt"></i> Login Page
                    </a>
                    <?php if(isset($_SESSION['SESS_USER_ID']) && $_SESSION['ACCESSLEVEL'] == 'Admin'): ?>
                    <a href="branch_management.php">
                        <i class="fa fa-cogs"></i> Branch Management
                    </a>
                    <?php endif; ?>
                </div>
            </form>
        </div>
    </div>
    
    <!-- jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let selectedBranchId = null;
        
        function selectBranch(branchId) {
            // Remove previous selection
            $('.branch-card').removeClass('selected');
            $('input[name="branch_id"]').prop('checked', false);
            
            // Select current branch
            $('input[name="branch_id"][value="' + branchId + '"]').prop('checked', true);
            $('input[name="branch_id"][value="' + branchId + '"]').closest('.branch-card').addClass('selected');
            
            selectedBranchId = branchId;
            
            // Enable submit button
            $('#selectBtn').prop('disabled', false);
            
            console.log('Selected branch ID:', branchId);
        }
        
        // Handle form submission
        $('#branchForm').on('submit', function(e) {
            if(!selectedBranchId) {
                e.preventDefault();
                alert('Please select a branch first');
                return false;
            }
            
            // Show loading state
            $('#selectBtn').html('<i class="fa fa-spinner fa-spin"></i> Redirecting...').prop('disabled', true);
        });
        
        // Auto-fill common URLs based on branch selection
        $('input[name="branch_id"]').on('change', function() {
            const branchId = $(this).val();
            const urlInput = $('#branch_url');
            
            // You can customize these URLs based on your branch setup
            const branchUrls = {
                '1': 'https://ajwagardenmultan.com/dashboard_enhanced.php',
                '2': 'https://ajwagardenbahawalpur.com/dashboard_enhanced.php', 
                '3': 'https://ajwagardensahiwal.com/dashboard_enhanced.php'
            };
            
            // Uncomment the line below if you want to auto-fill URLs
            // urlInput.val(branchUrls[branchId] || '');
        });
    </script>
</body>
</html>
