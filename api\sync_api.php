<?php
/**
 * Database Sync API for Online Server
 * This file should be uploaded to your online hosting server
 * Provides secure API endpoints for data synchronization
 */

// Security headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-API-Key');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Configuration
require_once('../proc/config.php');

// API Configuration
define('API_KEY', 'your-secure-api-key-here'); // Change this to a secure random key
define('API_VERSION', '1.0');
define('MAX_RECORDS_PER_REQUEST', 1000);

// Authentication
function authenticateRequest() {
    $headers = getallheaders();
    $apiKey = isset($headers['X-API-Key']) ? $headers['X-API-Key'] : 
              (isset($_GET['api_key']) ? $_GET['api_key'] : '');
    
    if ($apiKey !== API_KEY) {
        sendResponse(401, 'error', 'Invalid API key');
        exit();
    }
}

// Send JSON response
function sendResponse($code, $status, $message, $data = null) {
    http_response_code($code);
    $response = [
        'status' => $status,
        'message' => $message,
        'timestamp' => date('Y-m-d H:i:s'),
        'version' => API_VERSION
    ];
    
    if ($data !== null) {
        $response['data'] = $data;
    }
    
    echo json_encode($response);
}

// Get request method and endpoint
$method = $_SERVER['REQUEST_METHOD'];
$endpoint = isset($_GET['endpoint']) ? $_GET['endpoint'] : '';

// Authenticate all requests
authenticateRequest();

// Route requests
switch ($method) {
    case 'GET':
        handleGetRequest($endpoint);
        break;
    case 'POST':
        handlePostRequest($endpoint);
        break;
    case 'PUT':
        handlePutRequest($endpoint);
        break;
    case 'DELETE':
        handleDeleteRequest($endpoint);
        break;
    default:
        sendResponse(405, 'error', 'Method not allowed');
}

// Handle GET requests
function handleGetRequest($endpoint) {
    switch ($endpoint) {
        case 'test':
            sendResponse(200, 'success', 'API is working', ['server_time' => date('Y-m-d H:i:s')]);
            break;
            
        case 'employees':
            getEmployees();
            break;
            
        case 'attendance':
            getAttendance();
            break;
            
        case 'stats':
            getStats();
            break;
            
        default:
            sendResponse(404, 'error', 'Endpoint not found');
    }
}

// Handle POST requests
function handlePostRequest($endpoint) {
    switch ($endpoint) {
        case 'employees':
            createEmployees();
            break;
            
        case 'attendance':
            createAttendance();
            break;
            
        case 'sync':
            handleSync();
            break;
            
        default:
            sendResponse(404, 'error', 'Endpoint not found');
    }
}

// Handle PUT requests
function handlePutRequest($endpoint) {
    switch ($endpoint) {
        case 'employees':
            updateEmployees();
            break;
            
        case 'attendance':
            updateAttendance();
            break;
            
        default:
            sendResponse(404, 'error', 'Endpoint not found');
    }
}

// Handle DELETE requests
function handleDeleteRequest($endpoint) {
    switch ($endpoint) {
        case 'employees':
            deleteEmployees();
            break;
            
        case 'attendance':
            deleteAttendance();
            break;
            
        default:
            sendResponse(404, 'error', 'Endpoint not found');
    }
}

// Get employees
function getEmployees() {
    $limit = isset($_GET['limit']) ? min(intval($_GET['limit']), MAX_RECORDS_PER_REQUEST) : 100;
    $offset = isset($_GET['offset']) ? intval($_GET['offset']) : 0;
    $branch = isset($_GET['branch']) ? mysql_real_escape_string($_GET['branch']) : '';
    
    $where_clause = '';
    if ($branch) {
        $where_clause = "WHERE AgencyCompany = '$branch'";
    }
    
    $sql = "SELECT * FROM tbl_personnel $where_clause ORDER BY AccessID LIMIT $offset, $limit";
    $result = mysql_query($sql);
    
    if (!$result) {
        sendResponse(500, 'error', 'Database query failed: ' . mysql_error());
        return;
    }
    
    $employees = [];
    while ($row = mysql_fetch_assoc($result)) {
        $employees[] = $row;
    }
    
    // Get total count
    $count_sql = "SELECT COUNT(*) as total FROM tbl_personnel $where_clause";
    $count_result = mysql_query($count_sql);
    $total = 0;
    if ($count_result) {
        $count_row = mysql_fetch_assoc($count_result);
        $total = $count_row['total'];
    }
    
    sendResponse(200, 'success', 'Employees retrieved', [
        'employees' => $employees,
        'total' => $total,
        'limit' => $limit,
        'offset' => $offset
    ]);
}

// Get attendance records
function getAttendance() {
    $limit = isset($_GET['limit']) ? min(intval($_GET['limit']), MAX_RECORDS_PER_REQUEST) : 100;
    $offset = isset($_GET['offset']) ? intval($_GET['offset']) : 0;
    $date_from = isset($_GET['date_from']) ? mysql_real_escape_string($_GET['date_from']) : date('Y-m-d', strtotime('-30 days'));
    $date_to = isset($_GET['date_to']) ? mysql_real_escape_string($_GET['date_to']) : date('Y-m-d');
    $access_id = isset($_GET['access_id']) ? mysql_real_escape_string($_GET['access_id']) : '';
    
    $where_clause = "WHERE DATE(TimeRecord) BETWEEN '$date_from' AND '$date_to'";
    if ($access_id) {
        $where_clause .= " AND AccessID = '$access_id'";
    }
    
    $sql = "SELECT * FROM tbl_in_out $where_clause ORDER BY TimeRecord DESC LIMIT $offset, $limit";
    $result = mysql_query($sql);
    
    if (!$result) {
        sendResponse(500, 'error', 'Database query failed: ' . mysql_error());
        return;
    }
    
    $attendance = [];
    while ($row = mysql_fetch_assoc($result)) {
        $attendance[] = $row;
    }
    
    // Get total count
    $count_sql = "SELECT COUNT(*) as total FROM tbl_in_out $where_clause";
    $count_result = mysql_query($count_sql);
    $total = 0;
    if ($count_result) {
        $count_row = mysql_fetch_assoc($count_result);
        $total = $count_row['total'];
    }
    
    sendResponse(200, 'success', 'Attendance records retrieved', [
        'attendance' => $attendance,
        'total' => $total,
        'limit' => $limit,
        'offset' => $offset,
        'date_from' => $date_from,
        'date_to' => $date_to
    ]);
}

// Get database statistics
function getStats() {
    $stats = [];
    
    // Employee count
    $emp_result = mysql_query("SELECT COUNT(*) as count FROM tbl_personnel");
    if ($emp_result) {
        $emp_row = mysql_fetch_assoc($emp_result);
        $stats['employees'] = $emp_row['count'];
    }
    
    // Attendance count
    $att_result = mysql_query("SELECT COUNT(*) as count FROM tbl_in_out");
    if ($att_result) {
        $att_row = mysql_fetch_assoc($att_result);
        $stats['attendance'] = $att_row['count'];
    }
    
    // Recent attendance count (last 30 days)
    $recent_result = mysql_query("SELECT COUNT(*) as count FROM tbl_in_out WHERE DATE(TimeRecord) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)");
    if ($recent_result) {
        $recent_row = mysql_fetch_assoc($recent_result);
        $stats['recent_attendance'] = $recent_row['count'];
    }
    
    // Branch count
    $branch_result = mysql_query("SELECT COUNT(DISTINCT AgencyCompany) as count FROM tbl_personnel WHERE AgencyCompany IS NOT NULL AND AgencyCompany != ''");
    if ($branch_result) {
        $branch_row = mysql_fetch_assoc($branch_result);
        $stats['branches'] = $branch_row['count'];
    }
    
    sendResponse(200, 'success', 'Statistics retrieved', $stats);
}

// Create employees
function createEmployees() {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['employees'])) {
        sendResponse(400, 'error', 'Invalid input data');
        return;
    }
    
    $employees = $input['employees'];
    $created = 0;
    $updated = 0;
    $errors = 0;
    
    foreach ($employees as $employee) {
        if (!isset($employee['AccessID']) || !$employee['AccessID']) {
            $errors++;
            continue;
        }
        
        $access_id = mysql_real_escape_string($employee['AccessID']);
        $employee_id = mysql_real_escape_string($employee['EmployeeID'] ?? '');
        $full_name = mysql_real_escape_string($employee['FullName'] ?? '');
        $position = mysql_real_escape_string($employee['Position'] ?? '');
        $agency = mysql_real_escape_string($employee['AgencyCompany'] ?? '');
        $contact = mysql_real_escape_string($employee['ContactNo'] ?? '');
        $date_hired = mysql_real_escape_string($employee['DateHired'] ?? '');
        $status = mysql_real_escape_string($employee['Status'] ?? '');
        $time_in = mysql_real_escape_string($employee['TimeIN'] ?? '');
        $time_out = mysql_real_escape_string($employee['TimeOut'] ?? '');
        
        // Check if employee exists
        $check_sql = "SELECT COUNT(*) as count FROM tbl_personnel WHERE AccessID = '$access_id'";
        $check_result = mysql_query($check_sql);
        $exists = false;
        
        if ($check_result) {
            $check_row = mysql_fetch_assoc($check_result);
            $exists = $check_row['count'] > 0;
        }
        
        if ($exists) {
            // Update existing employee
            $update_sql = "UPDATE tbl_personnel SET 
                EmployeeID = '$employee_id',
                FullName = '$full_name',
                Position = '$position',
                AgencyCompany = '$agency',
                ContactNo = '$contact',
                DateHired = '$date_hired',
                Status = '$status',
                TimeIN = '$time_in',
                TimeOut = '$time_out'
                WHERE AccessID = '$access_id'";
            
            if (mysql_query($update_sql)) {
                $updated++;
            } else {
                $errors++;
            }
        } else {
            // Insert new employee
            $insert_sql = "INSERT INTO tbl_personnel (EmployeeID, AccessID, FullName, Position, AgencyCompany, ContactNo, DateHired, Status, TimeIN, TimeOut) 
                VALUES ('$employee_id', '$access_id', '$full_name', '$position', '$agency', '$contact', '$date_hired', '$status', '$time_in', '$time_out')";
            
            if (mysql_query($insert_sql)) {
                $created++;
            } else {
                $errors++;
            }
        }
    }
    
    sendResponse(200, 'success', 'Employee sync completed', [
        'created' => $created,
        'updated' => $updated,
        'errors' => $errors,
        'total_processed' => count($employees)
    ]);
}

// Create attendance records
function createAttendance() {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['attendance'])) {
        sendResponse(400, 'error', 'Invalid input data');
        return;
    }
    
    $attendance_records = $input['attendance'];
    $created = 0;
    $skipped = 0;
    $errors = 0;
    
    foreach ($attendance_records as $record) {
        if (!isset($record['AccessID']) || !isset($record['TimeRecord']) || !isset($record['TimeFlag'])) {
            $errors++;
            continue;
        }
        
        $access_id = mysql_real_escape_string($record['AccessID']);
        $full_name = mysql_real_escape_string($record['FullName'] ?? '');
        $time_record = mysql_real_escape_string($record['TimeRecord']);
        $time_flag = mysql_real_escape_string($record['TimeFlag']);
        $access_area = mysql_real_escape_string($record['AccessArea'] ?? '');
        
        // Check if record exists (prevent duplicates)
        $check_sql = "SELECT COUNT(*) as count FROM tbl_in_out WHERE 
            AccessID = '$access_id' AND 
            TimeRecord = '$time_record' AND 
            TimeFlag = '$time_flag'";
        $check_result = mysql_query($check_sql);
        $exists = false;
        
        if ($check_result) {
            $check_row = mysql_fetch_assoc($check_result);
            $exists = $check_row['count'] > 0;
        }
        
        if (!$exists) {
            $insert_sql = "INSERT INTO tbl_in_out (AccessID, FullName, TimeRecord, TimeFlag, AccessArea) 
                VALUES ('$access_id', '$full_name', '$time_record', '$time_flag', '$access_area')";
            
            if (mysql_query($insert_sql)) {
                $created++;
            } else {
                $errors++;
            }
        } else {
            $skipped++;
        }
    }
    
    sendResponse(200, 'success', 'Attendance sync completed', [
        'created' => $created,
        'skipped' => $skipped,
        'errors' => $errors,
        'total_processed' => count($attendance_records)
    ]);
}

// Handle sync operations
function handleSync() {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['operation'])) {
        sendResponse(400, 'error', 'Invalid sync operation');
        return;
    }
    
    $operation = $input['operation'];
    
    switch ($operation) {
        case 'full_stats':
            getStats();
            break;
            
        case 'backup':
            createBackup();
            break;
            
        case 'restore':
            restoreBackup($input);
            break;
            
        default:
            sendResponse(400, 'error', 'Unknown sync operation');
    }
}

// Create backup
function createBackup() {
    $backup_data = [
        'timestamp' => date('Y-m-d H:i:s'),
        'employees' => [],
        'attendance' => []
    ];
    
    // Backup employees
    $emp_result = mysql_query("SELECT * FROM tbl_personnel ORDER BY AccessID");
    if ($emp_result) {
        while ($row = mysql_fetch_assoc($emp_result)) {
            $backup_data['employees'][] = $row;
        }
    }
    
    // Backup recent attendance (last 90 days)
    $att_result = mysql_query("SELECT * FROM tbl_in_out WHERE DATE(TimeRecord) >= DATE_SUB(CURDATE(), INTERVAL 90 DAY) ORDER BY TimeRecord DESC");
    if ($att_result) {
        while ($row = mysql_fetch_assoc($att_result)) {
            $backup_data['attendance'][] = $row;
        }
    }
    
    sendResponse(200, 'success', 'Backup created', $backup_data);
}

?>
