<?php
  session_start();
  $_SESSION['LAST_ACTIVITY'] = time();

  if(@(isset($_SESSION['SESS_USER_ID']) || !(trim($_SESSION['SESS_USER_ID']) == ''))){
    header("location: dashboard_enhanced.php");
  }
?>
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="Biometrics Attendance Management System">
    <meta name="author" content="Biometrics Attendance System">
    <title>Biometrics Attendance System - Login</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Animate.css -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
  </head>

  <body>
    <!-- Background with gradient overlay -->
    <div class="login-background">
      <div class="gradient-overlay"></div>
    </div>

    <!-- Main Container -->
    <div class="container-fluid vh-100 d-flex align-items-center justify-content-center">
      <div class="row w-100">

        <!-- Left Side - Branding -->
        <div class="col-lg-7 d-none d-lg-flex align-items-center justify-content-center">
          <div class="branding-section animate__animated animate__fadeInLeft">
            <div class="brand-logo mb-4">
              <i class="fas fa-fingerprint fa-5x text-white mb-3"></i>
              <h1 class="brand-title">Biometrics Attendance System</h1>
              <p class="brand-subtitle">Advanced Attendance Management System</p>
            </div>

            <div class="features-list">
              <div class="feature-item">
                <i class="fas fa-shield-alt"></i>
                <span>Secure Authentication</span>
              </div>
              <div class="feature-item">
                <i class="fas fa-clock"></i>
                <span>Real-time Tracking</span>
              </div>
              <div class="feature-item">
                <i class="fas fa-chart-line"></i>
                <span>Advanced Analytics</span>
              </div>
              <div class="feature-item">
                <i class="fas fa-mobile-alt"></i>
                <span>Mobile Responsive</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Right Side - Login Form -->
        <div class="col-lg-5 d-flex align-items-center justify-content-center">
          <div class="login-card animate__animated animate__fadeInRight">

            <!-- Mobile Logo -->
            <div class="mobile-logo d-lg-none text-center mb-4">
              <i class="fas fa-fingerprint fa-3x text-primary mb-2"></i>
              <h3 class="text-dark">Biometrics Attendance System</h3>
            </div>

            <div class="card-header text-center">
              <h2 class="login-title">Welcome Back</h2>
              <p class="login-subtitle">Sign in to your account</p>
            </div>

            <div class="card-body">
              <form class="login-form form-signin" method="post" role="form">

                <!-- Alert Message -->
                <div id="page-alert" class="alert alert-danger d-none" role="alert">
                  <i class="fas fa-exclamation-triangle me-2"></i>
                  <span id="page-alert-message"></span>
                </div>

                <!-- Username Field -->
                <div class="form-floating mb-3">
                  <input type="text" class="form-control modern-input" id="username" name="username" placeholder="Username" autofocus required>
                  <label for="username"><i class="fas fa-user me-2"></i>Username</label>
                  <div class="input-feedback">
                    <span class="span-user-error text-danger"></span>
                  </div>
                </div>

                <!-- Password Field -->
                <div class="form-floating mb-3">
                  <input type="password" class="form-control modern-input" id="password" name="password" placeholder="Password" required>
                  <label for="password"><i class="fas fa-lock me-2"></i>Password</label>
                  <div class="input-feedback">
                    <span class="span-password-error text-danger"></span>
                  </div>
                </div>

                <!-- Branch Selection -->
                <div class="form-floating mb-4">
                  <select class="form-control modern-input" id="branch" name="branch_id" required>
                    <option value="">Select Branch</option>
                    <?php
                      include('proc/config.php');

                      // Check if branches table exists
                      $check_branches_table = mysql_query("SHOW TABLES LIKE 'tbl_branches'");
                      if($check_branches_table && mysql_num_rows($check_branches_table) > 0) {
                        // Get all branches
                        $branches_sql = "SELECT * FROM tbl_branches ORDER BY branch_name";
                        $branches_result = mysql_query($branches_sql);
                        if($branches_result && mysql_num_rows($branches_result) > 0) {
                          while($branch = mysql_fetch_assoc($branches_result)) {
                            echo '<option value="' . $branch['branch_id'] . '">' . $branch['branch_name'] . '</option>';
                          }
                        } else {
                          echo '<option value="1">Main Branch</option>';
                        }
                      } else {
                        echo '<option value="1">Main Branch</option>';
                      }
                    ?>
                  </select>
                  <label for="branch"><i class="fas fa-building me-2"></i>Select Branch</label>
                  <div class="input-feedback">
                    <span class="span-branch-error text-danger"></span>
                  </div>
                </div>

                <!-- Remember Me -->
                <div class="form-check mb-4">
                  <input class="form-check-input" type="checkbox" id="rememberMe">
                  <label class="form-check-label" for="rememberMe">
                    <i class="fas fa-user-check me-1"></i>
                    Remember me
                  </label>
                  <div class="remember-options mt-2" id="rememberOptions" style="display: none;">
                    <small class="text-muted">
                      <i class="fas fa-info-circle me-1"></i>
                      Your credentials will be saved securely for 30 days
                    </small>
                    <br>
                    <button type="button" class="btn btn-link btn-sm p-0 mt-1 text-danger" onclick="clearAllRememberedData()">
                      <i class="fas fa-trash-alt me-1"></i>Clear saved data
                    </button>
                  </div>
                </div>

                <!-- Sign In Button -->
                <button class="btn btn-primary btn-lg w-100 modern-btn form-btn" type="button">
                  <i class="fas fa-sign-in-alt me-2"></i>
                  Sign In
                  <div class="btn-loader d-none">
                    <i class="fas fa-spinner fa-spin"></i>
                  </div>
                </button>

              </form>
            </div>

            <div class="card-footer text-center">
              <p class="help-text">
                <i class="fas fa-info-circle me-1"></i>
                Don't have an account? Please contact your administrator.
              </p>

              <!-- Quick Links -->
              <div class="quick-links mt-3">
                <a href="#" class="quick-link" data-bs-toggle="modal" data-bs-target="#helpModal">
                  <i class="fas fa-question-circle"></i> Help
                </a>
                <span class="divider">|</span>
                <a href="#" class="quick-link" data-bs-toggle="modal" data-bs-target="#aboutModal">
                  <i class="fas fa-info"></i> About
                </a>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <footer class="login-footer">
      <div class="container-fluid">
        <div class="row">
          <div class="col-12 text-center">
            <p class="footer-text">
              Made with <i class="fas fa-heart text-danger heartbeat"></i> by
              <span class="developer-name">Saqib Mahmood</span>
              <span class="contact-info">
                <i class="fas fa-phone-alt"></i> +92 3041704232
              </span>
            </p>
          </div>
        </div>
      </div>
    </footer>

    <!-- Help Modal -->
    <div class="modal fade" id="helpModal" tabindex="-1" aria-labelledby="helpModalLabel" aria-hidden="true">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="helpModalLabel"><i class="fas fa-question-circle me-2"></i>Need Help?</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <h6><i class="fas fa-user me-2"></i>Login Issues:</h6>
            <ul>
              <li>Ensure your username and password are correct</li>
              <li>Check if Caps Lock is enabled</li>
              <li>Contact your administrator if you forgot your credentials</li>
            </ul>

            <h6 class="mt-3"><i class="fas fa-phone me-2"></i>Support Contact:</h6>
            <p>Email: <EMAIL><br>
               Phone: +92-304-1704232</p>
          </div>
        </div>
      </div>
    </div>

    <!-- About Modal -->
    <div class="modal fade" id="aboutModal" tabindex="-1" aria-labelledby="aboutModalLabel" aria-hidden="true">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="aboutModalLabel"><i class="fas fa-info me-2"></i>About Biometrics Attendance System</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <div class="text-center mb-3">
              <i class="fas fa-fingerprint fa-3x text-primary mb-2"></i>
              <h5>Biometrics Attendance System</h5>
              <p class="text-muted">Version 2.0</p>
            </div>

            <p>Advanced biometric attendance management system designed for modern workplaces. Features include real-time tracking, comprehensive reporting, and secure authentication.</p>

            <div class="row text-center mt-3">
              <div class="col-4">
                <i class="fas fa-shield-alt fa-2x text-success mb-2"></i>
                <p><small>Secure</small></p>
              </div>
              <div class="col-4">
                <i class="fas fa-tachometer-alt fa-2x text-info mb-2"></i>
                <p><small>Fast</small></p>
              </div>
              <div class="col-4">
                <i class="fas fa-chart-line fa-2x text-warning mb-2"></i>
                <p><small>Analytics</small></p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Custom CSS -->
    <style>
      :root {
        --primary-color: #4f46e5;
        --primary-dark: #3730a3;
        --secondary-color: #6366f1;
        --success-color: #10b981;
        --warning-color: #f59e0b;
        --danger-color: #ef4444;
        --dark-color: #1f2937;
        --light-color: #f8fafc;
        --border-radius: 12px;
        --box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }

      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Inter', sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        overflow-x: hidden;
      }

      .login-background {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        z-index: -2;
      }

      .gradient-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(45deg, rgba(79, 70, 229, 0.8), rgba(99, 102, 241, 0.6));
        z-index: -1;
      }

      /* Branding Section */
      .branding-section {
        text-align: center;
        color: white;
        padding: 2rem;
      }

      .brand-title {
        font-size: 3.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
        text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
      }

      .brand-subtitle {
        font-size: 1.2rem;
        font-weight: 300;
        opacity: 0.9;
        margin-bottom: 3rem;
      }

      .features-list {
        display: grid;
        gap: 1.5rem;
        max-width: 400px;
        margin: 0 auto;
      }

      .feature-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.1);
        border-radius: var(--border-radius);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: var(--transition);
      }

      .feature-item:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: translateY(-2px);
      }

      .feature-item i {
        font-size: 1.5rem;
        color: #fbbf24;
      }

      /* Login Card */
      .login-card {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
        padding: 2rem;
        width: 100%;
        max-width: 450px;
        margin: 1rem;
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      .card-header {
        margin-bottom: 2rem;
      }

      .login-title {
        font-size: 2rem;
        font-weight: 600;
        color: var(--dark-color);
        margin-bottom: 0.5rem;
      }

      .login-subtitle {
        color: #6b7280;
        font-weight: 400;
        margin-bottom: 0;
      }

      /* Modern Form Inputs */
      .modern-input {
        border: 2px solid #e5e7eb;
        border-radius: var(--border-radius);
        padding: 1rem 1rem 1rem 3rem;
        font-size: 1rem;
        transition: var(--transition);
        background: #f9fafb;
      }

      .modern-input:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        background: white;
      }

      .form-floating > label {
        padding-left: 3rem;
        color: #6b7280;
        font-weight: 500;
      }

      .form-floating > .modern-input:focus ~ label,
      .form-floating > .modern-input:not(:placeholder-shown) ~ label {
        color: var(--primary-color);
        transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
      }

      /* Modern Button */
      .modern-btn {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        border: none;
        border-radius: var(--border-radius);
        padding: 1rem 2rem;
        font-weight: 600;
        font-size: 1rem;
        transition: var(--transition);
        position: relative;
        overflow: hidden;
      }

      .modern-btn:hover {
        background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(79, 70, 229, 0.3);
      }

      .modern-btn:active {
        transform: translateY(0);
      }

      .btn-loader {
        position: absolute;
        right: 1rem;
        top: 50%;
        transform: translateY(-50%);
      }

      /* Form Check */
      .form-check-input:checked {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
      }

      .form-check-label {
        color: #6b7280;
        font-weight: 500;
      }

      /* Card Footer */
      .card-footer {
        border-top: 1px solid #e5e7eb;
        padding-top: 1.5rem;
        margin-top: 1.5rem;
      }

      .help-text {
        color: #6b7280;
        font-size: 0.9rem;
        margin-bottom: 0;
      }

      .quick-links {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 0.5rem;
      }

      .quick-link {
        color: var(--primary-color);
        text-decoration: none;
        font-size: 0.9rem;
        font-weight: 500;
        transition: var(--transition);
      }

      .quick-link:hover {
        color: var(--primary-dark);
        text-decoration: underline;
      }

      .divider {
        color: #d1d5db;
      }

      /* Mobile Logo */
      .mobile-logo h3 {
        font-weight: 600;
      }

      /* Alert Styling */
      .alert {
        border-radius: var(--border-radius);
        border: none;
        font-weight: 500;
      }

      .alert-danger {
        background: linear-gradient(135deg, #fef2f2, #fee2e2);
        color: var(--danger-color);
      }

      .alert-success {
        background: linear-gradient(135deg, #f0fdf4, #dcfce7);
        color: var(--success-color);
        border: 1px solid #bbf7d0;
      }

      /* Remember Me Notification */
      .remember-notification {
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
        padding: 15px 20px;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
        z-index: 9999;
        max-width: 350px;
        font-size: 14px;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: space-between;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      .btn-close-notification {
        background: none;
        border: none;
        color: white;
        font-size: 16px;
        cursor: pointer;
        padding: 0;
        margin-left: 15px;
        opacity: 0.8;
        transition: var(--transition);
      }

      .btn-close-notification:hover {
        opacity: 1;
        transform: scale(1.1);
      }

      /* Remember Me Checkbox Styling */
      .form-check {
        position: relative;
      }

      .form-check-input {
        width: 18px;
        height: 18px;
        border: 2px solid #d1d5db;
        border-radius: 4px;
        transition: var(--transition);
      }

      .form-check-input:checked {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='white'%3e%3cpath fill-rule='evenodd' d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z' clip-rule='evenodd'/%3e%3c/svg%3e");
      }

      .form-check-input:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
      }

      .form-check-label {
        margin-left: 8px;
        font-weight: 500;
        cursor: pointer;
        user-select: none;
      }

      /* Add a subtle animation for remembered credentials */
      .credentials-loaded {
        animation: credentialsGlow 2s ease-in-out;
      }

      @keyframes credentialsGlow {
        0% { box-shadow: 0 0 5px rgba(79, 70, 229, 0.3); }
        50% { box-shadow: 0 0 20px rgba(79, 70, 229, 0.6); }
        100% { box-shadow: 0 0 5px rgba(79, 70, 229, 0.3); }
      }

      /* Input Feedback */
      .input-feedback {
        margin-top: 0.25rem;
        min-height: 1.2rem;
      }

      /* Modal Styling */
      .modal-content {
        border-radius: var(--border-radius);
        border: none;
        box-shadow: var(--box-shadow);
      }

      .modal-header {
        border-bottom: 1px solid #e5e7eb;
        padding: 1.5rem;
      }

      .modal-title {
        font-weight: 600;
        color: var(--dark-color);
      }

      /* Responsive Design */
      @media (max-width: 768px) {
        .brand-title {
          font-size: 2.5rem;
        }

        .login-card {
          margin: 0.5rem;
          padding: 1.5rem;
        }

        .features-list {
          display: none;
        }
      }

      /* Animation Delays */
      .animate__fadeInLeft {
        animation-delay: 0.2s;
      }

      .animate__fadeInRight {
        animation-delay: 0.4s;
      }

      /* Loading Animation */
      @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
      }

      .loading {
        animation: pulse 1.5s infinite;
      }

      /* Hover Effects */
      .login-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
      }

      /* Focus States */
      .btn:focus,
      .form-control:focus {
        outline: none;
      }

      /* Footer Styling */
      .login-footer {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-top: 1px solid rgba(255, 255, 255, 0.2);
        padding: 15px 0;
        z-index: 1000;
      }

      .footer-text {
        color: rgba(255, 255, 255, 0.9);
        font-size: 14px;
        font-weight: 400;
        margin: 0;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
      }

      .developer-name {
        font-weight: 600;
        color: #fbbf24;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        transition: var(--transition);
      }

      .developer-name:hover {
        color: #f59e0b;
        text-shadow: 0 0 8px rgba(251, 191, 36, 0.6);
      }

      .contact-info {
        display: inline-block;
        margin-left: 15px;
        padding-left: 15px;
        border-left: 1px solid rgba(255, 255, 255, 0.3);
        color: rgba(255, 255, 255, 0.8);
        font-size: 13px;
        font-weight: 500;
        transition: var(--transition);
      }

      .contact-info:hover {
        color: #10b981;
        text-shadow: 0 0 6px rgba(16, 185, 129, 0.5);
      }

      .contact-info i {
        margin-right: 5px;
        font-size: 12px;
      }

      .heartbeat {
        animation: heartbeat 1.5s ease-in-out infinite;
        display: inline-block;
        margin: 0 4px;
      }

      @keyframes heartbeat {
        0% { transform: scale(1); }
        25% { transform: scale(1.1); }
        50% { transform: scale(1); }
        75% { transform: scale(1.05); }
        100% { transform: scale(1); }
      }

      /* Adjust main container to account for footer */
      .container-fluid.vh-100 {
        padding-bottom: 60px;
      }

      /* Custom Scrollbar */
      ::-webkit-scrollbar {
        width: 8px;
      }

      ::-webkit-scrollbar-track {
        background: #f1f1f1;
      }

      ::-webkit-scrollbar-thumb {
        background: var(--primary-color);
        border-radius: 4px;
      }

      ::-webkit-scrollbar-thumb:hover {
        background: var(--primary-dark);
      }

      /* Mobile Footer Adjustments */
      @media (max-width: 768px) {
        .footer-text {
          font-size: 12px;
          line-height: 1.4;
        }

        .login-footer {
          padding: 12px 0;
        }

        .container-fluid.vh-100 {
          padding-bottom: 50px;
        }

        .contact-info {
          display: block;
          margin-left: 0;
          margin-top: 5px;
          padding-left: 0;
          border-left: none;
          font-size: 11px;
        }

        .developer-name {
          display: block;
        }
      }

      /* Very small screens */
      @media (max-width: 480px) {
        .footer-text {
          font-size: 11px;
          padding: 0 10px;
        }

        .contact-info {
          font-size: 10px;
        }
      }
    </style>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="js/access.js"></script>

    <script>
      // Modern login enhancements
      $(document).ready(function() {
        // Hide alert initially - use hide() for compatibility with access.js
        $('#page-alert').hide().removeClass('d-none');

        // Load remembered credentials
        loadRememberedCredentials();

        // Remember Me checkbox toggle
        $('#rememberMe').on('change', function() {
          if ($(this).is(':checked')) {
            $('#rememberOptions').slideDown(300);
          } else {
            $('#rememberOptions').slideUp(300);
            clearRememberedCredentials();
          }
        });

        // Enhanced input focus effects
        $('.modern-input').on('focus', function() {
          $(this).parent().addClass('focused');
        }).on('blur', function() {
          $(this).parent().removeClass('focused');
        });

        // Keyboard shortcuts
        $(document).on('keypress', function(e) {
          if (e.which === 13) { // Enter key
            $('.form-btn').click();
          }
        });

        // Add ripple effect to button
        $('.modern-btn').on('click', function(e) {
          const btn = $(this);
          const ripple = $('<span class="ripple"></span>');

          btn.append(ripple);

          setTimeout(() => {
            ripple.remove();
          }, 600);
        });
      });

      // Remember Me Functions
      function loadRememberedCredentials() {
        if (localStorage.getItem('rememberMe') === 'true') {
          const savedUsername = localStorage.getItem('savedUsername');
          const savedPassword = localStorage.getItem('savedPassword');

          if (savedUsername) {
            $('#username').val(savedUsername);
            // Trigger the floating label
            $('#username').trigger('input');
            // Add glow effect
            $('#username').addClass('credentials-loaded');
          }

          if (savedPassword) {
            $('#password').val(atob(savedPassword)); // Decode base64
            // Trigger the floating label
            $('#password').trigger('input');
            // Add glow effect
            $('#password').addClass('credentials-loaded');
          }

          $('#rememberMe').prop('checked', true);
          $('#rememberOptions').show();

          // Show a subtle notification
          showRememberNotification();

          // Check if credentials are expired (30 days)
          const savedDate = localStorage.getItem('savedDate');
          if (savedDate) {
            const daysDiff = (new Date() - new Date(savedDate)) / (1000 * 60 * 60 * 24);
            if (daysDiff > 30) {
              clearRememberedCredentials();
              showExpiredNotification();
            }
          }
        }
      }

      function saveCredentials(username, password) {
        if ($('#rememberMe').is(':checked')) {
          localStorage.setItem('rememberMe', 'true');
          localStorage.setItem('savedUsername', username);
          localStorage.setItem('savedPassword', btoa(password)); // Encode base64 for basic security
          localStorage.setItem('savedDate', new Date().toISOString());
        } else {
          clearRememberedCredentials();
        }
      }

      function clearRememberedCredentials() {
        localStorage.removeItem('rememberMe');
        localStorage.removeItem('savedUsername');
        localStorage.removeItem('savedPassword');
        localStorage.removeItem('savedDate');
      }

      function showRememberNotification() {
        const notification = $('<div class="remember-notification animate__animated animate__fadeInDown">' +
          '<i class="fas fa-user-check me-2"></i>Welcome back! Your credentials have been restored.' +
          '<button type="button" class="btn-close-notification" onclick="hideRememberNotification()">' +
          '<i class="fas fa-times"></i></button></div>');

        $('body').append(notification);

        // Auto hide after 4 seconds
        setTimeout(() => {
          hideRememberNotification();
        }, 4000);
      }

      function hideRememberNotification() {
        $('.remember-notification').addClass('animate__fadeOutUp');
        setTimeout(() => {
          $('.remember-notification').remove();
        }, 500);
      }

      function showExpiredNotification() {
        const notification = $('<div class="remember-notification animate__animated animate__fadeInDown" style="background: linear-gradient(135deg, #f59e0b, #d97706);">' +
          '<i class="fas fa-clock me-2"></i>Your saved credentials have expired and been cleared.' +
          '<button type="button" class="btn-close-notification" onclick="hideRememberNotification()">' +
          '<i class="fas fa-times"></i></button></div>');

        $('body').append(notification);

        setTimeout(() => {
          hideRememberNotification();
        }, 5000);
      }

      function clearAllRememberedData() {
        if (confirm('Are you sure you want to clear all saved login data?')) {
          clearRememberedCredentials();
          $('#rememberMe').prop('checked', false);
          $('#rememberOptions').slideUp(300);
          $('#username').val('').trigger('input');
          $('#password').val('').trigger('input');

          // Show confirmation
          const notification = $('<div class="remember-notification animate__animated animate__fadeInDown" style="background: linear-gradient(135deg, #ef4444, #dc2626);">' +
            '<i class="fas fa-trash-alt me-2"></i>All saved login data has been cleared.' +
            '<button type="button" class="btn-close-notification" onclick="hideRememberNotification()">' +
            '<i class="fas fa-times"></i></button></div>');

          $('body').append(notification);

          setTimeout(() => {
            hideRememberNotification();
          }, 3000);
        }
      }

      // Override the showAlert function to work with modern design
      function showModernAlert() {
        $('#page-alert').removeClass('d-none').fadeIn('slow');
      }

      // Enhance the existing access.js functionality
      $(document).ready(function() {
        // Override the form-btn click to add loading state
        $('.form-btn').off('click').on('click', function() {
          const btn = $(this);
          const loader = btn.find('.btn-loader');

          var errorMessage = "";
          var errFlag = "";

          if($('input[name=username]').val() == "" || $('input[name=password]').val() == ""){

              if($('input[name=username]').val() == ""){
                  errFlag = 1;
              }

              if($('input[name=password]').val() == ""){
                  errFlag = 2;
              }

              switch(errFlag) {
                  case 1:
                      errorMessage = "Username is required.";
                      break;
                  case 2:
                      errorMessage = "Password is required.";
                      break;
                  default:
                      errorMessage = "Username and password is required.";
              }

              $('#page-alert-message').html(errorMessage);
              showModernAlert();
          } else {
              // Show loading state
              btn.prop('disabled', true);
              loader.removeClass('d-none');

              // Get credentials for saving
              const username = $('input[name=username]').val();
              const password = $('input[name=password]').val();
              const branchId = $('select[name=branch_id]').val();

              // Validate branch selection
              if(!branchId) {
                $('#page-alert-message').html("Please select a branch.");
                $('#page-alert').removeClass('alert-success').addClass('alert-danger');
                showModernAlert();
                btn.prop('disabled', false);
                loader.addClass('d-none');
                return;
              }

              //ajax to send datas
              $.ajax({
                type: 'POST',
                url: 'proc/process-login-users.php',
                data: $('.form-signin').serialize(),
                success: function(msg){
                  // Hide loading state
                  btn.prop('disabled', false);
                  loader.addClass('d-none');

                  console.log('Login response:', msg); // Debug log

                  // Try to parse as JSON first
                  let response;
                  try {
                    response = JSON.parse(msg);
                  } catch(e) {
                    // If not JSON, treat as old format
                    response = msg;
                  }

                  // Handle both old format (msg==1) and new format (response.success)
                  if((response.success === true) || (response == 1 || response == '1') || (msg == 1 || msg == '1')){
                      // Save credentials if remember me is checked
                      saveCredentials(username, password);

                      // Show success message with branch info
                      let branchName = 'Selected Branch';
                      let branchUrl = 'dashboard_enhanced.php';

                      if(response && typeof response === 'object') {
                        branchName = response.branch_name || 'Selected Branch';
                        branchUrl = response.branch_url || 'dashboard_enhanced.php';
                      }

                      $('#page-alert-message').html('<i class="fas fa-check-circle me-2"></i>Login successful! Redirecting to ' + branchName + '...');
                      $('#page-alert').removeClass('alert-danger').addClass('alert-success');
                      showModernAlert();

                      // Redirect to branch-specific URL after short delay
                      setTimeout(() => {
                        // Use the branch URL directly (can be external domain)
                        window.location.href = branchUrl;
                      }, 1500);
                  } else {
                      // Clear saved credentials on failed login
                      if ($('#rememberMe').is(':checked')) {
                        clearRememberedCredentials();
                        $('#rememberMe').prop('checked', false);
                      }

                      $('#page-alert-message').html("Wrong username, password, or branch combination!");
                      $('#page-alert').removeClass('alert-success').addClass('alert-danger');
                      showModernAlert();
                  }
                },
                error: function(){
                  // Hide loading state
                  btn.prop('disabled', false);
                  loader.addClass('d-none');

                  $('#page-alert-message').html("Connection error. Please try again.");
                  $('#page-alert').removeClass('alert-success').addClass('alert-danger');
                  showModernAlert();
                }
              });//end ajax
          }
        });
      });
    </script>

  </body>
</html>
