<?php
include('auth.php');
require_once('proc/config.php');

// Get dashboard statistics
$total_personnel = 0;
$active_personnel = 0;
$total_visitors = 0;
$today_attendance = 0;

// Count total personnel
$sql = "SELECT COUNT(*) as total FROM tbl_personnel";
$result = mysql_query($sql);
if($result && mysql_num_rows($result) > 0) {
    $row = mysql_fetch_assoc($result);
    $total_personnel = $row['total'];
}

// Count active personnel
$sql = "SELECT COUNT(*) as active FROM tbl_personnel WHERE Status != 'Resigned' OR Status IS NULL OR Status = ''";
$result = mysql_query($sql);
if($result && mysql_num_rows($result) > 0) {
    $row = mysql_fetch_assoc($result);
    $active_personnel = $row['active'];
}

// Count total users
$sql = "SELECT COUNT(*) as total FROM tb_user";
$result = mysql_query($sql);
if($result && mysql_num_rows($result) > 0) {
    $row = mysql_fetch_assoc($result);
    $total_users = $row['total'];
}

// Count today's attendance (if tbl_in_out exists)
$sql = "SELECT COUNT(*) as today FROM tbl_in_out WHERE DATE(TimeRecord) = CURDATE()";
$result = mysql_query($sql);
if($result && mysql_num_rows($result) > 0) {
    $row = mysql_fetch_assoc($result);
    $today_attendance = $row['today'];
}

// Get recent activities
$recent_activities = array();
$sql = "SELECT FullName, TimeRecord, TimeFlag FROM tbl_in_out ORDER BY TimeRecord DESC LIMIT 5";
$result = mysql_query($sql);
if($result && mysql_num_rows($result) > 0) {
    while($row = mysql_fetch_assoc($result)) {
        $recent_activities[] = $row;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Dashboard - Biometric Access System</title>
    
    <!-- Bootstrap CSS -->
    <link href="assets/css/bootstrap.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="assets/font-awesome/css/font-awesome.css" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }
        
        .dashboard-container {
            padding: 20px;
            margin-top: 70px;
        }
        
        .stats-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            opacity: 0;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }
        
        .stats-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.8;
        }
        
        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stats-label {
            color: #666;
            font-size: 1rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .quick-actions {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .action-btn {
            display: block;
            width: 100%;
            padding: 15px;
            margin-bottom: 10px;
            border: none;
            border-radius: 10px;
            text-decoration: none;
            color: white;
            font-weight: 500;
            transition: all 0.3s ease;
            text-align: center;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            color: white;
            text-decoration: none;
        }
        
        .btn-personnel { background: linear-gradient(45deg, #4CAF50, #45a049); }
        .btn-attendance { background: linear-gradient(45deg, #2196F3, #1976D2); }
        .btn-reports { background: linear-gradient(45deg, #FF9800, #F57C00); }
        .btn-settings { background: linear-gradient(45deg, #9C27B0, #7B1FA2); }
        
        .recent-activity {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .activity-item {
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .navbar-brand {
            font-weight: bold;
            font-size: 1.3rem;
        }
        
        .welcome-header {
            color: white;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .welcome-header h1 {
            font-size: 2.5rem;
            font-weight: 300;
            margin-bottom: 10px;
        }
        
        .welcome-header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-inverse navbar-fixed-top" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-collapse">
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="dashboard.php">
                    <i class="fa fa-fingerprint"></i> Biometric Access System
                </a>
            </div>
            <div class="navbar-collapse collapse">
                <ul class="nav navbar-nav navbar-right">
                    <?php if($_SESSION['ACCESSLEVEL']=='Admin') { ?>
                        <li><a href="pg_accounts.php"><i class="fa fa-users"></i>Employees</a></li>
                        <li><a href="biometrics_logs.php"><i class="fa fa-clock-o"></i> Attendance</a></li>
                        <li><a href="report_menu.php"><i class="fa fa-bar-chart"></i> Reports</a></li>
                        <li><a href="pg_settings.php"><i class="fa fa-cog"></i> Settings</a></li>
                        <li><a href="logout.php"><i class="fa fa-sign-out"></i> Logout</a></li>
                    <?php } ?>
                </ul>
            </div>
        </div>
    </nav>

    <div class="dashboard-container">
        <!-- Welcome Header -->
        <div class="welcome-header">
            <h1>Welcome, <?php echo $_SESSION['SESS_USER_FULL_NAME']; ?>!</h1>
            <p>Basic Biometric Access Control System Dashboard</p>
            <p id="current-time" style="font-size: 1rem; opacity: 0.8;"></p>
        </div>

        <div class="container-fluid">
            <!-- Statistics Cards -->
            <div class="row">
                <div class="col-md-3 col-sm-6">
                    <div class="stats-card text-center">
                        <div class="stats-icon text-primary">
                            <i class="fa fa-users"></i>
                        </div>
                        <div class="stats-number text-primary"><?php echo $total_personnel; ?></div>
                        <div class="stats-label">Total Employees</div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="stats-card text-center">
                        <div class="stats-icon text-success">
                            <i class="fa fa-user-check"></i>
                        </div>
                        <div class="stats-number text-success"><?php echo $active_personnel; ?></div>
                        <div class="stats-label">Active Employees</div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="stats-card text-center">
                        <div class="stats-icon text-warning">
                            <i class="fa fa-clock-o"></i>
                        </div>
                        <div class="stats-number text-warning"><?php echo $today_attendance; ?></div>
                        <div class="stats-label">Today's Attendance</div>
                    </div>
                </div>
                <div class="col-md-3 col-sm-6">
                    <div class="stats-card text-center">
                        <div class="stats-icon text-info">
                            <i class="fa fa-key"></i>
                        </div>
                        <div class="stats-number text-info"><?php echo $total_users; ?></div>
                        <div class="stats-label">System Users</div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions and Recent Activity -->
            <div class="row">
                <div class="col-md-8">
                    <div class="quick-actions">
                        <h3><i class="fa fa-bolt"></i> Quick Actions</h3>
                        <div class="row">
                            <div class="col-md-6">
                                <a href="pg_accounts.php" class="action-btn btn-personnel">
                                    <i class="fa fa-user-plus"></i> Manage Employees
                                </a>
                                <a href="biometrics_logs.php" class="action-btn btn-attendance">
                                    <i class="fa fa-clock-o"></i> View Attendance
                                </a>
                            </div>
                            <div class="col-md-6">
                                <a href="report_menu.php" class="action-btn btn-reports">
                                    <i class="fa fa-bar-chart"></i> Generate Reports
                                </a>
                                <a href="pg_settings.php" class="action-btn btn-settings">
                                    <i class="fa fa-cog"></i> System Settings
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="recent-activity">
                        <h3><i class="fa fa-history"></i> Recent Activity</h3>
                        <?php if(empty($recent_activities)): ?>
                            <p class="text-muted">No recent activity found.</p>
                        <?php else: ?>
                            <?php foreach($recent_activities as $activity): ?>
                                <div class="activity-item">
                                    <strong><?php echo htmlspecialchars($activity['FullName']); ?></strong>
                                    <br>
                                    <small class="text-muted">
                                        <?php echo $activity['TimeFlag']; ?> - 
                                        <?php echo date('M j, Y g:i A', strtotime($activity['TimeRecord'])); ?>
                                    </small>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="assets/js/jquery.js"></script>
    <script src="assets/js/bootstrap.min.js"></script>
    <script>
        $(document).ready(function() {
            // Add smooth animations
            $('.stats-card').each(function(index) {
                $(this).delay(index * 100).animate({
                    opacity: 1
                }, 500);
            });

            // Update time every second
            function updateTime() {
                var now = new Date();
                var timeString = now.toLocaleString();
                $('#current-time').text(timeString);
            }

            // Add current time display
            if ($('#current-time').length === 0) {
                $('.welcome-header p').after('<p id="current-time" style="font-size: 1rem; opacity: 0.8;"></p>');
            }

            updateTime();
            setInterval(updateTime, 1000);

            // Add click effects to action buttons
            $('.action-btn').on('click', function(e) {
                var ripple = $('<span class="ripple"></span>');
                $(this).append(ripple);

                setTimeout(function() {
                    ripple.remove();
                }, 600);
            });
        });
    </script>
    <style>
        .stats-card {
            opacity: 0;
        }

        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: scale(0);
            animation: ripple-animation 0.6s linear;
        }

        @keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        .action-btn {
            position: relative;
            overflow: hidden;
        }
    </style>
</body>
</html>
