div.DTE_Field div.multi-value,
div.DTE_Field div.multi-restore {
  display: none;
  cursor: pointer;
}
div.DTE_Field div.multi-value span,
div.DTE_Field div.multi-restore span {
  display: block;
  color: #666;
}
div.DTE_Field div.multi-value:hover,
div.DTE_Field div.multi-restore:hover {
  background-color: #f1f1f1;
}
div.DTE_Field div.multi-restore {
  margin-top: 0.5em;
  font-size: 0.8em;
  line-height: 1.25em;
}
div.DTE_Field:after {
  display: block;
  content: ".";
  height: 0;
  line-height: 0;
  clear: both;
  visibility: hidden;
}

div.DTE_Inline div.DTE_Field {
  width: 100%;
}
div.DTE_Inline div.DTE_Field > div {
  width: 100%;
  padding: 0;
}
div.DTE_Inline div.DTE_Field label {
  display: none;
}
div.DTE_Inline div.DTE_Field input {
  width: 100%;
  color: black;
}
div.DTE_Inline div.DTE_Field div.help-block {
  display: none;
}

div.DTE_Field_Type_checkbox div.controls,
div.DTE_Field_Type_radio div.controls {
  margin-top: 0.4em;
}
div.DTE_Field_Type_checkbox div.controls label,
div.DTE_Field_Type_radio div.controls label {
  margin-left: 0.75em;
  margin-bottom: 0;
  vertical-align: middle;
  font-weight: normal;
}

div.DTE_Bubble {
  position: absolute;
  z-index: 11;
  margin-top: -6px;
  opacity: 0;
}
div.DTE_Bubble div.DTE_Bubble_Liner {
  position: absolute;
  bottom: 0;
  border: 1px solid black;
  width: 300px;
  margin-left: -150px;
  background-color: white;
  box-shadow: 2px 2px 7px #555;
  border-radius: 5px;
  border: 2px solid #444;
  padding: 1em;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
div.DTE_Bubble div.DTE_Bubble_Liner div.DTE_Bubble_Table {
  display: table;
  width: 100%;
}
div.DTE_Bubble div.DTE_Bubble_Liner div.DTE_Bubble_Table > form {
  display: table-cell;
}
div.DTE_Bubble div.DTE_Bubble_Liner div.DTE_Bubble_Table > form div.DTE_Form_Content {
  padding: 0;
}
div.DTE_Bubble div.DTE_Bubble_Liner div.DTE_Bubble_Table > form div.DTE_Form_Content div.DTE_Field {
  position: relative;
  zoom: 1;
  margin-bottom: 0.5em;
}
div.DTE_Bubble div.DTE_Bubble_Liner div.DTE_Bubble_Table > form div.DTE_Form_Content div.DTE_Field:last-child {
  margin-bottom: 0;
}
div.DTE_Bubble div.DTE_Bubble_Liner div.DTE_Bubble_Table div.DTE_Form_Buttons {
  display: table-cell;
  vertical-align: bottom;
  padding: 0 0 0 0.75em;
  width: 1%;
}
div.DTE_Bubble div.DTE_Bubble_Liner div.DTE_Header {
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
}
div.DTE_Bubble div.DTE_Bubble_Liner div.DTE_Header + div.DTE_Form_Info,
div.DTE_Bubble div.DTE_Bubble_Liner div.DTE_Header + div.DTE_Bubble_Table {
  padding-top: 42px;
}
div.DTE_Bubble div.DTE_Bubble_Liner div.DTE_Form_Error {
  float: none;
  display: none;
  padding: 0;
  margin-bottom: 0.5em;
}
div.DTE_Bubble div.DTE_Bubble_Liner div.DTE_Bubble_Close {
  position: absolute;
  top: -11px;
  right: -11px;
  width: 22px;
  height: 22px;
  border: 2px solid white;
  background-color: black;
  text-align: center;
  border-radius: 15px;
  cursor: pointer;
  z-index: 12;
  box-shadow: 2px 2px 6px #111;
}
div.DTE_Bubble div.DTE_Bubble_Liner div.DTE_Bubble_Close:after {
  content: '\00d7';
  color: white;
  font-weight: bold;
  font-size: 18px;
  line-height: 22px;
  font-family: 'Courier New', Courier, monospace;
  padding-left: 1px;
}
div.DTE_Bubble div.DTE_Bubble_Liner div.DTE_Bubble_Close:hover {
  background-color: #092079;
  box-shadow: 2px 2px 9px #111;
}
div.DTE_Bubble div.DTE_Bubble_Triangle {
  position: absolute;
  height: 10px;
  width: 10px;
  top: -6px;
  background-color: white;
  border: 2px solid #444;
  border-top: none;
  border-right: none;
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  -o-transform: rotate(-45deg);
  transform: rotate(-45deg);
}
div.DTE_Bubble.DTE_Processing div.DTE_Bubble_Liner:after {
  position: absolute;
  content: ' ';
  display: block;
  top: 12px;
  right: 18px;
  height: 12px;
  width: 17px;
  background: url("../images/ajax-loader-small.gif") no-repeat top left;
}
div.DTE_Bubble.below div.DTE_Bubble_Liner {
  top: 10px;
  bottom: auto;
}
div.DTE_Bubble.below div.DTE_Bubble_Triangle {
  top: 4px;
  -webkit-transform: rotate(135deg);
  -moz-transform: rotate(135deg);
  -ms-transform: rotate(135deg);
  -o-transform: rotate(135deg);
  transform: rotate(135deg);
}

div.DTE_Bubble_Background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  /* Fallback */
  background: -ms-radial-gradient(center, ellipse farthest-corner, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.7) 100%);
  /* IE10 Consumer Preview */
  background: -moz-radial-gradient(center, ellipse farthest-corner, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.7) 100%);
  /* Firefox */
  background: -o-radial-gradient(center, ellipse farthest-corner, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.7) 100%);
  /* Opera */
  background: -webkit-gradient(radial, center center, 0, center center, 497, color-stop(0, rgba(0, 0, 0, 0.3)), color-stop(1, rgba(0, 0, 0, 0.7)));
  /* Webkit (Safari/Chrome 10) */
  background: -webkit-radial-gradient(center, ellipse farthest-corner, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.7) 100%);
  /* Webkit (Chrome 11+) */
  background: radial-gradient(ellipse farthest-corner at center, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.7) 100%);
  /* W3C Markup, IE10 Release Preview */
  z-index: 10;
}
div.DTE_Bubble_Background > div {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#99000000, endColorstr=#99000000);
  -ms-filter: "progid:DXImageTransform.Microsoft.gradient(startColorstr=#99000000, endColorstr=#99000000)";
}
div.DTE_Bubble_Background > div:not([dummy]) {
  filter: progid:DXImageTransform.Microsoft.gradient(enabled='false');
}

div.DTE_Bubble div.DTE_Bubble_Liner {
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  padding: 0 0 0.5em 0;
  border: 1px solid rgba(0, 0, 0, 0.2);
}
div.DTE_Bubble div.DTE_Bubble_Liner div.DTE_Bubble_Table > form div.DTE_Form_Content div.DTE_Field label,
div.DTE_Bubble div.DTE_Bubble_Liner div.DTE_Bubble_Table > form div.DTE_Form_Content div.DTE_Field > div {
  width: 100%;
  float: none;
  clear: both;
  text-align: left;
}
div.DTE_Bubble div.DTE_Bubble_Liner div.DTE_Bubble_Table > form div.DTE_Form_Content div.DTE_Field label {
  padding-bottom: 4px;
}
div.DTE_Bubble div.DTE_Bubble_Liner div.DTE_Header {
  background-color: #f7f7f7;
  border-bottom: 1px solid #ebebeb;
  font-size: 14px;
  width: 100%;
}
div.DTE_Bubble div.DTE_Bubble_Liner div.DTE_Bubble_Close:after {
  margin-top: -2px;
  display: block;
}
div.DTE_Bubble div.DTE_Bubble_Triangle {
  border: 1px solid rgba(0, 0, 0, 0.2);
}

div.DTE_Bubble_Background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10;
  background-color: rgba(0, 0, 0, 0.05);
}

div.DTE div.editor_upload {
  padding-top: 4px;
}
div.DTE div.editor_upload div.eu_table {
  display: table;
  width: 100%;
}
div.DTE div.editor_upload div.row {
  display: table-row;
}
div.DTE div.editor_upload div.cell {
  display: table-cell;
  position: relative;
  width: 50%;
  vertical-align: top;
}
div.DTE div.editor_upload div.cell + div.cell {
  padding-left: 10px;
}
div.DTE div.editor_upload div.row + div.row div.cell {
  padding-top: 10px;
}
div.DTE div.editor_upload button.btn,
div.DTE div.editor_upload input[type=file] {
  width: 100%;
  height: 2.3em;
  font-size: 0.8em;
  text-align: center;
  line-height: 1em;
}
div.DTE div.editor_upload input[type=file] {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  opacity: 0;
}
div.DTE div.editor_upload div.drop {
  position: relative;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  border: 3px dashed #ccc;
  border-radius: 6px;
  min-height: 4em;
  color: #999;
  padding-top: 3px;
  text-align: center;
}
div.DTE div.editor_upload div.drop.over {
  border: 3px dashed #111;
  color: #111;
}
div.DTE div.editor_upload div.drop span {
  max-width: 75%;
  font-size: 0.85em;
  line-height: 1em;
}
div.DTE div.editor_upload div.rendered img {
  max-width: 8em;
  margin: 0 auto;
}
div.DTE div.editor_upload.noDrop div.drop {
  display: none;
}
div.DTE div.editor_upload.noDrop div.row.second {
  display: none;
}
div.DTE div.editor_upload.noDrop div.rendered {
  margin-top: 10px;
}
div.DTE div.editor_upload.noClear div.clearValue button {
  display: none;
}
div.DTE div.editor_upload.multi div.cell {
  display: block;
  width: 100%;
}
div.DTE div.editor_upload.multi div.cell div.drop {
  min-height: 0;
  padding-bottom: 5px;
}
div.DTE div.editor_upload.multi div.clearValue {
  display: none;
}
div.DTE div.editor_upload.multi ul {
  list-style-type: none;
  margin: 0;
  padding: 0;
}
div.DTE div.editor_upload.multi ul li {
  position: relative;
  margin-top: 0.5em;
}
div.DTE div.editor_upload.multi ul li:first-child {
  margin-top: 0;
}
div.DTE div.editor_upload.multi ul li img {
  vertical-align: middle;
}
div.DTE div.editor_upload.multi ul li button {
  position: absolute;
  width: 40px;
  right: 0;
  top: 50%;
  margin-top: -1.5em;
}

div.DTE div.editor_upload button.btn,
div.DTE div.editor_upload input[type=file] {
  height: auto;
}
div.DTE div.editor_upload ul li button {
  padding-bottom: 8px;
}

div.editor-datetime {
  position: absolute;
  background-color: white;
  z-index: 2050;
  border: 1px solid #ccc;
  box-shadow: 0 5px 15px -5px rgba(0, 0, 0, 0.5);
  padding-bottom: 5px;
}
div.editor-datetime div.editor-datetime-title {
  text-align: center;
  padding: 5px 0px 3px;
}
div.editor-datetime table {
  border-spacing: 0;
  margin: 6px 13px;
}
div.editor-datetime table th {
  font-size: 0.8em;
  color: #777;
  font-weight: normal;
  width: 14.285714286%;
  padding: 0 0 4px 0;
  text-align: center;
}
div.editor-datetime table td {
  font-size: 0.9em;
  color: #444;
  padding: 0;
}
div.editor-datetime table td.day {
  text-align: right;
  background: #f5f5f5;
}
div.editor-datetime table td.day.disabled {
  color: #aaa;
  background: white;
}
div.editor-datetime table td.day.today {
  background-color: #ddd;
}
div.editor-datetime table td.day.today button {
  font-weight: bold;
}
div.editor-datetime table td.day.selected button {
  background: #337ab7;
  color: white;
  border-radius: 2px;
}
div.editor-datetime table td.day button:hover {
  background: #ff8000;
  color: white;
  border-radius: 2px;
}
div.editor-datetime table td.editor-datetime-week {
  font-size: 0.7em;
}
div.editor-datetime table button {
  width: 100%;
  box-sizing: border-box;
  border: none;
  background: transparent;
  font-size: inherit;
  color: inherit;
  text-align: inherit;
  padding: 5px 9px;
  cursor: pointer;
  margin: 0;
}
div.editor-datetime table.weekNumber th {
  width: 12.5%;
}
div.editor-datetime div.editor-datetime-label {
  position: relative;
  display: inline-block;
  height: 30px;
  padding: 5px 6px;
  border: 1px solid transparent;
  box-sizing: border-box;
  cursor: pointer;
}
div.editor-datetime div.editor-datetime-label:hover {
  border: 1px solid #ddd;
  border-radius: 2px;
  background-color: #f5f5f5;
}
div.editor-datetime div.editor-datetime-label select {
  position: absolute;
  top: 6px;
  left: 0;
  cursor: pointer;
  opacity: 0;
  -ms-filter: "alpha(opacity=0)";
}
div.editor-datetime div.editor-datetime-time {
  text-align: center;
}
div.editor-datetime div.editor-datetime-time > span {
  vertical-align: middle;
}
div.editor-datetime div.editor-datetime-time div.editor-datetime-timeblock {
  display: inline-block;
  vertical-align: middle;
}
div.editor-datetime div.editor-datetime-iconLeft,
div.editor-datetime div.editor-datetime-iconRight,
div.editor-datetime div.editor-datetime-iconUp,
div.editor-datetime div.editor-datetime-iconDown {
  width: 30px;
  height: 30px;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0.3;
  overflow: hidden;
  box-sizing: border-box;
}
div.editor-datetime div.editor-datetime-iconLeft:hover,
div.editor-datetime div.editor-datetime-iconRight:hover,
div.editor-datetime div.editor-datetime-iconUp:hover,
div.editor-datetime div.editor-datetime-iconDown:hover {
  border: 1px solid #ccc;
  border-radius: 2px;
  background-color: #f0f0f0;
  opacity: 0.6;
}
div.editor-datetime div.editor-datetime-iconLeft button,
div.editor-datetime div.editor-datetime-iconRight button,
div.editor-datetime div.editor-datetime-iconUp button,
div.editor-datetime div.editor-datetime-iconDown button {
  border: none;
  background: transparent;
  text-indent: 30px;
  height: 100%;
  width: 100%;
  cursor: pointer;
}
div.editor-datetime div.editor-datetime-iconLeft {
  position: absolute;
  top: 5px;
  left: 5px;
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAeCAYAAAAsEj5rAAAAUklEQVR42u3VMQoAIBADQf8Pgj+OD9hG2CtONJB2ymQkKe0HbwAP0xucDiQWARITIDEBEnMgMQ8S8+AqBIl6kKgHiXqQqAeJepBo/z38J/U0uAHlaBkBl9I4GwAAAABJRU5ErkJggg==");
}
div.editor-datetime div.editor-datetime-iconRight {
  position: absolute;
  top: 5px;
  right: 5px;
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAeCAYAAAAsEj5rAAAAU0lEQVR42u3VOwoAMAgE0dwfAnNjU26bYkBCFGwfiL9VVWoO+BJ4Gf3gtsEKKoFBNTCoCAYVwaAiGNQGMUHMkjGbgjk2mIONuXo0nC8XnCf1JXgArVIZAQh5TKYAAAAASUVORK5CYII=");
}
div.editor-datetime div.editor-datetime-iconUp {
  height: 20px;
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAALCAMAAABf9c24AAAAFVBMVEX///99fX1+fn57e3t6enoAAAAAAAC73bqPAAAABnRSTlMAYmJkZt92bnysAAAAL0lEQVR4AWOgJmBhxCvLyopHnpmVjY2VCadeoCxIHrcsWJ4RlyxCHlMWCTBRJxwAjrIBDMWSiM0AAAAASUVORK5CYII=");
}
div.editor-datetime div.editor-datetime-iconDown {
  height: 20px;
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAALCAMAAABf9c24AAAAFVBMVEX///99fX1+fn57e3t6enoAAAAAAAC73bqPAAAABnRSTlMAYmJkZt92bnysAAAAMElEQVR4AWOgDmBiRQIsmPKMrGxQgJDFlEfIYpoPk8Utz8qM232MYFfhkQfKUg8AANefAQxecJ58AAAAAElFTkSuQmCC");
}
