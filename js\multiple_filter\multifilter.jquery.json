{"name": "multifilter", "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Filter a HTML table based on multiple inputs", "keywords": ["table", "filter", "input"], "version": "1.2.1", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://tommyp.org"}, "maintainers": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://tommyp.org"}], "licenses": [{"type": "MIT", "url": "https://github.com/tommyp/multifilter/blob/master/LICENSE"}], "bugs": "https://github.com/tommyp/multifilter/issues", "homepage": "https://github.com/tommyp/multifilter", "docs": "https://github.com/tommyp/multifilter", "download": "http://code.jquery.com/#multifilter", "dependencies": {"jquery": ">=1.5"}}