<?php
/**
 * API-Based Database Synchronization Client
 * This runs on your local system and communicates with your online server via API
 */

// Configuration
$api_config = array(
    'base_url' => 'https://yourdomain.com/BWP/api/sync_api.php',  // Replace with your online domain
    'api_key' => 'your-secure-api-key-here',                      // Must match the key in sync_api.php
    'timeout' => 30,
    'max_batch_size' => 100
);

// Local database config
require_once('proc/config.php');

// Handle AJAX requests
if (isset($_GET['action'])) {
    handleLocalRequest($_GET['action']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_GET['action'])) {
    handleLocalPostRequest($_GET['action']);
    exit;
}

// Handle local requests
function handleLocalRequest($action) {
    switch ($action) {
        case 'local_stats':
            getLocalStats();
            break;
        case 'get_employees':
            getLocalEmployees();
            break;
        case 'get_attendance':
            getLocalAttendance();
            break;
        default:
            echo json_encode(['success' => false, 'message' => 'Unknown action']);
    }
}

// Handle local POST requests
function handleLocalPostRequest($action) {
    $input = json_decode(file_get_contents('php://input'), true);

    switch ($action) {
        case 'save_employees':
            saveEmployeesToLocal($input['data']);
            break;
        case 'save_attendance':
            saveAttendanceToLocal($input['data']);
            break;
        default:
            echo json_encode(['success' => false, 'message' => 'Unknown action']);
    }
}

// Get local database statistics
function getLocalStats() {
    $stats = ['employees' => 0, 'attendance' => 0];

    // Employee count
    $emp_result = mysql_query("SELECT COUNT(*) as count FROM tbl_personnel");
    if ($emp_result) {
        $emp_row = mysql_fetch_assoc($emp_result);
        $stats['employees'] = $emp_row['count'];
    }

    // Attendance count
    $att_result = mysql_query("SELECT COUNT(*) as count FROM tbl_in_out");
    if ($att_result) {
        $att_row = mysql_fetch_assoc($att_result);
        $stats['attendance'] = $att_row['count'];
    }

    echo json_encode($stats);
}

// Get local employees
function getLocalEmployees() {
    $batch_size = isset($_GET['batch_size']) ? intval($_GET['batch_size']) : 100;
    $offset = isset($_GET['offset']) ? intval($_GET['offset']) : 0;

    $sql = "SELECT * FROM tbl_personnel ORDER BY AccessID LIMIT $offset, $batch_size";
    $result = mysql_query($sql);

    if (!$result) {
        echo json_encode(['success' => false, 'message' => 'Database query failed']);
        return;
    }

    $employees = [];
    while ($row = mysql_fetch_assoc($result)) {
        $employees[] = $row;
    }

    echo json_encode(['success' => true, 'data' => $employees]);
}

// Get local attendance
function getLocalAttendance() {
    $batch_size = isset($_GET['batch_size']) ? intval($_GET['batch_size']) : 100;
    $date_range = isset($_GET['date_range']) ? $_GET['date_range'] : '30';
    $offset = isset($_GET['offset']) ? intval($_GET['offset']) : 0;

    $where_clause = '';
    if ($date_range !== 'all') {
        $days = intval($date_range);
        $where_clause = "WHERE DATE(TimeRecord) >= DATE_SUB(CURDATE(), INTERVAL $days DAY)";
    }

    $sql = "SELECT * FROM tbl_in_out $where_clause ORDER BY TimeRecord DESC LIMIT $offset, $batch_size";
    $result = mysql_query($sql);

    if (!$result) {
        echo json_encode(['success' => false, 'message' => 'Database query failed']);
        return;
    }

    $attendance = [];
    while ($row = mysql_fetch_assoc($result)) {
        $attendance[] = $row;
    }

    echo json_encode(['success' => true, 'data' => $attendance]);
}

// Save employees to local database
function saveEmployeesToLocal($employees) {
    $created = 0;
    $updated = 0;
    $errors = 0;

    foreach ($employees as $employee) {
        if (!isset($employee['AccessID']) || !$employee['AccessID']) {
            $errors++;
            continue;
        }

        $access_id = mysql_real_escape_string($employee['AccessID']);
        $employee_id = mysql_real_escape_string($employee['EmployeeID'] ?? '');
        $full_name = mysql_real_escape_string($employee['FullName'] ?? '');
        $position = mysql_real_escape_string($employee['Position'] ?? '');
        $agency = mysql_real_escape_string($employee['AgencyCompany'] ?? '');
        $contact = mysql_real_escape_string($employee['ContactNo'] ?? '');
        $date_hired = mysql_real_escape_string($employee['DateHired'] ?? '');
        $status = mysql_real_escape_string($employee['Status'] ?? '');
        $time_in = mysql_real_escape_string($employee['TimeIN'] ?? '');
        $time_out = mysql_real_escape_string($employee['TimeOut'] ?? '');

        // Check if employee exists
        $check_sql = "SELECT COUNT(*) as count FROM tbl_personnel WHERE AccessID = '$access_id'";
        $check_result = mysql_query($check_sql);
        $exists = false;

        if ($check_result) {
            $check_row = mysql_fetch_assoc($check_result);
            $exists = $check_row['count'] > 0;
        }

        if ($exists) {
            // Update existing employee
            $update_sql = "UPDATE tbl_personnel SET
                EmployeeID = '$employee_id',
                FullName = '$full_name',
                Position = '$position',
                AgencyCompany = '$agency',
                ContactNo = '$contact',
                DateHired = '$date_hired',
                Status = '$status',
                TimeIN = '$time_in',
                TimeOut = '$time_out'
                WHERE AccessID = '$access_id'";

            if (mysql_query($update_sql)) {
                $updated++;
            } else {
                $errors++;
            }
        } else {
            // Insert new employee
            $insert_sql = "INSERT INTO tbl_personnel (EmployeeID, AccessID, FullName, Position, AgencyCompany, ContactNo, DateHired, Status, TimeIN, TimeOut)
                VALUES ('$employee_id', '$access_id', '$full_name', '$position', '$agency', '$contact', '$date_hired', '$status', '$time_in', '$time_out')";

            if (mysql_query($insert_sql)) {
                $created++;
            } else {
                $errors++;
            }
        }
    }

    echo json_encode([
        'success' => $errors === 0,
        'created' => $created,
        'updated' => $updated,
        'errors' => $errors,
        'message' => "Processed " . count($employees) . " employees"
    ]);
}

// Save attendance to local database
function saveAttendanceToLocal($attendance_records) {
    $created = 0;
    $skipped = 0;
    $errors = 0;

    foreach ($attendance_records as $record) {
        if (!isset($record['AccessID']) || !isset($record['TimeRecord']) || !isset($record['TimeFlag'])) {
            $errors++;
            continue;
        }

        $access_id = mysql_real_escape_string($record['AccessID']);
        $full_name = mysql_real_escape_string($record['FullName'] ?? '');
        $time_record = mysql_real_escape_string($record['TimeRecord']);
        $time_flag = mysql_real_escape_string($record['TimeFlag']);
        $access_area = mysql_real_escape_string($record['AccessArea'] ?? '');

        // Check if record exists (prevent duplicates)
        $check_sql = "SELECT COUNT(*) as count FROM tbl_in_out WHERE
            AccessID = '$access_id' AND
            TimeRecord = '$time_record' AND
            TimeFlag = '$time_flag'";
        $check_result = mysql_query($check_sql);
        $exists = false;

        if ($check_result) {
            $check_row = mysql_fetch_assoc($check_result);
            $exists = $check_row['count'] > 0;
        }

        if (!$exists) {
            $insert_sql = "INSERT INTO tbl_in_out (AccessID, FullName, TimeRecord, TimeFlag, AccessArea)
                VALUES ('$access_id', '$full_name', '$time_record', '$time_flag', '$access_area')";

            if (mysql_query($insert_sql)) {
                $created++;
            } else {
                $errors++;
            }
        } else {
            $skipped++;
        }
    }

    echo json_encode([
        'success' => $errors === 0,
        'created' => $created,
        'skipped' => $skipped,
        'errors' => $errors,
        'message' => "Processed " . count($attendance_records) . " attendance records"
    ]);
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Database Synchronization</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sync-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            margin: 20px auto;
            max-width: 1200px;
            padding: 30px;
        }
        .api-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            margin: 15px 0;
            padding: 20px;
            transition: transform 0.3s ease;
        }
        .api-card:hover {
            transform: translateY(-3px);
        }
        .btn-api {
            background: linear-gradient(135deg, #28a745, #20c997);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .btn-api:hover {
            background: linear-gradient(135deg, #218838, #1ea085);
            transform: translateY(-2px);
            color: white;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-online { background-color: #28a745; }
        .status-offline { background-color: #dc3545; }
        .status-testing { background-color: #ffc107; animation: pulse 1s infinite; }
        .progress-container {
            display: none;
            margin: 15px 0;
        }
        .log-container {
            background: #f8f9fa;
            border-radius: 8px;
            max-height: 400px;
            overflow-y: auto;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }
        .sync-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-box {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            border-left: 4px solid #007bff;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="sync-container">
            <div class="text-center mb-4">
                <h1><i class="fas fa-cloud-sync me-3"></i>API Database Synchronization</h1>
                <p class="text-muted">Sync data between Local and Online databases using secure API</p>
            </div>

            <!-- API Configuration -->
            <div class="api-card">
                <h4><i class="fas fa-cog me-2"></i>API Configuration</h4>
                <div class="row">
                    <div class="col-md-6">
                        <label class="form-label">Online API URL:</label>
                        <input type="url" class="form-control" id="api-url" value="<?php echo $api_config['base_url']; ?>">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">API Key:</label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="api-key" value="<?php echo $api_config['api_key']; ?>">
                            <button class="btn btn-outline-secondary" type="button" onclick="toggleApiKey()">
                                <i class="fas fa-eye" id="key-toggle-icon"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="mt-3">
                    <button class="btn btn-api" onclick="testApiConnection()">
                        <i class="fas fa-plug me-2"></i>Test API Connection
                    </button>
                    <span class="ms-3">
                        <span class="status-indicator" id="api-status"></span>
                        <span id="api-status-text">Not tested</span>
                    </span>
                </div>
            </div>

            <!-- Database Statistics -->
            <div class="api-card">
                <h4><i class="fas fa-chart-bar me-2"></i>Database Statistics</h4>
                <div class="sync-stats">
                    <div class="stat-box">
                        <div class="stat-number" id="local-employees">-</div>
                        <div class="stat-label">Local Employees</div>
                    </div>
                    <div class="stat-box">
                        <div class="stat-number" id="online-employees">-</div>
                        <div class="stat-label">Online Employees</div>
                    </div>
                    <div class="stat-box">
                        <div class="stat-number" id="local-attendance">-</div>
                        <div class="stat-label">Local Attendance</div>
                    </div>
                    <div class="stat-box">
                        <div class="stat-number" id="online-attendance">-</div>
                        <div class="stat-label">Online Attendance</div>
                    </div>
                </div>
                <button class="btn btn-outline-primary" onclick="refreshStats()">
                    <i class="fas fa-refresh me-2"></i>Refresh Statistics
                </button>
            </div>

            <!-- Sync Operations -->
            <div class="api-card">
                <h4><i class="fas fa-sync-alt me-2"></i>Synchronization Operations</h4>
                
                <div class="row">
                    <div class="col-md-6">
                        <h5><i class="fas fa-users me-2"></i>Employee Data</h5>
                        <div class="d-grid gap-2">
                            <button class="btn btn-api" onclick="syncData('employees', 'upload')">
                                <i class="fas fa-upload me-2"></i>Upload Local → Online
                            </button>
                            <button class="btn btn-api" onclick="syncData('employees', 'download')">
                                <i class="fas fa-download me-2"></i>Download Online → Local
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h5><i class="fas fa-clock me-2"></i>Attendance Data</h5>
                        <div class="d-grid gap-2">
                            <button class="btn btn-api" onclick="syncData('attendance', 'upload')">
                                <i class="fas fa-upload me-2"></i>Upload Local → Online
                            </button>
                            <button class="btn btn-api" onclick="syncData('attendance', 'download')">
                                <i class="fas fa-download me-2"></i>Download Online → Local
                            </button>
                        </div>
                    </div>
                </div>

                <hr>

                <div class="text-center">
                    <h5>Complete Synchronization</h5>
                    <div class="btn-group">
                        <button class="btn btn-api btn-lg" onclick="fullSync('upload')">
                            <i class="fas fa-cloud-upload-alt me-2"></i>Full Upload (Local → Online)
                        </button>
                        <button class="btn btn-api btn-lg" onclick="fullSync('download')">
                            <i class="fas fa-cloud-download-alt me-2"></i>Full Download (Online → Local)
                        </button>
                    </div>
                </div>
            </div>

            <!-- Progress Bar -->
            <div class="progress-container">
                <div class="api-card">
                    <h5>Synchronization Progress</h5>
                    <div class="progress mb-3">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" style="width: 0%" id="sync-progress"></div>
                    </div>
                    <div id="sync-status">Ready to sync...</div>
                </div>
            </div>

            <!-- Sync Options -->
            <div class="api-card">
                <h5><i class="fas fa-sliders-h me-2"></i>Sync Options</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="create-backup" checked>
                            <label class="form-check-label" for="create-backup">
                                Create backup before sync
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="batch-processing" checked>
                            <label class="form-check-label" for="batch-processing">
                                Use batch processing (recommended)
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">Batch Size:</label>
                            <select class="form-select" id="batch-size">
                                <option value="50">50 records</option>
                                <option value="100" selected>100 records</option>
                                <option value="200">200 records</option>
                                <option value="500">500 records</option>
                            </select>
                        </div>
                        <div class="form-group mt-2">
                            <label class="form-label">Date Range (for attendance):</label>
                            <select class="form-select" id="date-range">
                                <option value="7">Last 7 days</option>
                                <option value="30" selected>Last 30 days</option>
                                <option value="90">Last 90 days</option>
                                <option value="365">Last year</option>
                                <option value="all">All records</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sync Log -->
            <div class="api-card">
                <h5><i class="fas fa-list-alt me-2"></i>Synchronization Log</h5>
                <div class="log-container" id="sync-log">
                    <div class="text-muted">API sync operations will be logged here...</div>
                </div>
                <div class="mt-3">
                    <button class="btn btn-outline-secondary btn-sm" onclick="clearLog()">
                        <i class="fas fa-trash me-1"></i>Clear Log
                    </button>
                    <button class="btn btn-outline-primary btn-sm" onclick="exportLog()">
                        <i class="fas fa-download me-1"></i>Export Log
                    </button>
                </div>
            </div>

            <!-- Navigation -->
            <div class="text-center mt-4">
                <a href="index.php" class="btn btn-outline-primary me-2">
                    <i class="fas fa-home me-1"></i>Home
                </a>
                <a href="reports_dashboard.php" class="btn btn-outline-primary">
                    <i class="fas fa-chart-bar me-1"></i>Reports
                </a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Global variables
        let apiUrl = document.getElementById('api-url').value;
        let apiKey = document.getElementById('api-key').value;
        let isApiConnected = false;

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            refreshStats();
        });

        // Toggle API key visibility
        function toggleApiKey() {
            const keyInput = document.getElementById('api-key');
            const toggleIcon = document.getElementById('key-toggle-icon');
            
            if (keyInput.type === 'password') {
                keyInput.type = 'text';
                toggleIcon.className = 'fas fa-eye-slash';
            } else {
                keyInput.type = 'password';
                toggleIcon.className = 'fas fa-eye';
            }
        }

        // Test API connection
        function testApiConnection() {
            updateApiStatus('testing', 'Testing connection...');
            
            apiUrl = document.getElementById('api-url').value;
            apiKey = document.getElementById('api-key').value;
            
            fetch(apiUrl + '?endpoint=test', {
                method: 'GET',
                headers: {
                    'X-API-Key': apiKey,
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    updateApiStatus('online', 'Connected - ' + data.message);
                    isApiConnected = true;
                    logMessage('✅ API connection successful');
                    refreshStats();
                } else {
                    updateApiStatus('offline', 'Failed - ' + data.message);
                    isApiConnected = false;
                    logMessage('❌ API connection failed: ' + data.message);
                }
            })
            .catch(error => {
                updateApiStatus('offline', 'Connection error');
                isApiConnected = false;
                logMessage('❌ API connection error: ' + error.message);
            });
        }

        // Update API status indicator
        function updateApiStatus(status, message) {
            const statusElement = document.getElementById('api-status');
            const textElement = document.getElementById('api-status-text');
            
            statusElement.className = 'status-indicator status-' + status;
            textElement.textContent = message;
        }

        // Refresh statistics
        function refreshStats() {
            if (!isApiConnected) {
                logMessage('⚠️ Please test API connection first');
                return;
            }

            // Get local stats
            fetch('api_sync_client.php?action=local_stats')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('local-employees').textContent = data.employees || '0';
                    document.getElementById('local-attendance').textContent = data.attendance || '0';
                });

            // Get online stats
            fetch(apiUrl + '?endpoint=stats', {
                headers: { 'X-API-Key': apiKey }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    document.getElementById('online-employees').textContent = data.data.employees || '0';
                    document.getElementById('online-attendance').textContent = data.data.attendance || '0';
                }
            });
        }

        // Sync data
        function syncData(type, direction) {
            if (!isApiConnected) {
                logMessage('⚠️ Please test API connection first');
                return;
            }

            const batchSize = parseInt(document.getElementById('batch-size').value);
            const dateRange = document.getElementById('date-range').value;
            
            showProgress(true);
            logMessage(`🔄 Starting ${type} sync: ${direction}`);
            
            if (direction === 'upload') {
                uploadData(type, batchSize, dateRange);
            } else {
                downloadData(type, batchSize, dateRange);
            }
        }

        // Upload data to online
        function uploadData(type, batchSize, dateRange) {
            updateProgress(10, 'Fetching local data...');
            
            fetch(`api_sync_client.php?action=get_${type}&batch_size=${batchSize}&date_range=${dateRange}`)
                .then(response => response.json())
                .then(localData => {
                    if (localData.success) {
                        updateProgress(30, 'Uploading to online server...');
                        
                        const payload = {};
                        payload[type] = localData.data;
                        
                        return fetch(apiUrl + '?endpoint=' + type, {
                            method: 'POST',
                            headers: {
                                'X-API-Key': apiKey,
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify(payload)
                        });
                    } else {
                        throw new Error('Failed to fetch local data');
                    }
                })
                .then(response => response.json())
                .then(result => {
                    updateProgress(100, 'Upload completed');
                    
                    if (result.status === 'success') {
                        logMessage(`✅ Upload completed: ${result.data.created} created, ${result.data.updated} updated, ${result.data.errors} errors`);
                    } else {
                        logMessage(`❌ Upload failed: ${result.message}`);
                    }
                    
                    showProgress(false);
                    refreshStats();
                })
                .catch(error => {
                    logMessage(`❌ Upload error: ${error.message}`);
                    showProgress(false);
                });
        }

        // Download data from online
        function downloadData(type, batchSize, dateRange) {
            updateProgress(10, 'Fetching online data...');
            
            let url = apiUrl + '?endpoint=' + type + '&limit=' + batchSize;
            if (type === 'attendance' && dateRange !== 'all') {
                const days = parseInt(dateRange);
                const dateFrom = new Date();
                dateFrom.setDate(dateFrom.getDate() - days);
                url += '&date_from=' + dateFrom.toISOString().split('T')[0];
            }
            
            fetch(url, {
                headers: { 'X-API-Key': apiKey }
            })
            .then(response => response.json())
            .then(onlineData => {
                if (onlineData.status === 'success') {
                    updateProgress(50, 'Saving to local database...');
                    
                    return fetch(`api_sync_client.php?action=save_${type}`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ data: onlineData.data[type] })
                    });
                } else {
                    throw new Error('Failed to fetch online data: ' + onlineData.message);
                }
            })
            .then(response => response.json())
            .then(result => {
                updateProgress(100, 'Download completed');
                
                if (result.success) {
                    logMessage(`✅ Download completed: ${result.created} created, ${result.updated} updated, ${result.errors} errors`);
                } else {
                    logMessage(`❌ Download failed: ${result.message}`);
                }
                
                showProgress(false);
                refreshStats();
            })
            .catch(error => {
                logMessage(`❌ Download error: ${error.message}`);
                showProgress(false);
            });
        }

        // Full sync
        function fullSync(direction) {
            if (!confirm(`Are you sure you want to perform a full ${direction}?\nThis will sync both employees and attendance data.`)) {
                return;
            }
            
            logMessage(`🔄 Starting full ${direction} sync...`);
            
            // Sync employees first, then attendance
            syncData('employees', direction);
            
            setTimeout(() => {
                syncData('attendance', direction);
            }, 2000);
        }

        // Show/hide progress bar
        function showProgress(show) {
            const container = document.querySelector('.progress-container');
            container.style.display = show ? 'block' : 'none';
        }

        // Update progress bar
        function updateProgress(percent, status) {
            document.getElementById('sync-progress').style.width = percent + '%';
            document.getElementById('sync-status').textContent = status;
        }

        // Log message
        function logMessage(message) {
            const log = document.getElementById('sync-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            log.appendChild(logEntry);
            log.scrollTop = log.scrollHeight;
        }

        // Clear log
        function clearLog() {
            document.getElementById('sync-log').innerHTML = '<div class="text-muted">API sync operations will be logged here...</div>';
        }

        // Export log
        function exportLog() {
            const logContent = document.getElementById('sync-log').innerText;
            const blob = new Blob([logContent], { type: 'text/plain' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'sync_log_' + new Date().toISOString().split('T')[0] + '.txt';
            a.click();
            window.URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>
