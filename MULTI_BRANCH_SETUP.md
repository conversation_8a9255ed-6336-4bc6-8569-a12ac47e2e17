# Multi-Branch Biometric System Setup Guide

## 🏢 Branch Configuration

Your biometric system has been configured for **3 branches**:

1. **Ajwa Garden Multan** (Code: AGM)
2. **Ajwa Garden Bahawalpur** (Code: AGB) 
3. **Ajwa Garden Sahiwal** (Code: AGS)

## 📋 Setup Instructions

### 1. Database Setup
Run the SQL script to add branch support:
```sql
-- Execute the file: database/branches_update.sql
```

This will:
- Create the `tbl_branches` table
- Add branch_id columns to existing tables
- Insert the 3 branch locations
- Create branch-specific access areas

### 2. User Branch Assignment
- **Admin users** can access all branches
- **Regular users** can be assigned to specific branches
- Branch assignments are stored in the `assigned_branches` field

### 3. Branch-Specific Data
All data is now filtered by branch:
- **Personnel/Employees** are assigned to specific branches
- **Attendance logs** are tracked per branch
- **Access areas** are branch-specific
- **Reports** show branch-filtered data

## 🎛️ Dashboard Features

### Branch Selector
- **Top navigation** shows current branch
- **Dropdown menu** allows switching between assigned branches
- **Real-time updates** when switching branches

### Branch-Specific Statistics
- **Total Employees** per branch
- **Active Employees** per branch
- **Today's Attendance** per branch
- **Weekly attendance charts** per branch

### Branch Management (Admin Only)
- **Overview of all branches**
- **Statistics comparison**
- **Branch status monitoring**
- Access via: `branch_management.php`

## 🔧 Technical Implementation

### Files Modified/Created:
1. **dashboard_enhanced.php** - Main dashboard with branch support
2. **api/dashboard_data.php** - Branch-filtered data API
3. **api/switch_branch.php** - Branch switching functionality
4. **branch_management.php** - Branch overview page
5. **database/branches_update.sql** - Database schema updates

### Session Management:
- `$_SESSION['CURRENT_BRANCH_ID']` - Stores selected branch
- Automatic branch filtering in all queries
- Branch access control for users

### Database Schema:
```sql
-- New table
tbl_branches (branch_id, branch_name, branch_code, branch_location, branch_status)

-- Added columns
tbl_personnel.branch_id
tbl_in_out.branch_id  
tbl_arealist.branch_id
tb_user.branch_id
tb_user.assigned_branches
```

## 🚀 Usage Instructions

### For Administrators:
1. **Login** with admin credentials
2. **Access Branch Management** from navigation
3. **View all branch statistics** and switch between branches
4. **Assign users** to specific branches in user settings

### For Regular Users:
1. **Login** with user credentials
2. **Select branch** from dropdown (if assigned to multiple)
3. **View branch-specific data** in dashboard
4. **All operations** are filtered to selected branch

### Branch Switching:
1. **Click branch dropdown** in navigation
2. **Select desired branch** from list
3. **Dashboard automatically updates** with branch data
4. **All subsequent operations** use selected branch

## 📊 Data Separation

Each branch maintains separate:
- **Employee records**
- **Attendance logs** 
- **Access areas**
- **Reports and analytics**

## 🔒 Security Features

- **Branch access control** - Users only see assigned branches
- **Data isolation** - Branch data is completely separated
- **Admin oversight** - Admins can access all branches
- **Session-based** branch selection

## 🎨 UI Features

- **Modern glass-morphism design**
- **Real-time branch switching**
- **Animated statistics**
- **Responsive layout**
- **Branch-specific branding**

## 📱 Mobile Responsive

The dashboard is fully responsive and works on:
- **Desktop computers**
- **Tablets**
- **Mobile phones**
- **All modern browsers**

## 🔄 Auto-Refresh

- **Real-time updates** every 30 seconds
- **Manual refresh** button available
- **Branch-specific data** updates automatically

## 🎯 Next Steps

1. **Run the database update** script
2. **Test branch switching** functionality
3. **Assign users** to appropriate branches
4. **Configure branch-specific** access areas
5. **Train staff** on new branch features

The system is now ready for multi-branch operations with complete data separation and modern dashboard interface!
