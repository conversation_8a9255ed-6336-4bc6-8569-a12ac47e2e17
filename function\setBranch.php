<?php
session_start();
include('../proc/config.php');

header('Content-Type: application/json');

if(isset($_POST['branch_id'])) {
    $branch_id = (int)$_POST['branch_id'];
    
    try {
        // Validate that the branch exists
        $sql = "SELECT * FROM tbl_branches WHERE branch_id = '$branch_id'";
        $result = mysql_query($sql);
        
        if($result && mysql_num_rows($result) > 0) {
            // Branch exists, set it in session
            $_SESSION['CURRENT_BRANCH_ID'] = $branch_id;
            
            $branch_data = mysql_fetch_assoc($result);
            $_SESSION['CURRENT_BRANCH_NAME'] = $branch_data['branch_name'];
            $_SESSION['CURRENT_BRANCH_CODE'] = $branch_data['branch_code'];
            
            echo json_encode(array(
                'success' => true,
                'message' => 'Branch switched successfully',
                'branch_id' => $branch_id,
                'branch_name' => $branch_data['branch_name']
            ));
        } else {
            // Branch doesn't exist, set default
            $_SESSION['CURRENT_BRANCH_ID'] = 1;
            $_SESSION['CURRENT_BRANCH_NAME'] = 'Main Branch';
            $_SESSION['CURRENT_BRANCH_CODE'] = 'MAIN';
            
            echo json_encode(array(
                'success' => false,
                'message' => 'Branch not found, switched to default branch'
            ));
        }
        
    } catch(Exception $e) {
        echo json_encode(array(
            'success' => false,
            'message' => 'Error: ' . $e->getMessage()
        ));
    }
    
} else {
    echo json_encode(array(
        'success' => false,
        'message' => 'Branch ID not provided'
    ));
}
?>
