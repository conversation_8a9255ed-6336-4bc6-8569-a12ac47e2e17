<?php
include('../proc/config.php');

if(isset($_POST['employee_id'])) {
    $employee_id = clean($_POST['employee_id']);
    $biometricsID = clean($_POST['biometricsID']);
    $employeeID = clean($_POST['employeeID']);
    $fullname = clean($_POST['fullname']);
    $datehired = clean($_POST['datehired']);
    $position = clean($_POST['position']);
    $company = clean($_POST['company']);
    $project = clean($_POST['project']);
    $phone = clean($_POST['phone']);
    $address = clean($_POST['address']);
    $timein = clean($_POST['timein']);
    $timeout = clean($_POST['timeout']);
    $branch_id = clean($_POST['branch_id']);
    $schedule = clean($_POST['schedule']);
    
    $response = array();
    
    try {
        $sql = "UPDATE tbl_personnel SET
                EmployeeID='$employeeID',
                AccessID='$biometricsID',
                FullName='$fullname',
                DateHired='$datehired',
                Position='$position',
                AgencyCompany='$company',
                ProjectAssigned='$project',
                ContactNo='$phone',
                branch_id='$branch_id',
                TimeIN='$timein',
                TimeOut='$timeout',
                schedule_dates='$schedule'
                WHERE EntryID='$employee_id'";
        
        $result = mysql_query($sql) or die(mysql_error());
        
        if($result) {
            $response['success'] = true;
            $response['message'] = 'Employee updated successfully';
        } else {
            $response['success'] = false;
            $response['message'] = 'Failed to update employee';
        }
        
    } catch(Exception $e) {
        $response['success'] = false;
        $response['message'] = 'Error: ' . $e->getMessage();
    }
    
    header('Content-Type: application/json');
    echo json_encode($response);
} else {
    $response = array(
        'success' => false,
        'message' => 'Invalid request'
    );
    header('Content-Type: application/json');
    echo json_encode($response);
}
?>
