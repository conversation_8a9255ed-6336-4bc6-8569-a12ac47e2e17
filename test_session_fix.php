<!DOCTYPE html>
<html>
<head>
    <title>Test Session Fix</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .btn { padding: 10px 15px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; text-decoration: none; display: inline-block; }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h3>🔧 Session Fix Test</h3>
        
        <?php
        session_start();
        
        echo "<div class='info'>";
        echo "<h4>Current Session Status:</h4>";
        echo "<p><strong>User ID:</strong> " . (isset($_SESSION['SESS_USER_ID']) ? $_SESSION['SESS_USER_ID'] : 'NOT SET') . "</p>";
        echo "<p><strong>User Name:</strong> " . (isset($_SESSION['SESS_USER_FULL_NAME']) ? $_SESSION['SESS_USER_FULL_NAME'] : 'NOT SET') . "</p>";
        echo "<p><strong>Branch ID:</strong> " . (isset($_SESSION['CURRENT_BRANCH_ID']) ? $_SESSION['CURRENT_BRANCH_ID'] : 'NOT SET') . "</p>";
        echo "<p><strong>Branch Name:</strong> " . (isset($_SESSION['CURRENT_BRANCH_NAME']) ? $_SESSION['CURRENT_BRANCH_NAME'] : 'NOT SET') . "</p>";
        echo "</div>";
        
        if(isset($_SESSION['SESS_USER_ID'])) {
            echo "<div class='success'>";
            echo "<h4>✅ Session Fix Applied Successfully!</h4>";
            echo "<p>The main issue was that <code>session_start()</code> was missing from <code>function/getAccounts.php</code></p>";
            echo "<p>This has been fixed. The employee filtering should now work correctly.</p>";
            echo "</div>";
            
            echo "<h4>Test the Fix:</h4>";
            echo "<p>1. <a href='function/getAccounts.php' target='_blank' class='btn'>Test AJAX Response</a> - Should now show only your branch employees</p>";
            echo "<p>2. <a href='pg_accounts.php' target='_blank' class='btn'>Go to Accounts Page</a> - Should show filtered employees</p>";
            echo "<p>3. <a href='debug_detailed.php' target='_blank' class='btn'>Run Detailed Debug</a> - For further troubleshooting</p>";
            
        } else {
            echo "<div class='error'>";
            echo "<h4>❌ Not Logged In</h4>";
            echo "<p>You need to be logged in to test the session fix.</p>";
            echo "<p><a href='index.php' class='btn'>Go to Login Page</a></p>";
            echo "</div>";
        }
        ?>
        
        <div class="info">
            <h4>What Was Fixed:</h4>
            <ol>
                <li><strong>Added <code>session_start()</code></strong> to <code>function/getAccounts.php</code></li>
                <li><strong>Added session validation</strong> to ensure user is logged in</li>
                <li><strong>Added debug logging</strong> to track session variables</li>
                <li><strong>Enhanced error handling</strong> for better debugging</li>
            </ol>
            
            <h4>Why This Happened:</h4>
            <p>The <code>getAccounts.php</code> file is called via AJAX by the DataTables component to load employee data. Without <code>session_start()</code>, it couldn't access the session variables that contain the current branch ID, so it was showing all employees instead of filtering by branch.</p>
            
            <h4>Expected Result:</h4>
            <p>Now when you're logged in with <strong>Bawalpur branch</strong>, you should only see employees assigned to Bawalpur branch, not Multan employees.</p>
        </div>
        
        <hr>
        <p><strong>Next Steps:</strong></p>
        <ol>
            <li>Clear your browser cache</li>
            <li>Go to the accounts page</li>
            <li>Verify you only see Bawalpur employees</li>
            <li>If you still see Multan employees, run the debug script to check employee branch assignments</li>
        </ol>
    </div>
</body>
</html>
