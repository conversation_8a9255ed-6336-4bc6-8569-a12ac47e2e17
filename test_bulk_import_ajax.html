<!DOCTYPE html>
<html>
<head>
    <title>Bulk Import AJAX Test</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h2>Bulk Import AJAX Test</h2>
    
    <div id="test-results"></div>
    
    <h3>Test 1: Create Sample CSV</h3>
    <button onclick="createSampleCSV()">Create Sample CSV</button>
    <div id="csv-result"></div>
    
    <h3>Test 2: Test Bulk Import</h3>
    <form id="testForm" enctype="multipart/form-data">
        <input type="file" id="testFile" name="excel_file" accept=".csv,.xlsx,.xls"><br><br>
        <label><input type="checkbox" name="auto_generate_ids" value="1" checked> Auto-generate IDs</label><br>
        <label><input type="checkbox" name="skip_duplicates" value="1" checked> Skip duplicates</label><br><br>
        <button type="button" onclick="testBulkImport()">Test Import</button>
    </form>
    
    <div id="import-result"></div>

    <script>
    function createSampleCSV() {
        $('#csv-result').html('Creating sample CSV...');

        $.ajax({
            url: 'create_sample_csv.php',
            type: 'GET',
            dataType: 'json',
            success: function(data) {
                if(data.success) {
                    $('#csv-result').html('✅ Sample CSV created: <a href="' + data.file + '" download>Download</a>');
                } else {
                    $('#csv-result').html('❌ Error: ' + data.message);
                }
            },
            error: function(xhr, status, error) {
                $('#csv-result').html('❌ AJAX Error: ' + error + '<br>Status: ' + status + '<br>Response: ' + xhr.responseText);
            }
        });
    }
    
    function testBulkImport() {
        const fileInput = document.getElementById('testFile');
        if(!fileInput.files.length) {
            alert('Please select a file first');
            return;
        }
        
        const formData = new FormData(document.getElementById('testForm'));
        
        $('#import-result').html('Testing bulk import...');
        
        $.ajax({
            url: 'function/bulkImportEmployees.php',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            dataType: 'json',
            success: function(response) {
                $('#import-result').html('<h4>✅ Success Response:</h4><pre>' + JSON.stringify(response, null, 2) + '</pre>');
            },
            error: function(xhr, status, error) {
                $('#import-result').html('<h4>❌ Error Response:</h4>' +
                    '<p><strong>Status:</strong> ' + status + '</p>' +
                    '<p><strong>Error:</strong> ' + error + '</p>' +
                    '<p><strong>Response Text:</strong></p>' +
                    '<pre>' + xhr.responseText + '</pre>' +
                    '<p><strong>Response Length:</strong> ' + xhr.responseText.length + ' characters</p>'
                );
                
                // Try to identify the issue
                const responseText = xhr.responseText;
                if(responseText.includes('<!DOCTYPE') || responseText.includes('<html')) {
                    $('#import-result').append('<p><strong>Issue:</strong> Server returned HTML instead of JSON (likely an error page)</p>');
                } else if(responseText.includes('<?php')) {
                    $('#import-result').append('<p><strong>Issue:</strong> PHP code is being output (syntax or include error)</p>');
                } else if(responseText.trim().startsWith('<')) {
                    $('#import-result').append('<p><strong>Issue:</strong> XML/HTML content detected</p>');
                }
            }
        });
    }
    </script>
</body>
</html>
