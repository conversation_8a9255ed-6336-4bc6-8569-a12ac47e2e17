<?php
// Fix employee branch assignments
session_start();
include('proc/config.php');

echo "<h3>🔧 Fix Employee Branch Assignments</h3>";

// Check if branch_id column exists
$check_column = mysql_query("SHOW COLUMNS FROM tbl_personnel LIKE 'branch_id'");
$has_branch_id_column = mysql_num_rows($check_column) > 0;

if(!$has_branch_id_column) {
    echo "<p style='color: red;'>❌ branch_id column doesn't exist in tbl_personnel table</p>";
    echo "<p>Adding branch_id column...</p>";
    
    $add_column_sql = "ALTER TABLE tbl_personnel ADD COLUMN branch_id INT DEFAULT 1";
    if(mysql_query($add_column_sql)) {
        echo "<p style='color: green;'>✅ branch_id column added successfully</p>";
    } else {
        echo "<p style='color: red;'>❌ Error adding branch_id column: " . mysql_error() . "</p>";
        exit();
    }
}

// Check current employee branch assignments
echo "<h4>Current Employee Branch Assignments:</h4>";

$employees_sql = "SELECT EntryID, EmployeeID, FullName, branch_id FROM tbl_personnel ORDER BY EntryID";
$employees_result = mysql_query($employees_sql);

if($employees_result && mysql_num_rows($employees_result) > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr style='background: #f0f0f0;'><th>Entry ID</th><th>Employee ID</th><th>Full Name</th><th>Current Branch ID</th><th>Status</th></tr>";
    
    $null_branch_count = 0;
    $employees_to_fix = array();
    
    while($emp = mysql_fetch_assoc($employees_result)) {
        $branch_status = '';
        $row_style = '';
        
        if(is_null($emp['branch_id']) || $emp['branch_id'] == '' || $emp['branch_id'] == 0) {
            $branch_status = '❌ Needs Fix';
            $row_style = 'style="background: #f8d7da;"';
            $null_branch_count++;
            $employees_to_fix[] = $emp['EntryID'];
        } else {
            $branch_status = '✅ OK';
            $row_style = 'style="background: #d4edda;"';
        }
        
        echo "<tr $row_style>";
        echo "<td>" . $emp['EntryID'] . "</td>";
        echo "<td>" . $emp['EmployeeID'] . "</td>";
        echo "<td>" . $emp['FullName'] . "</td>";
        echo "<td>" . ($emp['branch_id'] ?: 'NULL') . "</td>";
        echo "<td>$branch_status</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<p><strong>Summary:</strong> $null_branch_count employees need branch assignment</p>";
    
    // Fix employees with null/empty branch_id
    if($null_branch_count > 0) {
        echo "<h4>Fix Options:</h4>";
        
        if(isset($_POST['fix_branches'])) {
            $default_branch_id = (int)$_POST['default_branch_id'];
            
            echo "<p>Assigning all employees without branch to Branch ID: $default_branch_id</p>";
            
            $fix_sql = "UPDATE tbl_personnel SET branch_id = '$default_branch_id' WHERE branch_id IS NULL OR branch_id = '' OR branch_id = 0";
            
            if(mysql_query($fix_sql)) {
                $affected_rows = mysql_affected_rows();
                echo "<p style='color: green;'>✅ Fixed $affected_rows employees successfully!</p>";
                echo "<script>setTimeout(() => window.location.reload(), 2000);</script>";
            } else {
                echo "<p style='color: red;'>❌ Error fixing employees: " . mysql_error() . "</p>";
            }
        } else {
            // Show available branches
            echo "<form method='POST' style='background: #f8f9fa; padding: 15px; border-radius: 4px; margin: 20px 0;'>";
            echo "<h5>Assign Default Branch:</h5>";
            echo "<p>Select which branch to assign to employees without branch assignment:</p>";
            
            echo "<select name='default_branch_id' required>";
            $branches_sql = "SELECT * FROM tbl_branches ORDER BY branch_name";
            $branches_result = mysql_query($branches_sql);
            
            if($branches_result && mysql_num_rows($branches_result) > 0) {
                while($branch = mysql_fetch_assoc($branches_result)) {
                    echo "<option value='" . $branch['branch_id'] . "'>" . $branch['branch_name'] . " (ID: " . $branch['branch_id'] . ")</option>";
                }
            } else {
                echo "<option value='1'>Main Branch (ID: 1)</option>";
            }
            
            echo "</select>";
            echo "<button type='submit' name='fix_branches' style='background: #28a745; color: white; padding: 10px 15px; border: none; border-radius: 4px; margin-left: 10px;'>Fix All Employees</button>";
            echo "</form>";
        }
    }
    
} else {
    echo "<p>No employees found in database</p>";
}

// Show branches
echo "<h4>Available Branches:</h4>";
$branches_sql = "SELECT * FROM tbl_branches ORDER BY branch_id";
$branches_result = mysql_query($branches_sql);

if($branches_result && mysql_num_rows($branches_result) > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr style='background: #f0f0f0;'><th>Branch ID</th><th>Branch Name</th><th>Branch Code</th><th>Employee Count</th></tr>";
    
    while($branch = mysql_fetch_assoc($branches_result)) {
        // Count employees in this branch
        $count_sql = "SELECT COUNT(*) as count FROM tbl_personnel WHERE branch_id = '" . $branch['branch_id'] . "'";
        $count_result = mysql_query($count_sql);
        $count_row = mysql_fetch_assoc($count_result);
        $employee_count = $count_row['count'];
        
        echo "<tr>";
        echo "<td>" . $branch['branch_id'] . "</td>";
        echo "<td>" . $branch['branch_name'] . "</td>";
        echo "<td>" . ($branch['branch_code'] ?? 'N/A') . "</td>";
        echo "<td>$employee_count employees</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: orange;'>⚠️ No branches found. You may need to create branches first.</p>";
}

// Manual employee assignment
echo "<h4>Manual Employee Assignment:</h4>";

if(isset($_POST['assign_employee'])) {
    $employee_id = (int)$_POST['employee_entry_id'];
    $new_branch_id = (int)$_POST['new_branch_id'];
    
    $assign_sql = "UPDATE tbl_personnel SET branch_id = '$new_branch_id' WHERE EntryID = '$employee_id'";
    
    if(mysql_query($assign_sql)) {
        echo "<p style='color: green;'>✅ Employee assigned to new branch successfully!</p>";
        echo "<script>setTimeout(() => window.location.reload(), 1000);</script>";
    } else {
        echo "<p style='color: red;'>❌ Error assigning employee: " . mysql_error() . "</p>";
    }
}

echo "<form method='POST' style='background: #f8f9fa; padding: 15px; border-radius: 4px; margin: 20px 0;'>";
echo "<h5>Assign Individual Employee:</h5>";

echo "<div style='margin: 10px 0;'>";
echo "<label>Select Employee:</label><br>";
echo "<select name='employee_entry_id' required style='width: 300px; padding: 5px;'>";

// Reset employees result
$employees_result = mysql_query($employees_sql);
if($employees_result) {
    while($emp = mysql_fetch_assoc($employees_result)) {
        echo "<option value='" . $emp['EntryID'] . "'>" . $emp['FullName'] . " (ID: " . $emp['EmployeeID'] . ")</option>";
    }
}

echo "</select>";
echo "</div>";

echo "<div style='margin: 10px 0;'>";
echo "<label>Assign to Branch:</label><br>";
echo "<select name='new_branch_id' required style='width: 300px; padding: 5px;'>";

// Reset branches result
$branches_result = mysql_query($branches_sql);
if($branches_result) {
    while($branch = mysql_fetch_assoc($branches_result)) {
        echo "<option value='" . $branch['branch_id'] . "'>" . $branch['branch_name'] . " (ID: " . $branch['branch_id'] . ")</option>";
    }
}

echo "</select>";
echo "</div>";

echo "<button type='submit' name='assign_employee' style='background: #007bff; color: white; padding: 10px 15px; border: none; border-radius: 4px;'>Assign Employee</button>";
echo "</form>";

echo "<hr>";
echo "<p><a href='debug_branch_filtering.php'>🔍 Debug Branch Filtering</a> | <a href='pg_accounts.php'>👥 Go to Accounts Page</a></p>";

echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; }";
echo "table { border-collapse: collapse; margin: 10px 0; }";
echo "th, td { padding: 8px; border: 1px solid #ccc; text-align: left; }";
echo "th { background: #f0f0f0; }";
echo "</style>";
?>
