<?php
session_start();

// Set test session if not logged in
if(!isset($_SESSION['SESS_USER_ID'])) {
    $_SESSION['SESS_USER_ID'] = 1;
    $_SESSION['CURRENT_BRANCH_ID'] = 1;
}

// Get branch info
include('proc/config.php');

// Check if tbl_branch exists, if not use default
$current_branch = array('branch_name' => 'Main Branch');
try {
    $branch_query = mysql_query("SELECT * FROM tbl_branch WHERE branch_id = 1");
    if($branch_query) {
        $branch_result = mysql_fetch_assoc($branch_query);
        if($branch_result) {
            $current_branch = $branch_result;
        }
    }
} catch(Exception $e) {
    // Table doesn't exist, use default
    $current_branch = array('branch_name' => 'Main Branch');
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Modal Debug Test</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
</head>
<body>
    <div class="container mt-5">
        <h2>Modal Debug Test</h2>
        
        <div class="alert alert-info">
            <h5>Debug Information:</h5>
            <p><strong>Session User ID:</strong> <?php echo $_SESSION['SESS_USER_ID']; ?></p>
            <p><strong>Branch ID:</strong> <?php echo $_SESSION['CURRENT_BRANCH_ID']; ?></p>
            <p><strong>Branch Name:</strong> <?php echo $current_branch['branch_name']; ?></p>
        </div>
        
        <button class="btn btn-primary" id="showBulkImportModal">
            <i class="fa fa-upload"></i> Open Bulk Import Modal
        </button>
        
        <div class="mt-3">
            <button onclick="testConsole()" class="btn btn-info">Test Console</button>
            <button onclick="testJQuery()" class="btn btn-success">Test jQuery</button>
            <button onclick="testCheckboxes()" class="btn btn-warning">Test Checkboxes</button>
        </div>
        
        <div id="debug-output" class="mt-3"></div>
    </div>

    <!-- Bulk Import Modal -->
    <div class="modal fade" id="BulkImportModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fa fa-upload"></i> Bulk Import Employees from Excel
                    </h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="bulkImportForm" enctype="multipart/form-data">
                        <!-- Template Download -->
                        <div class="form-group">
                            <label class="form-label"><i class="fa fa-download"></i> Step 1: Download Template</label>
                            <div>
                                <button type="button" id="downloadTemplate" class="btn btn-success">
                                    <i class="fa fa-download"></i> Download Excel Template
                                </button>
                                <small class="form-text text-muted">Download the template with the correct column headers</small>
                            </div>
                        </div>

                        <!-- File Upload -->
                        <div class="form-group">
                            <label class="form-label"><i class="fa fa-upload"></i> Step 2: Upload Completed Excel File *</label>
                            <input type="file" name="excel_file" id="excelFile" class="form-control" accept=".xlsx,.xls,.csv" required>
                            <small class="form-text text-muted">Supported formats: .xlsx, .xls, .csv (Max size: 10MB)</small>
                        </div>

                        <!-- Import Options -->
                        <div class="form-group">
                            <label class="form-label"><i class="fa fa-cogs"></i> Import Options</label>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="skip_duplicates" value="1" checked>
                                    Skip duplicate Employee IDs (recommended)
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="auto_generate_ids" value="1" checked>
                                    Auto-generate missing Employee/Biometric IDs
                                </label>
                            </div>
                        </div>

                        <!-- Branch Info -->
                        <div class="alert alert-warning">
                            <i class="fa fa-building"></i> <strong>Branch Assignment:</strong>
                            All imported employees will be assigned to your current branch: <strong><?php echo $current_branch['branch_name']; ?></strong>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="bulkImportBtn" form="bulkImportForm">
                        <i class="fa fa-upload"></i> Import Employees
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
    // Test functions
    function testConsole() {
        console.log('Console test - this should appear in browser console');
        $('#debug-output').html('<div class="alert alert-success">Console test executed - check browser console (F12)</div>');
    }
    
    function testJQuery() {
        const jQueryVersion = $.fn.jquery;
        console.log('jQuery version:', jQueryVersion);
        $('#debug-output').html('<div class="alert alert-info">jQuery version: ' + jQueryVersion + '</div>');
    }
    
    function testCheckboxes() {
        const skipDuplicates = $('input[name="skip_duplicates"]').length;
        const autoGenerateIds = $('input[name="auto_generate_ids"]').length;
        const skipChecked = $('input[name="skip_duplicates"]').is(':checked');
        const autoChecked = $('input[name="auto_generate_ids"]').is(':checked');
        
        console.log('Checkbox test results:');
        console.log('Skip duplicates found:', skipDuplicates, 'checked:', skipChecked);
        console.log('Auto generate found:', autoGenerateIds, 'checked:', autoChecked);
        
        let output = '<div class="alert alert-info">';
        output += '<h6>Checkbox Test Results:</h6>';
        output += '<p>Skip duplicates: Found ' + skipDuplicates + ', Checked: ' + skipChecked + '</p>';
        output += '<p>Auto generate: Found ' + autoGenerateIds + ', Checked: ' + autoChecked + '</p>';
        output += '</div>';
        
        $('#debug-output').html(output);
    }
    
    // Modal event handlers
    $(document).on('click', '#showBulkImportModal', function(){
        console.log('Modal button clicked');
        $('#debug-output').html('<div class="alert alert-primary">Modal button clicked - check console</div>');
        
        // Set checkboxes before showing modal
        setTimeout(function() {
            $('input[name="skip_duplicates"]').prop('checked', true);
            $('input[name="auto_generate_ids"]').prop('checked', true);
            console.log('Checkboxes set before modal show');
        }, 100);
        
        $('#BulkImportModal').modal('show');
    });

    $('#BulkImportModal').on('shown.bs.modal', function () {
        console.log('Bulk Import Modal shown - setting default checkbox states');
        
        // Set default checkbox states
        $('input[name="skip_duplicates"]').prop('checked', true);
        $('input[name="auto_generate_ids"]').prop('checked', true);
        
        // Clear any previous file selection
        $('input[name="excel_file"]').val('');
        
        // Debug: Check if checkboxes are actually checked
        console.log('Skip duplicates checked:', $('input[name="skip_duplicates"]').is(':checked'));
        console.log('Auto generate IDs checked:', $('input[name="auto_generate_ids"]').is(':checked'));
        
        // Update debug output
        $('#debug-output').html('<div class="alert alert-success">Modal shown - checkboxes should be checked. Check console for details.</div>');
    });
    
    // Download template handler
    $(document).on('click', '#downloadTemplate', function(){
        console.log('Download template clicked');
        window.location.href = 'function/downloadTemplate.php';
    });
    
    // Document ready
    $(document).ready(function() {
        console.log('Document ready - debug modal page loaded');
        
        // Set default checkbox states
        setTimeout(function() {
            $('input[name="skip_duplicates"]').prop('checked', true);
            $('input[name="auto_generate_ids"]').prop('checked', true);
            console.log('Default checkbox states set on page load');
        }, 500);
    });
    </script>
</body>
</html>
