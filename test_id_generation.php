<?php
// Test ID generation functionality
include('proc/config.php');

echo "<h3>Testing ID Generation</h3>";

// Include the generation functions
include('function/generateEmployeeIDs.php');

echo "<h4>Current Database State:</h4>";

// Show current highest IDs
$sql = "SELECT AccessID FROM tbl_personnel WHERE AccessID LIKE 'B%' ORDER BY CAST(SUBSTRING(AccessID, 2) AS UNSIGNED) DESC LIMIT 5";
$result = mysql_query($sql);

echo "<strong>Current Biometric IDs (B-series):</strong><br>";
if($result && mysql_num_rows($result) > 0) {
    while($row = mysql_fetch_assoc($result)) {
        echo "- " . $row['AccessID'] . "<br>";
    }
} else {
    echo "No B-series IDs found<br>";
}

$sql = "SELECT EmployeeID FROM tbl_personnel WHERE EmployeeID REGEXP '^[0-9]+$' AND CAST(EmployeeID AS UNSIGNED) >= 1001 ORDER BY CAST(EmployeeID AS UNSIGNED) DESC LIMIT 5";
$result = mysql_query($sql);

echo "<br><strong>Current Employee IDs (1001+ series):</strong><br>";
if($result && mysql_num_rows($result) > 0) {
    while($row = mysql_fetch_assoc($result)) {
        echo "- " . $row['EmployeeID'] . "<br>";
    }
} else {
    echo "No 1001+ series IDs found<br>";
}

echo "<h4>Generated IDs:</h4>";

// Test ID generation
for($i = 1; $i <= 5; $i++) {
    $biometricID = generateUniqueBiometricID();
    $employeeID = generateUniqueEmployeeID();
    
    echo "<strong>Test $i:</strong><br>";
    echo "Biometric ID: $biometricID<br>";
    echo "Employee ID: $employeeID<br>";
    echo "Biometric Unique: " . (isBiometricIDUnique($biometricID) ? 'Yes' : 'No') . "<br>";
    echo "Employee Unique: " . (isEmployeeIDUnique($employeeID) ? 'Yes' : 'No') . "<br><br>";
}

echo "<h4>AJAX Test:</h4>";
echo "<button onclick='testAjax()'>Test AJAX Generation</button>";
echo "<div id='ajaxResult'></div>";
?>

<script>
function testAjax() {
    fetch('function/generateEmployeeIDs.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'action=generate_ids'
    })
    .then(response => response.json())
    .then(data => {
        document.getElementById('ajaxResult').innerHTML = 
            '<strong>AJAX Result:</strong><br>' +
            'Success: ' + data.success + '<br>' +
            'Biometric ID: ' + (data.biometric_id || 'N/A') + '<br>' +
            'Employee ID: ' + (data.employee_id || 'N/A') + '<br>' +
            'Message: ' + (data.message || 'N/A');
    })
    .catch(error => {
        document.getElementById('ajaxResult').innerHTML = 'Error: ' + error;
    });
}
</script>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
button { padding: 10px 15px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
button:hover { background: #0056b3; }
#ajaxResult { margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 4px; }
</style>
