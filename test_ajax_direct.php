<?php
// Direct test of the AJAX endpoint to see exactly what's returned
require_once('proc/config.php');

echo "<h2>Direct AJAX Test</h2>";

// Test data from your online API
$test_data = [
    [
        'EmployeeID' => '12345',
        'AccessID' => 'B001',
        'FullName' => '<PERSON>',
        'Position' => 'Web Dev',
        'AgencyCompany' => 'ESI',
        'ContactNo' => '',
        'DateHired' => '2017-08-14',
        'Status' => '',
        'TimeIN' => '08:00:00',
        'TimeOut' => '17:00:00'
    ]
];

echo "<h3>Test 1: Direct Function Call</h3>";

// Test the function directly
function testSaveEmployeesToLocal($employees) {
    global $link;
    $created = 0;
    $updated = 0;
    $errors = 0;
    
    if (!is_array($employees)) {
        return ['success' => false, 'message' => 'Invalid employee data format'];
    }
    
    foreach ($employees as $employee) {
        if (!isset($employee['AccessID']) || !$employee['AccessID']) {
            $errors++;
            continue;
        }
        
        // Clean the AccessID (remove whitespace and newlines)
        $access_id = mysqli_real_escape_string($link, trim($employee['AccessID']));
        
        // Check if exists
        $check_sql = "SELECT COUNT(*) as count FROM tbl_personnel WHERE AccessID = '$access_id'";
        $check_result = @mysqli_query($link, $check_sql);
        $exists = false;
        
        if ($check_result) {
            $check_row = mysqli_fetch_assoc($check_result);
            $exists = $check_row['count'] > 0;
        } else {
            $errors++;
            continue;
        }
        
        // Prepare data
        $employee_id = mysqli_real_escape_string($link, $employee['EmployeeID'] ?? '');
        $full_name = mysqli_real_escape_string($link, $employee['FullName'] ?? '');
        $position = mysqli_real_escape_string($link, $employee['Position'] ?? '');
        $agency = mysqli_real_escape_string($link, $employee['AgencyCompany'] ?? '');
        $contact = mysqli_real_escape_string($link, $employee['ContactNo'] ?? '');
        $date_hired = mysqli_real_escape_string($link, $employee['DateHired'] ?? '');
        $status = mysqli_real_escape_string($link, $employee['Status'] ?? '');
        $time_in = mysqli_real_escape_string($link, $employee['TimeIN'] ?? '');
        $time_out = mysqli_real_escape_string($link, $employee['TimeOut'] ?? '');
        
        if ($exists) {
            $sql = "UPDATE tbl_personnel SET 
                EmployeeID = '$employee_id',
                FullName = '$full_name',
                Position = '$position',
                AgencyCompany = '$agency',
                ContactNo = '$contact',
                DateHired = '$date_hired',
                Status = '$status',
                TimeIN = '$time_in',
                TimeOut = '$time_out'
                WHERE AccessID = '$access_id'";
            
            if (@mysqli_query($link, $sql)) {
                $updated++;
            } else {
                $errors++;
            }
        } else {
            $sql = "INSERT INTO tbl_personnel (EmployeeID, AccessID, FullName, Position, AgencyCompany, ContactNo, DateHired, Status, TimeIN, TimeOut) 
                VALUES ('$employee_id', '$access_id', '$full_name', '$position', '$agency', '$contact', '$date_hired', '$status', '$time_in', '$time_out')";
            
            if (@mysqli_query($link, $sql)) {
                $created++;
            } else {
                $errors++;
            }
        }
    }
    
    return [
        'success' => $errors === 0,
        'created' => $created,
        'updated' => $updated,
        'errors' => $errors
    ];
}

$result = testSaveEmployeesToLocal($test_data);
echo "Direct function result: <pre>" . print_r($result, true) . "</pre>";
echo "Direct function JSON: <pre>" . json_encode($result) . "</pre>";

echo "<h3>Test 2: Simulate AJAX Call</h3>";

// Simulate the exact AJAX call
$_SERVER['REQUEST_METHOD'] = 'POST';
$_GET['action'] = 'save_employees';

// Capture what would be sent to the AJAX endpoint
$ajax_input = json_encode(['data' => $test_data]);
echo "AJAX input: <pre>" . htmlspecialchars($ajax_input) . "</pre>";

// Simulate the AJAX endpoint by capturing all output
ob_start();

// Set the JSON header like the real endpoint
header('Content-Type: application/json');

// Simulate reading POST data
$input = json_decode($ajax_input, true);

if (!$input || !isset($input['data'])) {
    echo json_encode(['success' => false, 'message' => 'Invalid input data']);
} else {
    // Call the same function
    $ajax_result = testSaveEmployeesToLocal($input['data']);
    echo json_encode($ajax_result);
}

$ajax_output = ob_get_clean();

echo "AJAX output length: " . strlen($ajax_output) . " characters<br>";
echo "AJAX output: <pre>" . htmlspecialchars($ajax_output) . "</pre>";

// Check for any non-JSON content
if (substr($ajax_output, 0, 1) !== '{') {
    echo "<div style='color: red;'>❌ Output doesn't start with JSON!</div>";
    echo "First 50 characters: <pre>" . htmlspecialchars(substr($ajax_output, 0, 50)) . "</pre>";
    echo "Character codes: <pre>";
    for ($i = 0; $i < min(20, strlen($ajax_output)); $i++) {
        echo $i . ": " . ord($ajax_output[$i]) . " (" . $ajax_output[$i] . ")\n";
    }
    echo "</pre>";
} else {
    echo "<div style='color: green;'>✅ Output starts with JSON</div>";
    
    // Try to parse it
    $parsed = json_decode($ajax_output);
    if ($parsed === null) {
        echo "<div style='color: red;'>❌ JSON parsing failed: " . json_last_error_msg() . "</div>";
    } else {
        echo "<div style='color: green;'>✅ JSON parsing successful</div>";
    }
}

echo "<h3>Test 3: Check for Hidden Output</h3>";

// Check if there's any output buffering or hidden content
if (ob_get_level() > 0) {
    echo "⚠️ Output buffering is active (level: " . ob_get_level() . ")<br>";
} else {
    echo "✅ No output buffering<br>";
}

// Check for any headers already sent
if (headers_sent($file, $line)) {
    echo "⚠️ Headers already sent from $file:$line<br>";
} else {
    echo "✅ No headers sent yet<br>";
}

echo "<hr>";
echo "<p><a href='simple_sync_client.php'>← Back to Sync Client</a></p>";
?>
