<?php
session_start();
include('../proc/config.php');

// Check if user is logged in
if(!isset($_SESSION['SESS_USER_ID'])) {
    header('Location: ../index.php');
    exit();
}

// Get current branch info
$current_branch_id = isset($_SESSION['CURRENT_BRANCH_ID']) ? $_SESSION['CURRENT_BRANCH_ID'] : 1;
$current_branch_name = isset($_SESSION['CURRENT_BRANCH_NAME']) ? $_SESSION['CURRENT_BRANCH_NAME'] : 'Main Branch';

// Create CSV template
$filename = 'employee_import_template_' . date('Y-m-d') . '.csv';

// Set headers for CSV download
header('Content-Type: text/csv');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
header('Expires: 0');

// Open output stream
$output = fopen('php://output', 'w');

// Write CSV header
$headers = array(
    'Full Name *',
    'Position *', 
    'Phone Number',
    'Address',
    'Date Hired (YYYY-MM-DD) *',
    'Employee ID (leave blank for auto-generate)',
    'Biometric ID (leave blank for auto-generate)',
    'Company',
    'Project Assignment',
    'Time In (HH:MM)',
    'Time Out (HH:MM)',
    'Schedule (Monday,Tuesday,etc)'
);

fputcsv($output, $headers);

// Write instructions row
$instructions = array(
    'Enter employee full name',
    'Job title or position',
    'Contact phone number',
    'Home or office address',
    'Hiring date in YYYY-MM-DD format',
    'Leave blank to auto-generate',
    'Leave blank to auto-generate',
    'Company or agency name',
    'Project or department',
    'Default check-in time',
    'Default check-out time',
    'Work days separated by commas'
);

fputcsv($output, $instructions);

// Write sample data rows
$sampleData = array(
    array(
        'John Doe',
        'Manager',
        '+1234567890',
        '123 Main Street, City',
        date('Y-m-d'),
        '', // Will be auto-generated
        '', // Will be auto-generated
        'ABC Company',
        'Project Alpha',
        '09:00',
        '17:00',
        'Monday,Tuesday,Wednesday,Thursday,Friday'
    ),
    array(
        'Jane Smith',
        'Assistant',
        '+1234567891',
        '456 Oak Avenue, City',
        date('Y-m-d'),
        '', // Will be auto-generated
        '', // Will be auto-generated
        'XYZ Corporation',
        'Project Beta',
        '08:30',
        '16:30',
        'Monday,Tuesday,Wednesday,Thursday,Friday,Saturday'
    ),
    array(
        'Bob Johnson',
        'Security Guard',
        '+1234567892',
        '789 Pine Road, City',
        date('Y-m-d'),
        '', // Will be auto-generated
        '', // Will be auto-generated
        'Security Services Ltd',
        'Night Shift',
        '22:00',
        '06:00',
        'Monday,Tuesday,Wednesday,Thursday,Friday,Saturday,Sunday'
    )
);

// Write sample data
foreach($sampleData as $row) {
    fputcsv($output, $row);
}

// Write additional information as comments
fputcsv($output, array());
fputcsv($output, array('=== IMPORTANT NOTES ==='));
fputcsv($output, array('1. Fields marked with * are required'));
fputcsv($output, array('2. Delete the instruction and sample rows before importing'));
fputcsv($output, array('3. Date format must be YYYY-MM-DD (e.g., 2024-01-15)'));
fputcsv($output, array('4. Time format must be HH:MM (e.g., 09:00, 17:30)'));
fputcsv($output, array('5. Schedule days should be separated by commas'));
fputcsv($output, array('6. Leave Employee ID and Biometric ID blank for auto-generation'));
fputcsv($output, array('7. All employees will be assigned to: ' . $current_branch_name));
fputcsv($output, array('8. Maximum file size: 10MB'));
fputcsv($output, array('9. Supported formats: .xlsx, .xls, .csv'));
fputcsv($output, array());
fputcsv($output, array('=== COLUMN DESCRIPTIONS ==='));
fputcsv($output, array('Full Name: Complete name of the employee'));
fputcsv($output, array('Position: Job title or role'));
fputcsv($output, array('Phone Number: Contact number with country code'));
fputcsv($output, array('Address: Complete address'));
fputcsv($output, array('Date Hired: Employment start date'));
fputcsv($output, array('Employee ID: Unique identifier (auto-generated if blank)'));
fputcsv($output, array('Biometric ID: Fingerprint scanner ID (auto-generated if blank)'));
fputcsv($output, array('Company: Employer or agency name'));
fputcsv($output, array('Project Assignment: Current project or department'));
fputcsv($output, array('Time In: Default check-in time'));
fputcsv($output, array('Time Out: Default check-out time'));
fputcsv($output, array('Schedule: Working days of the week'));

// Close output stream
fclose($output);
exit();
?>
