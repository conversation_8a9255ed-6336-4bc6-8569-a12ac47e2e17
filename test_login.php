<?php
// Test login functionality and create default user if needed
require_once('proc/config.php');

echo "<h2>Login System Test</h2>";

// Check if tb_user table exists and has users
$check_table = "SHOW TABLES LIKE 'tb_user'";
$result = mysql_query($check_table);

if(mysql_num_rows($result) == 0) {
    echo "<p style='color: red;'>❌ Table 'tb_user' does not exist!</p>";
    
    // Create the table
    $create_table = "CREATE TABLE `tb_user` (
        `IDno` int(11) NOT NULL AUTO_INCREMENT,
        `user_name` varchar(50) NOT NULL,
        `pass_word` varchar(255) NOT NULL,
        `full_name` varchar(100) NOT NULL,
        `userlevel` varchar(20) DEFAULT 'user',
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`IDno`),
        UNIQUE KEY `user_name` (`user_name`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8";
    
    if(mysql_query($create_table)) {
        echo "<p style='color: green;'>✅ Table 'tb_user' created successfully!</p>";
    } else {
        echo "<p style='color: red;'>❌ Failed to create table: " . mysql_error() . "</p>";
    }
} else {
    echo "<p style='color: green;'>✅ Table 'tb_user' exists</p>";
}

// Check for existing users
$check_users = "SELECT COUNT(*) as user_count FROM tb_user";
$result = mysql_query($check_users);
$row = mysql_fetch_assoc($result);

echo "<p>👥 Current users in database: " . $row['user_count'] . "</p>";

// If no users exist, create a default admin user
if($row['user_count'] == 0) {
    $default_username = 'admin';
    $default_password = 'admin123';
    $password_hash = md5($default_password);
    $full_name = 'System Administrator';
    
    $insert_user = "INSERT INTO tb_user (user_name, pass_word, full_name, userlevel) 
                    VALUES ('$default_username', '$password_hash', '$full_name', 'admin')";
    
    if(mysql_query($insert_user)) {
        echo "<p style='color: green;'>✅ Default admin user created!</p>";
        echo "<p><strong>Username:</strong> admin</p>";
        echo "<p><strong>Password:</strong> admin123</p>";
    } else {
        echo "<p style='color: red;'>❌ Failed to create default user: " . mysql_error() . "</p>";
    }
} else {
    // Show existing users
    echo "<h3>Existing Users:</h3>";
    $get_users = "SELECT user_name, full_name, userlevel FROM tb_user";
    $result = mysql_query($get_users);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th style='padding: 8px;'>Username</th><th style='padding: 8px;'>Full Name</th><th style='padding: 8px;'>Level</th></tr>";
    
    while($user = mysql_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td style='padding: 8px;'>" . $user['user_name'] . "</td>";
        echo "<td style='padding: 8px;'>" . $user['full_name'] . "</td>";
        echo "<td style='padding: 8px;'>" . $user['userlevel'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

// Test database connection
echo "<h3>Database Connection Test:</h3>";
if($link) {
    echo "<p style='color: green;'>✅ Database connection successful</p>";
    echo "<p>Database: " . DB_DATABASE . "</p>";
    echo "<p>Host: " . DB_HOST . "</p>";
} else {
    echo "<p style='color: red;'>❌ Database connection failed</p>";
}

// Test password hashing
echo "<h3>Password Hash Test:</h3>";
$test_password = "admin123";
$test_hash = md5($test_password);
echo "<p>Password: '$test_password' → Hash: '$test_hash'</p>";

echo "<hr>";
echo "<p><a href='index.php'>← Back to Login</a></p>";
?>
