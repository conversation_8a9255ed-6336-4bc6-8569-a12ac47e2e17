<?php
require_once('proc/config.php');

echo "<h2>Testing Global_MySQL Function</h2>";

// Test if function exists
if (function_exists('Global_MySQL')) {
    echo "✅ Global_MySQL function exists<br>";
    
    // Test the function
    try {
        $result = Global_MySQL('', "SELECT * FROM tb_user LIMIT 1", false);
        echo "✅ Global_MySQL function works<br>";
        
        if ($result && mysql_num_rows($result) > 0) {
            $row = mysql_fetch_assoc($result);
            echo "✅ Query returned data: " . print_r($row, true) . "<br>";
        } else {
            echo "⚠️ Query returned no data<br>";
        }
        
    } catch (Exception $e) {
        echo "❌ Error calling Global_MySQL: " . $e->getMessage() . "<br>";
    }
    
} else {
    echo "❌ Global_MySQL function does not exist<br>";
}

// Test database connection
echo "<h3>Database Connection Test:</h3>";
if ($link) {
    echo "✅ Database connected<br>";
} else {
    echo "❌ Database not connected<br>";
}

// List all defined functions containing 'mysql' or 'Global'
echo "<h3>Available Functions:</h3>";
$functions = get_defined_functions();
$user_functions = $functions['user'];
foreach ($user_functions as $func) {
    if (stripos($func, 'mysql') !== false || stripos($func, 'global') !== false) {
        echo "- " . $func . "<br>";
    }
}
?>
