<!DOCTYPE html>
<html>
<head>
    <title>Checkbox Fix Test</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
    </style>
</head>
<body>
    <h1>Checkbox Fix Test</h1>
    
    <div class="test-section">
        <h2>Test Checkboxes</h2>
        <form id="testForm">
            <div class="checkbox">
                <label>
                    <input type="checkbox" name="skip_duplicates" value="1" checked>
                    Skip duplicate Employee IDs (recommended)
                </label>
            </div>
            <div class="checkbox">
                <label>
                    <input type="checkbox" name="auto_generate_ids" value="1" checked>
                    Auto-generate missing Employee/Biometric IDs
                </label>
            </div>
        </form>
        
        <div id="status" class="mt-3"></div>
        
        <button onclick="checkStatus()">Check Status</button>
        <button onclick="setDefaults()">Set Defaults</button>
        <button onclick="resetForm()">Reset Form</button>
    </div>
    
    <div class="test-section">
        <h2>Test Results</h2>
        <div id="results"></div>
    </div>

    <script>
    function checkStatus() {
        const skipDuplicates = $('input[name="skip_duplicates"]').is(':checked');
        const autoGenerateIds = $('input[name="auto_generate_ids"]').is(':checked');
        
        let status = '<h4>Current Status:</h4>';
        status += '<p>Skip Duplicates: <span class="' + (skipDuplicates ? 'success' : 'error') + '">' + (skipDuplicates ? 'CHECKED ✓' : 'UNCHECKED ✗') + '</span></p>';
        status += '<p>Auto Generate IDs: <span class="' + (autoGenerateIds ? 'success' : 'error') + '">' + (autoGenerateIds ? 'CHECKED ✓' : 'UNCHECKED ✗') + '</span></p>';
        
        $('#status').html(status);
        
        // Log to console
        console.log('Skip Duplicates:', skipDuplicates);
        console.log('Auto Generate IDs:', autoGenerateIds);
        
        return { skipDuplicates, autoGenerateIds };
    }
    
    function setDefaults() {
        $('input[name="skip_duplicates"]').prop('checked', true);
        $('input[name="auto_generate_ids"]').prop('checked', true);
        console.log('Defaults set via JavaScript');
        checkStatus();
    }
    
    function resetForm() {
        $('#testForm')[0].reset();
        console.log('Form reset');
        setTimeout(function() {
            checkStatus();
            // After reset, set defaults again
            setDefaults();
        }, 100);
    }
    
    // Test sequence
    function runTests() {
        let results = '<h4>Test Results:</h4>';
        
        // Test 1: Initial state
        let initial = checkStatus();
        results += '<p>Test 1 - Initial State: ' + (initial.skipDuplicates && initial.autoGenerateIds ? '<span class="success">PASS ✓</span>' : '<span class="error">FAIL ✗</span>') + '</p>';
        
        // Test 2: After reset
        setTimeout(function() {
            resetForm();
            setTimeout(function() {
                let afterReset = checkStatus();
                results += '<p>Test 2 - After Reset + Set Defaults: ' + (afterReset.skipDuplicates && afterReset.autoGenerateIds ? '<span class="success">PASS ✓</span>' : '<span class="error">FAIL ✗</span>') + '</p>';
                $('#results').html(results);
            }, 200);
        }, 100);
    }
    
    // Run tests when page loads
    $(document).ready(function() {
        console.log('Page loaded, running tests...');
        setTimeout(runTests, 500);
    });
    </script>
</body>
</html>
