/*!
 * Bootstrap-checkbox v1.2.14 (http://vsn4ik.github.io/bootstrap-checkbox)
 * Copyright 2013-2015 Vasily A. (https://github.com/vsn4ik)
 * Licensed under the MIT license
 */

"use strict";!function(a){"function"==typeof define&&define.amd?define(["jquery"],a):"object"==typeof exports?module.exports=a(require("jquery")):a(jQuery)}(function(a){function b(b,c){if(this.element=b,this.$element=a(b),this.options=a.extend({},a.fn.checkboxpicker.defaults,c,this.$element.data()),this.$element.closest("label").length)return void console.warn(this.options.warningMessage);this.$group=a.create("div").addClass("btn-group"),this.$buttons=a.create("a","a").addClass("btn");var d=this.options.reverse||""===this.options.reverse;this.$off=this.$buttons.eq(d?1:0),this.$on=this.$buttons.eq(d?0:1),this.init()}a.create=function(){return a(a.map(arguments,function(a){return document.createElement(a)}))},b.prototype={init:function(){this.$element.addClass("hidden"),this.options.offLabel&&this.$off.text(this.options.offLabel),this.options.onLabel&&this.$on.text(this.options.onLabel),this.options.offIconClass&&(this.options.offLabel&&this.$off.prepend("&nbsp;"),a.create("span").addClass(this.options.offIconClass).prependTo(this.$off)),this.options.onIconClass&&(this.options.onLabel&&this.$on.prepend("&nbsp;"),a.create("span").addClass(this.options.onIconClass).prependTo(this.$on)),this.element.checked?(this.$on.addClass("active "+this.options.onClass),this.$off.addClass(this.options.defaultClass)):(this.$off.addClass("active "+this.options.offClass),this.$on.addClass(this.options.defaultClass)),this.options.style&&this.$group.addClass(this.options.style),this.element.title?this.$group.attr("title",this.element.title):(this.options.offTitle&&this.$off.attr("title",this.options.offTitle),this.options.onTitle&&this.$on.attr("title",this.options.onTitle)),this.$group.on("keydown",a.proxy(this,"keydown")),this.$group.on("click","a:not(.active)",a.proxy(this,"click")),this.$element.on("change",a.proxy(this,"toggleChecked")),a(this.element.labels).on("click",a.proxy(this,"focus")),a(this.element.form).on("reset",a.proxy(this,"reset")),this.$group.append(this.$buttons).insertAfter(this.element),this.element.disabled?(this.$buttons.addClass("disabled"),this.options.disabledCursor&&this.$group.css("cursor",this.options.disabledCursor)):(this.$group.attr("tabindex",this.element.tabIndex),this.element.autofocus&&this.focus())},toggleChecked:function(){this.$buttons.toggleClass("active "+this.options.defaultClass),this.$off.toggleClass(this.options.offClass),this.$on.toggleClass(this.options.onClass)},toggleDisabled:function(){this.$buttons.toggleClass("disabled"),this.element.disabled?(this.$group.attr("tabindex",this.element.tabIndex),this.$group.css("cursor","")):(this.$group.removeAttr("tabindex"),this.options.disabledCursor&&this.$group.css("cursor",this.options.disabledCursor))},focus:function(){this.$group.trigger("focus")},click:function(){this.change(!this.element.checked)},change:function(a){this.element.checked=a,this.$element.trigger("change")},keydown:function(b){-1!=a.inArray(b.keyCode,this.options.toggleKeyCodes)?(b.preventDefault(),this.click()):13==b.keyCode&&a(this.element.form).trigger("submit")},reset:function(){(this.element.defaultChecked&&this.$off.hasClass("active")||!this.element.defaultChecked&&this.$on.hasClass("active"))&&this.change(this.element.defaultChecked)}};var c=a.extend({},a.propHooks);return a.extend(a.propHooks,{checked:{set:function(b,d){var e=a.data(b,"bs.checkbox");e&&b.checked!=d&&e.change(d),c.checked&&c.checked.set&&c.checked.set(b,d)}},disabled:{set:function(b,d){var e=a.data(b,"bs.checkbox");e&&b.disabled!=d&&e.toggleDisabled(),c.disabled&&c.disabled.set&&c.disabled.set(b,d)}}}),a.fn.checkboxpicker=function(c,d){var e;return e=this instanceof a?this:a("string"==typeof c?c:d),e.each(function(){var d=a.data(this,"bs.checkbox");d||(d=new b(this,c),a.data(this,"bs.checkbox",d))})},a.fn.checkboxpicker.defaults={style:!1,defaultClass:"btn-default",disabledCursor:"not-allowed",offClass:"btn-danger",onClass:"btn-success",offLabel:"No",onLabel:"Yes",offTitle:!1,onTitle:!1,toggleKeyCodes:[13,32],warningMessage:"Please do not use Bootstrap-checkbox element in label element."},a.fn.checkboxpicker});