<!DOCTYPE html>
<html>
<head>
    <title>AJAX Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-box { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        pre { background: white; padding: 10px; border: 1px solid #ddd; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>AJAX Sync Test</h1>
    
    <div class="test-box">
        <h3>Test 1: Check Local Stats</h3>
        <button onclick="testLocalStats()">Test Local Stats</button>
        <div id="stats-result"></div>
    </div>
    
    <div class="test-box">
        <h3>Test 2: Test Save Employees</h3>
        <button onclick="testSaveEmployees()">Test Save Employees</button>
        <div id="save-result"></div>
    </div>
    
    <div class="test-box">
        <h3>Test 3: Raw Response Check</h3>
        <button onclick="testRawResponse()">Check Raw Response</button>
        <div id="raw-result"></div>
    </div>

    <script>
        function testLocalStats() {
            const resultDiv = document.getElementById('stats-result');
            resultDiv.innerHTML = 'Testing...';
            
            fetch('simple_sync_client.php?action=local_stats')
                .then(response => {
                    console.log('Response status:', response.status);
                    console.log('Response headers:', response.headers);
                    return response.text(); // Get as text first
                })
                .then(text => {
                    console.log('Raw response:', text);
                    resultDiv.innerHTML = `
                        <h4>Raw Response:</h4>
                        <pre>${text}</pre>
                        <h4>Trying to parse as JSON:</h4>
                    `;
                    
                    try {
                        const data = JSON.parse(text);
                        resultDiv.innerHTML += `<div class="success">✅ Valid JSON: ${JSON.stringify(data, null, 2)}</div>`;
                    } catch (e) {
                        resultDiv.innerHTML += `<div class="error">❌ Invalid JSON: ${e.message}</div>`;
                    }
                })
                .catch(error => {
                    resultDiv.innerHTML = `<div class="error">❌ Fetch error: ${error.message}</div>`;
                });
        }
        
        function testSaveEmployees() {
            const resultDiv = document.getElementById('save-result');
            resultDiv.innerHTML = 'Testing...';
            
            const testData = {
                data: [
                    {
                        EmployeeID: '12345',
                        AccessID: 'TEST001',
                        FullName: 'Test Employee',
                        Position: 'Developer',
                        AgencyCompany: 'Test Company',
                        ContactNo: '*********',
                        DateHired: '2024-01-01',
                        Status: 'Active',
                        TimeIN: '09:00:00',
                        TimeOut: '17:00:00'
                    }
                ]
            };
            
            fetch('simple_sync_client.php?action=save_employees', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(testData)
            })
            .then(response => {
                console.log('Response status:', response.status);
                return response.text(); // Get as text first
            })
            .then(text => {
                console.log('Raw response:', text);
                resultDiv.innerHTML = `
                    <h4>Raw Response:</h4>
                    <pre>${text}</pre>
                    <h4>Trying to parse as JSON:</h4>
                `;
                
                try {
                    const data = JSON.parse(text);
                    resultDiv.innerHTML += `<div class="success">✅ Valid JSON: ${JSON.stringify(data, null, 2)}</div>`;
                } catch (e) {
                    resultDiv.innerHTML += `<div class="error">❌ Invalid JSON: ${e.message}</div>`;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `<div class="error">❌ Fetch error: ${error.message}</div>`;
            });
        }
        
        function testRawResponse() {
            const resultDiv = document.getElementById('raw-result');
            resultDiv.innerHTML = 'Testing...';
            
            // Test with XMLHttpRequest to get more details
            const xhr = new XMLHttpRequest();
            xhr.open('POST', 'simple_sync_client.php?action=save_employees', true);
            xhr.setRequestHeader('Content-Type', 'application/json');
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    const response = xhr.responseText;
                    const status = xhr.status;
                    const headers = xhr.getAllResponseHeaders();
                    
                    resultDiv.innerHTML = `
                        <h4>Status: ${status}</h4>
                        <h4>Headers:</h4>
                        <pre>${headers}</pre>
                        <h4>Raw Response (${response.length} characters):</h4>
                        <pre>${response}</pre>
                        <h4>First 100 characters:</h4>
                        <pre>${response.substring(0, 100)}</pre>
                        <h4>Character codes of first 20 characters:</h4>
                        <pre>${response.substring(0, 20).split('').map(c => c.charCodeAt(0)).join(', ')}</pre>
                    `;
                }
            };
            
            const testData = {
                data: [
                    {
                        EmployeeID: '12345',
                        AccessID: 'TEST001',
                        FullName: 'Test Employee'
                    }
                ]
            };
            
            xhr.send(JSON.stringify(testData));
        }
    </script>
</body>
</html>
