(function(e){"use strict";e.fn.multifilter=function(t){var n=e.extend({target:e("table"),method:"thead"},t);jQuery.expr[":"].Contains=function(e,t,n){return(e.textContent||e.innerText||"").toUpperCase().indexOf(n[3].toUpperCase())>=0};this.each(function(){var t=e(this);var r=n.target;var i="tr";var s="td";var o=r.find(e(i));if(n.method==="thead"){var u=r.find("th:Contains("+t.data("col")+")");var a=r.find(e("thead th")).index(u)}if(n.method==="class"){var u=o.first().find("td."+t.data("col")+"");var a=o.first().find("td").index(u)}t.change(function(){var n=t.val();o.each(function(){var t=e(this);var r=e(t.children(s)[a]);if(n){if(r.text().toLowerCase().indexOf(n.toLowerCase())!==-1){r.attr("data-filtered","positive")}else{r.attr("data-filtered","negative")}if(t.find(s+"[data-filtered=negative]").size()>0){t.hide()}else{if(t.find(s+"[data-filtered=positive]").size()>0){t.show()}}}else{r.attr("data-filtered","positive");if(t.find(s+"[data-filtered=negative]").size()>0){t.hide()}else{if(t.find(s+"[data-filtered=positive]").size()>0){t.show()}}}});return false}).keyup(function(){t.change()})})}})(jQuery)
