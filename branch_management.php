<?php
include('auth.php');
require_once('include/include-head.php');
require_once('include/include-main.php');
require_once('proc/config.php');

// Check if user is admin
if($_SESSION['ACCESSLEVEL'] != 'Admin') {
    header('location: access-denied.php');
    exit;
}

// Get all branches
$branches = array();
$sql = "SELECT * FROM tbl_branches ORDER BY branch_name";
$result = mysql_query($sql);
if($result && mysql_num_rows($result) > 0) {
    while($row = mysql_fetch_assoc($result)) {
        $branches[] = $row;
    }
}

// Get branch statistics
$branch_stats = array();
foreach($branches as $branch) {
    $branch_id = $branch['branch_id'];
    
    // Get personnel count
    $sql = "SELECT COUNT(*) as personnel_count FROM tbl_personnel WHERE branch_id = $branch_id";
    $result = mysql_query($sql);
    $personnel_count = 0;
    if($result && mysql_num_rows($result) > 0) {
        $row = mysql_fetch_assoc($result);
        $personnel_count = $row['personnel_count'];
    }
    
    // Get today's attendance
    $sql = "SELECT COUNT(*) as today_attendance FROM tbl_in_out WHERE branch_id = $branch_id AND DATE(TimeRecord) = CURDATE()";
    $result = mysql_query($sql);
    $today_attendance = 0;
    if($result && mysql_num_rows($result) > 0) {
        $row = mysql_fetch_assoc($result);
        $today_attendance = $row['today_attendance'];
    }
    
    // Get access areas count
    $sql = "SELECT COUNT(*) as areas_count FROM tbl_arealist WHERE branch_id = $branch_id";
    $result = mysql_query($sql);
    $areas_count = 0;
    if($result && mysql_num_rows($result) > 0) {
        $row = mysql_fetch_assoc($result);
        $areas_count = $row['areas_count'];
    }
    
    $branch_stats[$branch_id] = array(
        'personnel_count' => $personnel_count,
        'today_attendance' => $today_attendance,
        'areas_count' => $areas_count
    );
}
?>

<style>
.branch-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.branch-card:hover {
    transform: translateY(-5px);
}

.branch-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.branch-name {
    font-size: 1.5rem;
    font-weight: bold;
    color: #333;
}

.branch-code {
    background: #007bff;
    color: white;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
}

.branch-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.stat-item {
    text-align: center;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: bold;
    color: #007bff;
}

.stat-label {
    font-size: 0.8rem;
    color: #666;
    text-transform: uppercase;
}

.status-badge {
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: bold;
}

.status-active {
    background: #d4edda;
    color: #155724;
}

.status-inactive {
    background: #f8d7da;
    color: #721c24;
}
</style>

<div class="col col-md-12">
    <div class="panel panel-default">
        <div class="panel-heading">
            <div class="panel-title">
                <span style="font-size:x-large;font-weight:bold;">
                    <i class="fa fa-building"></i> BRANCH MANAGEMENT
                </span>
                <button class="btn btn-primary pull-right" onclick="window.location.href='dashboard_enhanced.php'">
                    <i class="fa fa-dashboard"></i> Back to Dashboard
                </button>
            </div>
        </div>

        <div class="panel-body">
            <div class="row">
                <div class="col-md-12">
                    <h3>Ajwa Garden Branches Overview</h3>
                    <p class="text-muted">Manage and monitor all branch locations</p>
                </div>
            </div>

            <div class="row">
                <?php foreach($branches as $branch): ?>
                    <?php $stats = $branch_stats[$branch['branch_id']]; ?>
                    <div class="col-md-4">
                        <div class="branch-card">
                            <div class="branch-header">
                                <div>
                                    <div class="branch-name"><?php echo $branch['branch_name']; ?></div>
                                    <div style="color: #666; margin-top: 5px;">
                                        <i class="fa fa-map-marker"></i> <?php echo $branch['branch_location']; ?>
                                    </div>
                                </div>
                                <div>
                                    <div class="branch-code"><?php echo $branch['branch_code']; ?></div>
                                    <div style="margin-top: 5px;">
                                        <span class="status-badge <?php echo $branch['branch_status'] == 'Active' ? 'status-active' : 'status-inactive'; ?>">
                                            <?php echo $branch['branch_status']; ?>
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <div class="branch-stats">
                                <div class="stat-item">
                                    <div class="stat-number"><?php echo $stats['personnel_count']; ?></div>
                                    <div class="stat-label">Employees</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number"><?php echo $stats['today_attendance']; ?></div>
                                    <div class="stat-label">Today's Attendance</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number"><?php echo $stats['areas_count']; ?></div>
                                    <div class="stat-label">Access Areas</div>
                                </div>
                            </div>

                            <div style="margin-top: 15px; text-align: center;">
                                <button class="btn btn-sm btn-primary" onclick="switchToBranch(<?php echo $branch['branch_id']; ?>)">
                                    <i class="fa fa-eye"></i> View Dashboard
                                </button>
                                <button class="btn btn-sm btn-info" onclick="viewBranchDetails(<?php echo $branch['branch_id']; ?>)">
                                    <i class="fa fa-info-circle"></i> Details
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Summary Statistics -->
            <div class="row" style="margin-top: 30px;">
                <div class="col-md-12">
                    <div class="panel panel-info">
                        <div class="panel-heading">
                            <h4><i class="fa fa-bar-chart"></i> Overall Statistics</h4>
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-md-3 text-center">
                                    <h3 class="text-primary"><?php echo count($branches); ?></h3>
                                    <p>Total Branches</p>
                                </div>
                                <div class="col-md-3 text-center">
                                    <h3 class="text-success"><?php echo array_sum(array_column($branch_stats, 'personnel_count')); ?></h3>
                                    <p>Total Employees</p>
                                </div>
                                <div class="col-md-3 text-center">
                                    <h3 class="text-warning"><?php echo array_sum(array_column($branch_stats, 'today_attendance')); ?></h3>
                                    <p>Today's Total Attendance</p>
                                </div>
                                <div class="col-md-3 text-center">
                                    <h3 class="text-info"><?php echo array_sum(array_column($branch_stats, 'areas_count')); ?></h3>
                                    <p>Total Access Areas</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function switchToBranch(branchId) {
    $.ajax({
        url: 'api/switch_branch.php',
        method: 'POST',
        data: { branch_id: branchId },
        success: function(response) {
            window.location.href = 'dashboard_enhanced.php';
        },
        error: function(xhr, status, error) {
            alert('Error switching branch: ' + error);
        }
    });
}

function viewBranchDetails(branchId) {
    // You can implement a detailed view modal or redirect to a detailed page
    alert('Branch details functionality can be implemented here');
}
</script>
