<?php
// Debug version of dashboard_data.php
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// Start output buffering to catch any unwanted output
ob_start();

try {
    // Test 1: Basic PHP execution
    echo "<!-- Debug: PHP execution started -->\n";
    
    // Test 2: Include config
    require_once('../proc/config.php');
    echo "<!-- Debug: Config included -->\n";
    
    // Test 3: Start session
    session_start();
    echo "<!-- Debug: Session started -->\n";
    
    // Test 4: Check authentication (simplified)
    if(!isset($_SESSION['SESS_USER_ID'])) {
        // Set a default session for testing
        $_SESSION['SESS_USER_ID'] = 1;
        $_SESSION['SESS_USER_NAME'] = 'test_user';
        $_SESSION['SESS_USER_FULL_NAME'] = 'Test User';
        $_SESSION['ACCESSLEVEL'] = 'admin';
    }
    echo "<!-- Debug: Authentication checked -->\n";
    
    // Clear any output buffer content before JSON
    ob_clean();
    
    // Set proper headers
    header('Content-Type: application/json');
    
    // Test 5: Basic database connection
    if(!function_exists('mysql_query')) {
        throw new Exception('MySQL functions not available');
    }
    
    // Test 6: Simple query
    $sql = "SELECT COUNT(*) as count FROM tbl_personnel";
    $result = mysql_query($sql);
    
    if(!$result) {
        throw new Exception('Database query failed: ' . mysql_error());
    }
    
    $row = mysql_fetch_assoc($result);
    $total_personnel = (int)$row['count'];
    
    // Test 7: Create minimal response
    $data = array(
        'status' => 'success',
        'debug' => 'API is working',
        'total_personnel' => $total_personnel,
        'current_branch_id' => isset($_GET['branch_id']) ? (int)$_GET['branch_id'] : 1,
        'timestamp' => date('Y-m-d H:i:s'),
        'session_user' => $_SESSION['SESS_USER_ID']
    );
    
    // Test 8: JSON encoding
    $json = json_encode($data);
    if($json === false) {
        throw new Exception('JSON encoding failed: ' . json_last_error_msg());
    }
    
    echo $json;
    
} catch (Exception $e) {
    // Clear any output
    ob_clean();
    
    // Set error headers
    http_response_code(500);
    header('Content-Type: application/json');
    
    // Return detailed error information
    echo json_encode(array(
        'error' => true,
        'message' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString()
    ));
} catch (Error $e) {
    // Handle fatal errors
    ob_clean();
    http_response_code(500);
    header('Content-Type: application/json');
    
    echo json_encode(array(
        'error' => true,
        'message' => 'Fatal error: ' . $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ));
}
?>
