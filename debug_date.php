<?php
// Debug date and timezone issues
echo "<h2>Date Debug Information</h2>";

echo "<h3>Default PHP Date:</h3>";
echo "Current date (default): " . date('Y-m-d H:i:s') . "<br>";
echo "Current timezone (default): " . date_default_timezone_get() . "<br>";

echo "<h3>After Setting Asia/Karachi:</h3>";
date_default_timezone_set('Asia/Karachi');
echo "Current date (Karachi): " . date('Y-m-d H:i:s') . "<br>";
echo "Current timezone (Karachi): " . date_default_timezone_get() . "<br>";

echo "<h3>Test Dates:</h3>";
$test_dates = ['2025-07-27', '2025-07-28'];
$current_date = date('Y-m-d');

foreach($test_dates as $test_date) {
    $is_today = ($test_date == $current_date);
    echo "Date: $test_date | Current: $current_date | Is Today: " . ($is_today ? 'YES' : 'NO') . "<br>";
}

echo "<h3>Server Information:</h3>";
echo "Server time: " . $_SERVER['REQUEST_TIME'] . " (" . date('Y-m-d H:i:s', $_SERVER['REQUEST_TIME']) . ")<br>";
echo "PHP version: " . phpversion() . "<br>";

// Check what the config file sets
echo "<h3>Config File Check:</h3>";
require_once('proc/config.php');
echo "Config timezone should be: Asia/Karachi<br>";
echo "Current timezone after config: " . date_default_timezone_get() . "<br>";
echo "Current date after config: " . date('Y-m-d H:i:s') . "<br>";
?>
