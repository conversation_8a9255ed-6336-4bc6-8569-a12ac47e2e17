/* JPEGCam v1.0.11 *//* Webcam library for capturing JPEG images and submitting to a server *//* Copyright (c) 2008 - 2009 <PERSON> <<EMAIL>> *//* Licensed under the GNU Lesser Public License *//* http://www.gnu.org/licenses/lgpl.html *//* Usage:
	<script language="JavaScript">
		document.write( webcam.get_html(320, 240) );
		webcam.set_api_url( 'test.php' );
		webcam.set_hook( 'onComplete', 'my_callback_function' );
		function my_callback_function(response) {
			alert("Success! PHP returned: " + response);
		}
	</script>
	<a href="javascript:void(webcam.snap())">Take Snapshot</a>
*/// Everything is under a 'webcam' Namespace
window.webcam={version:"1.0.11",ie:!!navigator.userAgent.match(/MSIE/),protocol:location.protocol.match(/https/i)?"https":"http",callback:null,swf_url:"webcam.swf",shutter_url:"shutter.mp3",api_url:"",loaded:false,quality:90,shutter_sound:true,stealth:true,hooks:{onLoad:null,onAllow:null,onComplete:null,onError:null},set_hook:function(a,b){this.hooks[a]=b},fire_hook:function(a,b){if(this.hooks[a]){if(typeof this.hooks[a]==="function"){this.hooks[a](b)}else if(typeof this.hooks[a]==="array"){this.hooks[a][0][this.hooks[a][1]](b)}else if(window[this.hooks[a]]){window[this.hooks[a]](b)}return true}return false},set_api_url:function(a){this.api_url=a},set_swf_url:function(a){this.swf_url=a},get_html:function(a,b,c,d){if(!c){c=a}if(!d){d=b}var e="";var f="shutter_enabled="+(this.shutter_sound?1:0)+"&shutter_url="+encodeURIComponent(this.shutter_url)+"&width="+a+"&height="+b+"&server_width="+c+"&server_height="+d;if(this.ie){e+='<object classid="clsid:d27cdb6e-ae6d-11cf-96b8-444553540000" codebase="'+this.protocol+'://download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=9,0,0,0" width="'+a+'" height="'+b+'" id="webcam_movie" align="middle"><param name="allowScriptAccess" value="always" /><param name="allowFullScreen" value="false" /><param name="movie" value="'+this.swf_url+'" /><param name="loop" value="false" /><param name="menu" value="false" /><param name="quality" value="best" /><param name="bgcolor" value="#ffffff" /><param name="flashvars" value="'+f+'"/></object>'}else{e+='<embed id="webcam_movie" src="'+this.swf_url+'" loop="false" menu="false" quality="best" bgcolor="#ffffff" width="'+a+'" height="'+b+'" name="webcam_movie" align="middle" wmode="opaque" allowScriptAccess="always" allowFullScreen="false" type="application/x-shockwave-flash" pluginspage="http://www.macromedia.com/go/getflashplayer" flashvars="'+f+'" />'}this.loaded=false;return e},get_movie:function(){if(!this.loaded){return false}var a=document.getElementById("webcam_movie");if(!a){return false}return a},set_stealth:function(a){this.stealth=a},snap:function(a,b,c){if(b){this.set_hook("onComplete",b)}if(a){this.set_api_url(a)}if(typeof c!=="undefined"){this.set_stealth(c)}this.get_movie()._snap(this.api_url,this.quality,this.shutter_sound?1:0,this.stealth?1:0)},freeze:function(){this.get_movie()._snap("",this.quality,this.shutter_sound?1:0,0)},upload:function(a,b){if(b){this.set_hook("onComplete",b)}if(a){this.set_api_url(a)}this.get_movie()._upload(this.api_url)},reset:function(){this.get_movie()._reset()},configure:function(a){if(!a){a="camera"}this.get_movie()._configure(a)},set_quality:function(a){this.quality=a},set_shutter_sound:function(a,b){this.shutter_sound=a;this.shutter_url=b?b:"shutter.mp3"},flash_notify:function(a,b){switch(a){case"security":var c=b=="Camera.Unmuted";this.fire_hook("onAllow",c);break;case"flashLoadComplete":this.loaded=true;this.fire_hook("onLoad",b);break;case"error":if(!this.fire_hook("onError",b)){}break;case"success":this.fire_hook("onComplete",b.toString());break;default:break}}}