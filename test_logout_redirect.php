<!DOCTYPE html>
<html>
<head>
    <title>Test Logout Redirect</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .test-container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin: 30px 0; padding: 20px; border: 1px solid #e9ecef; border-radius: 8px; }
        .success { background: #d4edda; color: #155724; border-color: #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border-color: #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border-color: #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border-color: #ffeaa7; }
        .btn { padding: 10px 15px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; text-decoration: none; display: inline-block; }
        .btn:hover { background: #0056b3; }
        .btn-danger { background: #dc3545; }
        .btn-danger:hover { background: #c82333; }
        .code-block { background: #f8f9fa; padding: 15px; border-radius: 4px; font-family: monospace; border-left: 4px solid #007bff; }
    </style>
</head>
<body>
    <div class="test-container">
        <h3>🚪 Logout Redirect Test</h3>
        
        <?php
        session_start();
        
        // Test 1: Current session status
        echo "<div class='test-section'>";
        echo "<h4>1. Current Session Status</h4>";
        
        if(isset($_SESSION['SESS_USER_ID'])) {
            echo "<p class='success'>✅ User is currently logged in</p>";
            echo "<p><strong>User:</strong> " . ($_SESSION['SESS_USER_FULL_NAME'] ?? 'N/A') . "</p>";
            echo "<p><strong>Username:</strong> " . ($_SESSION['SESS_USER_NAME'] ?? 'N/A') . "</p>";
            echo "<p><strong>Branch ID:</strong> " . ($_SESSION['CURRENT_BRANCH_ID'] ?? 'Not set') . "</p>";
            echo "<p><strong>Branch Name:</strong> " . ($_SESSION['CURRENT_BRANCH_NAME'] ?? 'Not set') . "</p>";
        } else {
            echo "<p class='info'>ℹ️ No user currently logged in</p>";
            echo "<p>You need to login first to test logout functionality.</p>";
            echo "<a href='index.php' class='btn'>Go to Login Page</a>";
        }
        echo "</div>";
        
        // Test 2: Logout file content
        echo "<div class='test-section'>";
        echo "<h4>2. Current Logout Configuration</h4>";
        
        $logout_content = file_get_contents('logout.php');
        echo "<p><strong>Current logout.php content:</strong></p>";
        echo "<div class='code-block'>" . htmlspecialchars($logout_content) . "</div>";
        
        if(strpos($logout_content, 'registration.ipd.ac.uk') !== false) {
            echo "<p class='success'>✅ Logout is configured to redirect to registration.ipd.ac.uk</p>";
        } else {
            echo "<p class='error'>❌ Logout is not configured for external redirect</p>";
        }
        echo "</div>";
        
        // Test 3: Test logout functionality
        echo "<div class='test-section'>";
        echo "<h4>3. Test Logout Functionality</h4>";
        
        if(isset($_SESSION['SESS_USER_ID'])) {
            echo "<p>Click the button below to test the logout redirect:</p>";
            echo "<a href='logout.php' class='btn btn-danger'>Test Logout (Redirect to registration.ipd.ac.uk)</a>";
            echo "<p class='warning'>⚠️ This will log you out and redirect to the external URL</p>";
        } else {
            echo "<p class='info'>ℹ️ You need to be logged in to test logout functionality</p>";
        }
        echo "</div>";
        
        // Test 4: Manual redirect test
        echo "<div class='test-section'>";
        echo "<h4>4. Manual Redirect Test</h4>";
        
        echo "<p>Test the redirect URL directly:</p>";
        echo "<a href='https://registration.ipd.ac.uk' target='_blank' class='btn'>Test External URL</a>";
        echo "<p class='info'>ℹ️ This will open the external URL in a new tab</p>";
        echo "</div>";
        
        // Test 5: Session cleanup verification
        echo "<div class='test-section'>";
        echo "<h4>5. Session Variables That Will Be Cleared</h4>";
        
        echo "<p>The following session variables will be cleared on logout:</p>";
        echo "<ul>";
        echo "<li><code>\$_SESSION['SESS_USER_FULL_NAME']</code> - " . (isset($_SESSION['SESS_USER_FULL_NAME']) ? '✅ Set' : '❌ Not set') . "</li>";
        echo "<li><code>\$_SESSION['SESS_USER_ID']</code> - " . (isset($_SESSION['SESS_USER_ID']) ? '✅ Set' : '❌ Not set') . "</li>";
        echo "<li><code>\$_SESSION['SESS_USER_NAME']</code> - " . (isset($_SESSION['SESS_USER_NAME']) ? '✅ Set' : '❌ Not set') . "</li>";
        echo "<li><code>\$_SESSION['LAST_ACTIVITY']</code> - " . (isset($_SESSION['LAST_ACTIVITY']) ? '✅ Set' : '❌ Not set') . "</li>";
        echo "<li><code>\$_SESSION['ACCESSLEVEL']</code> - " . (isset($_SESSION['ACCESSLEVEL']) ? '✅ Set' : '❌ Not set') . "</li>";
        echo "<li><code>\$_SESSION['CURRENT_BRANCH_ID']</code> - " . (isset($_SESSION['CURRENT_BRANCH_ID']) ? '✅ Set' : '❌ Not set') . "</li>";
        echo "<li><code>\$_SESSION['CURRENT_BRANCH_NAME']</code> - " . (isset($_SESSION['CURRENT_BRANCH_NAME']) ? '✅ Set' : '❌ Not set') . "</li>";
        echo "<li><code>\$_SESSION['CURRENT_BRANCH_CODE']</code> - " . (isset($_SESSION['CURRENT_BRANCH_CODE']) ? '✅ Set' : '❌ Not set') . "</li>";
        echo "</ul>";
        echo "</div>";
        
        // Test 6: Alternative logout URLs
        echo "<div class='test-section'>";
        echo "<h4>6. Alternative Logout URL Options</h4>";
        
        if(isset($_POST['update_logout_url'])) {
            $new_url = $_POST['logout_url'];
            
            // Read current logout.php content
            $current_content = file_get_contents('logout.php');
            
            // Replace the redirect URL
            $new_content = preg_replace(
                '/header\("location: [^"]+"\);/',
                'header("location: ' . $new_url . '");',
                $current_content
            );
            
            // Write back to file
            if(file_put_contents('logout.php', $new_content)) {
                echo "<p class='success'>✅ Logout URL updated to: $new_url</p>";
                echo "<script>setTimeout(() => window.location.reload(), 1000);</script>";
            } else {
                echo "<p class='error'>❌ Failed to update logout URL</p>";
            }
        }
        
        echo "<form method='POST' style='background: #f8f9fa; padding: 15px; border-radius: 4px;'>";
        echo "<h5>Update Logout Redirect URL:</h5>";
        echo "<input type='url' name='logout_url' value='https://registration.ipd.ac.uk' required style='width: 400px; padding: 8px; margin: 5px;'>";
        echo "<button type='submit' name='update_logout_url' class='btn'>Update Logout URL</button>";
        echo "<small style='display: block; margin-top: 5px;'>Enter the URL where users should be redirected after logout</small>";
        echo "</form>";
        echo "</div>";
        ?>
        
        <!-- Instructions -->
        <div class="test-section info">
            <h4>📋 Logout Redirect System</h4>
            <p><strong>Current Configuration:</strong></p>
            <ul>
                <li>Logout URL: <code>https://registration.ipd.ac.uk</code></li>
                <li>Session cleanup: All user and branch variables cleared</li>
                <li>Complete session destruction</li>
                <li>Automatic redirect to external URL</li>
            </ul>
            
            <p><strong>How it works:</strong></p>
            <ol>
                <li>User clicks logout link</li>
                <li>System clears all session variables</li>
                <li>System destroys the session completely</li>
                <li>User is redirected to <code>https://registration.ipd.ac.uk</code></li>
            </ol>
            
            <p><strong>Testing Steps:</strong></p>
            <ol>
                <li>Login to the system first</li>
                <li>Click the "Test Logout" button above</li>
                <li>Verify you're redirected to the external URL</li>
                <li>Try accessing protected pages to confirm logout worked</li>
            </ol>
        </div>
        
        <!-- Navigation test -->
        <div class="test-section">
            <h4>7. Navigation Logout Links</h4>
            <p>Test logout from different pages:</p>
            <a href="dashboard_enhanced.php" class="btn">Dashboard (check logout link)</a>
            <a href="pg_accounts.php" class="btn">Accounts (check logout link)</a>
            <p class="info">ℹ️ All logout links should redirect to registration.ipd.ac.uk</p>
        </div>
    </div>
</body>
</html>
