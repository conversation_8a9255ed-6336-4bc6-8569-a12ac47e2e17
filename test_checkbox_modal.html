<!DOCTYPE html>
<html>
<head>
    <title>Checkbox Modal Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
</head>
<body>
    <div class="container mt-5">
        <h2>Checkbox Modal Test</h2>
        
        <button class="btn btn-primary" data-toggle="modal" data-target="#testModal">
            Open Test Modal
        </button>
        
        <div id="checkbox-status" class="mt-3"></div>
    </div>

    <!-- Test Modal -->
    <div class="modal fade" id="testModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Test Modal</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="testForm">
                        <div class="form-group">
                            <label>Import Options</label>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="skip_duplicates" value="1" checked>
                                    Skip duplicate Employee IDs (recommended)
                                </label>
                            </div>
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" name="auto_generate_ids" value="1" checked>
                                    Auto-generate missing Employee/Biometric IDs
                                </label>
                            </div>
                        </div>
                        
                        <button type="button" onclick="checkStatus()" class="btn btn-info">Check Status</button>
                        <button type="button" onclick="resetForm()" class="btn btn-warning">Reset Form</button>
                        <button type="button" onclick="setDefaults()" class="btn btn-success">Set Defaults</button>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script>
    // Check status when modal is shown
    $('#testModal').on('shown.bs.modal', function () {
        console.log('Modal shown event triggered');
        setDefaults();
        checkStatus();
    });
    
    // Check status when modal is hidden
    $('#testModal').on('hidden.bs.modal', function () {
        console.log('Modal hidden event triggered');
    });
    
    function checkStatus() {
        const skipDuplicates = $('input[name="skip_duplicates"]').is(':checked');
        const autoGenerateIds = $('input[name="auto_generate_ids"]').is(':checked');
        
        let status = '<h4>Checkbox Status:</h4>';
        status += '<p>Skip Duplicates: <strong>' + (skipDuplicates ? 'CHECKED' : 'UNCHECKED') + '</strong></p>';
        status += '<p>Auto Generate IDs: <strong>' + (autoGenerateIds ? 'CHECKED' : 'UNCHECKED') + '</strong></p>';
        
        $('#checkbox-status').html(status);
        
        console.log('Skip Duplicates:', skipDuplicates);
        console.log('Auto Generate IDs:', autoGenerateIds);
    }
    
    function resetForm() {
        $('#testForm')[0].reset();
        console.log('Form reset');
        setTimeout(checkStatus, 100); // Check status after reset
    }
    
    function setDefaults() {
        $('input[name="skip_duplicates"]').prop('checked', true);
        $('input[name="auto_generate_ids"]').prop('checked', true);
        console.log('Defaults set');
    }
    
    // Initial check when page loads
    $(document).ready(function() {
        checkStatus();
    });
    </script>
</body>
</html>
