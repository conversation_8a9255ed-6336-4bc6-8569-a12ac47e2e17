<?php
session_start();
include('../proc/config.php');

header('Content-Type: application/json');

// Check if user is logged in
if(!isset($_SESSION['SESS_USER_ID'])) {
    echo json_encode(array('success' => false, 'message' => 'User not logged in'));
    exit();
}

try {
    // Get current branch
    $current_branch_id = isset($_SESSION['CURRENT_BRANCH_ID']) ? $_SESSION['CURRENT_BRANCH_ID'] : 1;
    
    // Get employees from current branch
    $sql = "SELECT EmployeeID, AccessID, FullName, Position, DateHired, AgencyCompany, ProjectAssigned 
            FROM tbl_personnel 
            WHERE branch_id = '$current_branch_id' 
            ORDER BY DateHired DESC, EmployeeID DESC 
            LIMIT 50";
    
    $result = mysql_query($sql);
    
    if(!$result) {
        throw new Exception('Database query failed: ' . mysql_error());
    }
    
    $employees = array();
    while($row = mysql_fetch_assoc($result)) {
        $employees[] = $row;
    }
    
    echo json_encode(array(
        'success' => true,
        'data' => $employees,
        'count' => count($employees),
        'branch_id' => $current_branch_id
    ));

} catch(Exception $e) {
    echo json_encode(array(
        'success' => false,
        'message' => $e->getMessage()
    ));
}
?>
