<!DOCTYPE html>
<html>
<head>
    <title>Test Bulk Employee Import</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .test-container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin: 30px 0; padding: 20px; border: 1px solid #e9ecef; border-radius: 8px; }
        .success { background: #d4edda; color: #155724; border-color: #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border-color: #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border-color: #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border-color: #ffeaa7; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { padding: 12px; border: 1px solid #ccc; text-align: left; }
        th { background: #f0f0f0; font-weight: bold; }
        .btn { padding: 10px 15px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; text-decoration: none; display: inline-block; }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #218838; }
        .btn-info { background: #17a2b8; }
        .btn-info:hover { background: #138496; }
        .code-block { background: #f8f9fa; padding: 15px; border-radius: 4px; font-family: monospace; border-left: 4px solid #007bff; }
    </style>
</head>
<body>
    <div class="test-container">
        <h3>📊 Bulk Employee Import System Test</h3>
        
        <?php
        session_start();
        include('proc/config.php');
        
        // Test 1: Check login status and branch
        echo "<div class='test-section'>";
        echo "<h4>1. System Status Check</h4>";
        
        if(isset($_SESSION['SESS_USER_ID'])) {
            echo "<p class='success'>✅ User is logged in</p>";
            echo "<p><strong>User:</strong> " . $_SESSION['SESS_USER_FULL_NAME'] . "</p>";
            echo "<p><strong>Current Branch:</strong> " . ($_SESSION['CURRENT_BRANCH_NAME'] ?? 'Not set') . " (ID: " . ($_SESSION['CURRENT_BRANCH_ID'] ?? 'Not set') . ")</p>";
        } else {
            echo "<p class='error'>❌ User not logged in - <a href='index.php'>Login first</a></p>";
        }
        
        // Check temp directory
        $tempDir = 'temp/';
        if(!is_dir($tempDir)) {
            echo "<p class='warning'>⚠️ Temp directory doesn't exist - will be created automatically</p>";
        } else {
            echo "<p class='success'>✅ Temp directory exists</p>";
        }
        
        echo "</div>";
        
        // Test 2: Template download test
        echo "<div class='test-section'>";
        echo "<h4>2. Template Download Test</h4>";
        
        echo "<p>Test the Excel template download functionality:</p>";
        echo "<a href='function/downloadTemplate.php' class='btn btn-success'>Download Template</a>";
        echo "<p class='info'>ℹ️ This should download a CSV file with the correct headers and sample data</p>";
        
        echo "</div>";
        
        // Test 3: File upload requirements
        echo "<div class='test-section'>";
        echo "<h4>3. File Upload Requirements</h4>";
        
        $uploadMaxFilesize = ini_get('upload_max_filesize');
        $postMaxSize = ini_get('post_max_size');
        $maxExecutionTime = ini_get('max_execution_time');
        
        echo "<table>";
        echo "<tr><th>Setting</th><th>Current Value</th><th>Recommended</th><th>Status</th></tr>";
        echo "<tr><td>upload_max_filesize</td><td>$uploadMaxFilesize</td><td>10M or higher</td><td>" . (intval($uploadMaxFilesize) >= 10 ? '✅' : '❌') . "</td></tr>";
        echo "<tr><td>post_max_size</td><td>$postMaxSize</td><td>10M or higher</td><td>" . (intval($postMaxSize) >= 10 ? '✅' : '❌') . "</td></tr>";
        echo "<tr><td>max_execution_time</td><td>{$maxExecutionTime}s</td><td>60s or higher</td><td>" . ($maxExecutionTime >= 60 || $maxExecutionTime == 0 ? '✅' : '❌') . "</td></tr>";
        echo "</table>";
        
        echo "</div>";
        
        // Test 4: Database structure check
        echo "<div class='test-section'>";
        echo "<h4>4. Database Structure Check</h4>";
        
        // Check if required columns exist
        $requiredColumns = array('EmployeeID', 'AccessID', 'FullName', 'Position', 'ContactNo', 'DateHired', 'branch_id');
        $optionalColumns = array('Address', 'AgencyCompany', 'ProjectAssigned', 'TimeIN', 'TimeOut', 'schedule_dates');
        
        $columnsCheck = mysql_query("DESCRIBE tbl_personnel");
        $existingColumns = array();
        
        if($columnsCheck) {
            while($row = mysql_fetch_assoc($columnsCheck)) {
                $existingColumns[] = $row['Field'];
            }
        }
        
        echo "<h5>Required Columns:</h5>";
        echo "<ul>";
        foreach($requiredColumns as $column) {
            $exists = in_array($column, $existingColumns);
            echo "<li>" . ($exists ? '✅' : '❌') . " $column</li>";
        }
        echo "</ul>";
        
        echo "<h5>Optional Columns:</h5>";
        echo "<ul>";
        foreach($optionalColumns as $column) {
            $exists = in_array($column, $existingColumns);
            echo "<li>" . ($exists ? '✅' : '⚠️') . " $column</li>";
        }
        echo "</ul>";
        
        echo "</div>";
        
        // Test 5: ID generation test
        echo "<div class='test-section'>";
        echo "<h4>5. ID Generation Test</h4>";
        
        if(file_exists('function/generateEmployeeIDs.php')) {
            include('function/generateEmployeeIDs.php');
            
            echo "<p class='success'>✅ ID generation functions available</p>";
            
            try {
                $testEmployeeID = generateUniqueEmployeeID();
                $testBiometricID = generateUniqueBiometricID();
                
                echo "<p><strong>Sample Generated IDs:</strong></p>";
                echo "<ul>";
                echo "<li>Employee ID: $testEmployeeID</li>";
                echo "<li>Biometric ID: $testBiometricID</li>";
                echo "</ul>";
            } catch(Exception $e) {
                echo "<p class='error'>❌ Error generating IDs: " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p class='error'>❌ ID generation functions not found</p>";
        }
        
        echo "</div>";
        
        // Test 6: Sample CSV data
        echo "<div class='test-section'>";
        echo "<h4>6. Sample CSV Format</h4>";
        
        echo "<p>Here's the expected CSV format for bulk import:</p>";
        
        $sampleCSV = array(
            array('Full Name *', 'Position *', 'Phone Number', 'Address', 'Date Hired (YYYY-MM-DD) *', 'Employee ID', 'Biometric ID', 'Company', 'Project Assignment', 'Time In', 'Time Out', 'Schedule'),
            array('John Doe', 'Manager', '+1234567890', '123 Main St', '2024-01-15', '', '', 'ABC Corp', 'Project A', '09:00', '17:00', 'Monday,Tuesday,Wednesday,Thursday,Friday'),
            array('Jane Smith', 'Assistant', '+1234567891', '456 Oak Ave', '2024-01-16', '', '', 'XYZ Inc', 'Project B', '08:30', '16:30', 'Monday,Tuesday,Wednesday,Thursday,Friday')
        );
        
        echo "<table>";
        foreach($sampleCSV as $rowIndex => $row) {
            echo "<tr>";
            foreach($row as $cell) {
                if($rowIndex == 0) {
                    echo "<th style='font-size: 12px;'>$cell</th>";
                } else {
                    echo "<td style='font-size: 12px;'>$cell</td>";
                }
            }
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<p class='info'>ℹ️ Empty Employee ID and Biometric ID fields will be auto-generated</p>";
        
        echo "</div>";
        
        // Test 7: Manual test upload
        echo "<div class='test-section'>";
        echo "<h4>7. Manual Test Upload</h4>";
        
        if(isset($_POST['test_upload']) && isset($_FILES['test_file'])) {
            echo "<h5>Upload Test Results:</h5>";
            
            $file = $_FILES['test_file'];
            echo "<p><strong>File Name:</strong> " . $file['name'] . "</p>";
            echo "<p><strong>File Size:</strong> " . number_format($file['size'] / 1024, 2) . " KB</p>";
            echo "<p><strong>File Type:</strong> " . $file['type'] . "</p>";
            echo "<p><strong>Upload Error:</strong> " . ($file['error'] == 0 ? 'None' : $file['error']) . "</p>";
            
            if($file['error'] == 0) {
                $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
                echo "<p><strong>File Extension:</strong> $extension</p>";
                
                if($extension == 'csv') {
                    echo "<p class='success'>✅ CSV file detected - attempting to read...</p>";
                    
                    $tempFile = $file['tmp_name'];
                    if(($handle = fopen($tempFile, "r")) !== FALSE) {
                        $rowCount = 0;
                        echo "<h6>First 5 rows:</h6>";
                        echo "<table>";
                        
                        while(($row = fgetcsv($handle, 1000, ",")) !== FALSE && $rowCount < 5) {
                            echo "<tr>";
                            foreach($row as $cell) {
                                echo "<td style='font-size: 12px;'>" . htmlspecialchars($cell) . "</td>";
                            }
                            echo "</tr>";
                            $rowCount++;
                        }
                        
                        echo "</table>";
                        fclose($handle);
                        
                        echo "<p class='success'>✅ File read successfully! $rowCount rows shown.</p>";
                    } else {
                        echo "<p class='error'>❌ Could not read CSV file</p>";
                    }
                } else {
                    echo "<p class='warning'>⚠️ Non-CSV file - would need Excel processing</p>";
                }
            }
        }
        
        echo "<form method='POST' enctype='multipart/form-data' style='background: #f8f9fa; padding: 15px; border-radius: 4px;'>";
        echo "<h5>Test File Upload:</h5>";
        echo "<input type='file' name='test_file' accept='.csv,.xlsx,.xls' required>";
        echo "<button type='submit' name='test_upload' class='btn'>Test Upload</button>";
        echo "<small style='display: block; margin-top: 5px;'>Upload a test file to verify the system can read it</small>";
        echo "</form>";
        
        echo "</div>";
        
        // Test 8: Access the actual feature
        echo "<div class='test-section'>";
        echo "<h4>8. Access Bulk Import Feature</h4>";
        
        echo "<p>Test the actual bulk import feature:</p>";
        echo "<a href='pg_accounts.php' class='btn btn-info'>Go to Accounts Page</a>";
        echo "<p class='info'>ℹ️ Look for the 'Bulk Import from Excel' button on the accounts page</p>";
        
        echo "</div>";
        ?>
        
        <!-- Instructions -->
        <div class="test-section info">
            <h4>📋 How to Use Bulk Import</h4>
            <ol>
                <li><strong>Download Template:</strong> Click "Download Excel Template" to get the CSV template</li>
                <li><strong>Fill Data:</strong> Open the template and fill in employee information</li>
                <li><strong>Save as CSV:</strong> Save the file as CSV format for best compatibility</li>
                <li><strong>Upload File:</strong> Use the "Bulk Import from Excel" button on accounts page</li>
                <li><strong>Configure Options:</strong> Choose import settings (skip duplicates, auto-generate IDs)</li>
                <li><strong>Import:</strong> Click "Import Employees" to process the file</li>
                <li><strong>Review Results:</strong> Check the import summary and any errors</li>
            </ol>
            
            <h5>Important Notes:</h5>
            <ul>
                <li>All imported employees are assigned to your current branch</li>
                <li>Employee IDs and Biometric IDs can be auto-generated</li>
                <li>Duplicate checking is based on Employee ID and Biometric ID</li>
                <li>Date format must be YYYY-MM-DD</li>
                <li>CSV format is recommended for best compatibility</li>
                <li>Maximum file size: 10MB</li>
            </ul>
        </div>
    </div>
</body>
</html>
