<?php
session_start();
require_once('../proc/config.php');

// Check if user is authenticated
if(!isset($_SESSION['SESS_USER_ID'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

header('Content-Type: application/json');

if($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

$branch_id = isset($_POST['branch_id']) ? (int)$_POST['branch_id'] : 0;

if($branch_id <= 0) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid branch ID']);
    exit;
}

try {
    // Check if branch exists
    $sql = "SELECT branch_id, branch_name FROM tbl_branches WHERE branch_id = $branch_id AND branch_status = 'Active'";
    $result = mysql_query($sql);
    
    if(!$result || mysql_num_rows($result) == 0) {
        http_response_code(404);
        echo json_encode(['error' => 'Branch not found']);
        exit;
    }
    
    $branch = mysql_fetch_assoc($result);
    
    // Check if user has access to this branch (Admin can access all branches)
    if($_SESSION['ACCESSLEVEL'] != 'Admin') {
        $sql = "SELECT assigned_branches FROM tb_user WHERE IDno = '".$_SESSION['SESS_USER_ID']."'";
        $result = mysql_query($sql);
        
        if($result && mysql_num_rows($result) > 0) {
            $row = mysql_fetch_assoc($result);
            $assigned_branches = $row['assigned_branches'];
            
            if($assigned_branches) {
                $user_branches = explode(',', $assigned_branches);
                if(!in_array($branch_id, $user_branches)) {
                    http_response_code(403);
                    echo json_encode(['error' => 'Access denied to this branch']);
                    exit;
                }
            }
        }
    }
    
    // Set the current branch in session
    $_SESSION['CURRENT_BRANCH_ID'] = $branch_id;
    
    echo json_encode([
        'success' => true,
        'branch_id' => $branch_id,
        'branch_name' => $branch['branch_name'],
        'message' => 'Branch switched successfully'
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error: ' . $e->getMessage()]);
}
?>
