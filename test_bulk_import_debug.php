<?php
session_start();

// Set a test session for debugging
$_SESSION['SESS_USER_ID'] = 1;
$_SESSION['CURRENT_BRANCH_ID'] = 1;

echo "<h2>Bulk Import Debug Test</h2>";

// Test 1: Check if files exist
echo "<h3>1. File Existence Check</h3>";
$files_to_check = [
    'proc/config.php',
    'function/generateEmployeeIDs.php',
    'function/bulkImportEmployees.php',
    'temp/'
];

foreach($files_to_check as $file) {
    if(file_exists($file)) {
        echo "✅ $file exists<br>";
    } else {
        echo "❌ $file missing<br>";
    }
}

// Test 2: Check database connection
echo "<h3>2. Database Connection Test</h3>";
try {
    include('proc/config.php');
    echo "✅ Database connection successful<br>";
    
    // Test query
    $result = mysql_query("SELECT COUNT(*) as count FROM tbl_personnel");
    if($result) {
        $row = mysql_fetch_assoc($result);
        echo "✅ Personnel table accessible, " . $row['count'] . " records found<br>";
    } else {
        echo "❌ Error querying personnel table: " . mysql_error() . "<br>";
    }
} catch(Exception $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "<br>";
}

// Test 3: Check ID generation
echo "<h3>3. ID Generation Test</h3>";
try {
    include('function/generateEmployeeIDs.php');
    
    $biometricID = generateUniqueBiometricID();
    $employeeID = generateUniqueEmployeeID();
    
    echo "✅ Biometric ID generated: $biometricID<br>";
    echo "✅ Employee ID generated: $employeeID<br>";
} catch(Exception $e) {
    echo "❌ ID generation failed: " . $e->getMessage() . "<br>";
}

// Test 4: Create sample CSV file
echo "<h3>4. Sample CSV Creation</h3>";
$csvContent = "Full Name,Position,Phone Number,Address,Date Hired,Employee ID,Biometric ID,Company,Project Assignment,Time In,Time Out,Schedule\n";
$csvContent .= "John Doe,Manager,+1234567890,123 Main St,2024-01-15,,,ABC Corp,Project A,09:00,17:00,Monday,Tuesday,Wednesday,Thursday,Friday\n";
$csvContent .= "Jane Smith,Assistant,+1234567891,456 Oak Ave,2024-01-16,,,XYZ Inc,Project B,08:30,16:30,Monday,Tuesday,Wednesday,Thursday,Friday\n";

$csvFile = 'temp/sample_import.csv';
if(file_put_contents($csvFile, $csvContent)) {
    echo "✅ Sample CSV created: $csvFile<br>";
    echo "<a href='$csvFile' download>Download Sample CSV</a><br>";
} else {
    echo "❌ Failed to create sample CSV<br>";
}

// Test 5: Test bulk import directly
echo "<h3>5. Direct Bulk Import Test</h3>";
if(file_exists($csvFile)) {
    echo "<form method='post' enctype='multipart/form-data'>";
    echo "<input type='file' name='excel_file' accept='.csv,.xlsx,.xls'><br><br>";
    echo "<label><input type='checkbox' name='auto_generate_ids' value='1' checked> Auto-generate IDs</label><br>";
    echo "<label><input type='checkbox' name='skip_duplicates' value='1' checked> Skip duplicates</label><br><br>";
    echo "<input type='submit' name='test_import' value='Test Import'>";
    echo "</form>";
    
    if(isset($_POST['test_import']) && isset($_FILES['excel_file'])) {
        echo "<h4>Import Test Results:</h4>";
        
        // Simulate the bulk import process
        $_POST['auto_generate_ids'] = '1';
        $_POST['skip_duplicates'] = '1';
        
        // Capture output
        ob_start();
        include('function/bulkImportEmployees.php');
        $output = ob_get_clean();
        
        echo "<pre>Raw Output: " . htmlspecialchars($output) . "</pre>";
        
        // Try to decode as JSON
        $json = json_decode($output, true);
        if($json) {
            echo "<h5>JSON Response:</h5>";
            echo "<pre>" . print_r($json, true) . "</pre>";
        } else {
            echo "<h5>JSON Decode Error:</h5>";
            echo "Error: " . json_last_error_msg() . "<br>";
            echo "Raw output length: " . strlen($output) . " characters<br>";
        }
    }
}

// Test 6: Check error logs
echo "<h3>6. Error Log Check</h3>";
$errorLogs = [
    'temp/bulk_import_errors.log',
    'temp/generate_ids_errors.log'
];

foreach($errorLogs as $logFile) {
    if(file_exists($logFile)) {
        $content = file_get_contents($logFile);
        if(!empty($content)) {
            echo "<h4>$logFile:</h4>";
            echo "<pre>" . htmlspecialchars($content) . "</pre>";
        } else {
            echo "✅ $logFile exists but is empty<br>";
        }
    } else {
        echo "ℹ️ $logFile doesn't exist yet<br>";
    }
}

?>
