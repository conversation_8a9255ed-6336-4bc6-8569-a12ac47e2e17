<?php
// Test the branch fix
session_start();
include('proc/config.php');

echo "<h3>Testing Branch Fix</h3>";

// Test 1: Check if branches table exists
echo "<h4>1. Branches Table Check:</h4>";
$check_branches_table = mysql_query("SHOW TABLES LIKE 'tbl_branches'");
if($check_branches_table && mysql_num_rows($check_branches_table) > 0) {
    echo "<p>✅ tbl_branches table exists</p>";
    
    // Show branches
    $branches_sql = "SELECT * FROM tbl_branches ORDER BY branch_name";
    $branches_result = mysql_query($branches_sql);
    if($branches_result && mysql_num_rows($branches_result) > 0) {
        echo "<p>Branches found: " . mysql_num_rows($branches_result) . "</p>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr style='background: #f0f0f0;'><th>Branch ID</th><th>Branch Name</th><th>Branch Code</th></tr>";
        
        while($branch = mysql_fetch_assoc($branches_result)) {
            echo "<tr>";
            echo "<td>" . $branch['branch_id'] . "</td>";
            echo "<td>" . $branch['branch_name'] . "</td>";
            echo "<td>" . ($branch['branch_code'] ?? 'N/A') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>❌ No branches found in table</p>";
    }
} else {
    echo "<p>❌ tbl_branches table does not exist</p>";
}

// Test 2: Current branch logic
echo "<h4>2. Current Branch Logic Test:</h4>";

// Simulate the same logic as in pg_accounts.php
$current_branch_id = isset($_SESSION['CURRENT_BRANCH_ID']) ? $_SESSION['CURRENT_BRANCH_ID'] : 1;

$current_branch = array(
    'branch_id' => $current_branch_id,
    'branch_name' => 'Main Branch',
    'branch_code' => 'MAIN'
);

$check_branches_table = mysql_query("SHOW TABLES LIKE 'tbl_branches'");
if($check_branches_table && mysql_num_rows($check_branches_table) > 0) {
    $branch_sql = "SELECT * FROM tbl_branches WHERE branch_id = '$current_branch_id' LIMIT 1";
    $branch_result = mysql_query($branch_sql);
    if($branch_result && mysql_num_rows($branch_result) > 0) {
        $current_branch = mysql_fetch_assoc($branch_result);
    }

    $branches = array();
    $branches_sql = "SELECT * FROM tbl_branches ORDER BY branch_name";
    $branches_result = mysql_query($branches_sql);
    if($branches_result && mysql_num_rows($branches_result) > 0) {
        while($branch_row = mysql_fetch_assoc($branches_result)) {
            $branches[] = $branch_row;
        }
    } else {
        $branches = array($current_branch);
    }
} else {
    $branches = array($current_branch);
}

echo "<p><strong>Current Branch ID:</strong> " . $current_branch['branch_id'] . "</p>";
echo "<p><strong>Current Branch Name:</strong> " . $current_branch['branch_name'] . "</p>";
echo "<p><strong>Current Branch Code:</strong> " . $current_branch['branch_code'] . "</p>";
echo "<p><strong>Available Branches:</strong> " . count($branches) . "</p>";

// Test 3: Session information
echo "<h4>3. Session Information:</h4>";
echo "<p><strong>Session CURRENT_BRANCH_ID:</strong> " . (isset($_SESSION['CURRENT_BRANCH_ID']) ? $_SESSION['CURRENT_BRANCH_ID'] : 'Not set') . "</p>";
echo "<p><strong>Session CURRENT_BRANCH_NAME:</strong> " . (isset($_SESSION['CURRENT_BRANCH_NAME']) ? $_SESSION['CURRENT_BRANCH_NAME'] : 'Not set') . "</p>";

// Test 4: Variable output test
echo "<h4>4. Variable Output Test:</h4>";
echo "<p>Testing the exact code that was causing the error:</p>";
echo "<code>\$current_branch['branch_name'] = " . $current_branch['branch_name'] . "</code>";

// Test 5: Branch switching test
echo "<h4>5. Branch Switching Test:</h4>";
if(isset($_POST['test_branch_switch'])) {
    $test_branch_id = $_POST['test_branch_id'];
    $_SESSION['CURRENT_BRANCH_ID'] = $test_branch_id;
    echo "<p>✅ Branch switched to ID: $test_branch_id</p>";
    echo "<p>Reload the page to see the change.</p>";
}
?>

<form method="POST" style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
    <h5>Test Branch Switching:</h5>
    <label>Switch to Branch ID:</label>
    <select name="test_branch_id" required>
        <?php foreach($branches as $branch): ?>
            <option value="<?php echo $branch['branch_id']; ?>" <?php echo ($branch['branch_id'] == $current_branch_id) ? 'selected' : ''; ?>>
                <?php echo $branch['branch_name']; ?> (ID: <?php echo $branch['branch_id']; ?>)
            </option>
        <?php endforeach; ?>
    </select>
    <button type="submit" name="test_branch_switch" style="background: #007bff; color: white; padding: 8px 15px; border: none; border-radius: 4px; margin: 5px;">
        Switch Branch
    </button>
</form>

<h4>6. Create Sample Branch (if needed):</h4>
<?php
if(isset($_POST['create_sample_branch'])) {
    // Create sample branches if table exists but is empty
    $create_sql = "INSERT INTO tbl_branches (branch_id, branch_name, branch_code) VALUES 
                   (1, 'Main Branch', 'MAIN'),
                   (2, 'Secondary Branch', 'SEC'),
                   (3, 'Third Branch', 'THIRD')
                   ON DUPLICATE KEY UPDATE branch_name=VALUES(branch_name)";
    
    if(mysql_query($create_sql)) {
        echo "<p>✅ Sample branches created successfully!</p>";
    } else {
        echo "<p>❌ Error creating sample branches: " . mysql_error() . "</p>";
    }
}
?>

<form method="POST" style="background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;">
    <button type="submit" name="create_sample_branch" style="background: #ffc107; color: black; padding: 8px 15px; border: none; border-radius: 4px;">
        Create Sample Branches
    </button>
    <small style="display: block; margin-top: 5px;">This will create sample branches if the table exists but is empty.</small>
</form>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { border-collapse: collapse; margin: 10px 0; }
th, td { padding: 8px; border: 1px solid #ccc; text-align: left; }
th { background: #f0f0f0; }
code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; font-family: monospace; }
</style>
