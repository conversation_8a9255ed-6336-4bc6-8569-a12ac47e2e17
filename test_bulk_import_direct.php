<?php
session_start();

// Set test session
$_SESSION['SESS_USER_ID'] = 1;
$_SESSION['CURRENT_BRANCH_ID'] = 1;

echo "<h2>Direct Bulk Import Test</h2>";

// Create a test CSV file first
$csvContent = "Full Name,Position,Phone Number,Address,Date Hired,Employee ID,Biometric ID,Company,Project Assignment,Time In,Time Out,Schedule\n";
$csvContent .= "<PERSON>,Manager,+1234567890,123 Main St,2024-01-15,,,ABC Corp,Project A,09:00,17:00,Monday,Tuesday,Wednesday,Thursday,Friday\n";
$csvContent .= "<PERSON>,Assistant,+1234567891,456 Oak Ave,2024-01-16,,,XYZ Inc,Project B,08:30,16:30,Monday,Tuesday,Wednesday,Thursday,Friday\n";

$testCsvFile = 'temp/test_bulk_import.csv';
file_put_contents($testCsvFile, $csvContent);

echo "<h3>Test CSV Created</h3>";
echo "File: $testCsvFile<br>";
echo "Size: " . filesize($testCsvFile) . " bytes<br>";
echo "<a href='$testCsvFile' download>Download Test CSV</a><br><br>";

// Simulate file upload
if(isset($_POST['test_bulk_import'])) {
    echo "<h3>Simulating Bulk Import</h3>";
    
    // Create fake $_FILES array
    $_FILES['excel_file'] = array(
        'name' => 'test_bulk_import.csv',
        'type' => 'text/csv',
        'tmp_name' => $testCsvFile,
        'error' => UPLOAD_ERR_OK,
        'size' => filesize($testCsvFile)
    );
    
    $_POST['auto_generate_ids'] = '1';
    $_POST['skip_duplicates'] = '1';
    
    echo "Simulated file upload:<br>";
    echo "Name: " . $_FILES['excel_file']['name'] . "<br>";
    echo "Size: " . $_FILES['excel_file']['size'] . "<br>";
    echo "Error: " . $_FILES['excel_file']['error'] . "<br>";
    echo "Temp Name: " . $_FILES['excel_file']['tmp_name'] . "<br><br>";
    
    // Capture output from bulk import
    ob_start();
    
    try {
        // Include the bulk import file
        include('function/bulkImportEmployees.php');
        $output = ob_get_clean();
        
        echo "<h4>Bulk Import Output:</h4>";
        echo "<pre>Raw Output Length: " . strlen($output) . " characters</pre>";
        echo "<pre>Raw Output: " . htmlspecialchars($output) . "</pre>";
        
        // Try to decode JSON
        $json = json_decode($output, true);
        if($json) {
            echo "<h4>Parsed JSON:</h4>";
            echo "<pre>" . print_r($json, true) . "</pre>";
            
            if($json['success']) {
                echo "<p style='color: green;'>✅ Bulk import successful!</p>";
            } else {
                echo "<p style='color: red;'>❌ Bulk import failed: " . $json['message'] . "</p>";
            }
        } else {
            echo "<h4>JSON Parse Error:</h4>";
            echo "<p>Error: " . json_last_error_msg() . "</p>";
            echo "<p>This indicates the PHP script is outputting something before the JSON response.</p>";
        }
        
    } catch(Exception $e) {
        ob_end_clean();
        echo "<h4>Exception Caught:</h4>";
        echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    }
    
} else {
    // Show form to test
    echo "<h3>Test Bulk Import</h3>";
    echo "<form method='post'>";
    echo "<input type='hidden' name='test_bulk_import' value='1'>";
    echo "<input type='submit' value='Test Bulk Import with Sample CSV'>";
    echo "</form>";
}

// Check for error logs
echo "<h3>Error Logs</h3>";
$errorLogs = glob('temp/*_errors.log');
if(empty($errorLogs)) {
    echo "No error logs found.<br>";
} else {
    foreach($errorLogs as $logFile) {
        echo "<h4>$logFile:</h4>";
        $content = file_get_contents($logFile);
        if(empty($content)) {
            echo "Log file is empty.<br>";
        } else {
            echo "<pre>" . htmlspecialchars($content) . "</pre>";
        }
    }
}
?>
