<?php
require_once('proc/config.php');

echo "<h2>Database Connection Test:</h2>";
if($link) {
    echo "✅ Database connected successfully!<br>";
} else {
    echo "❌ Database connection failed: " . mysqli_connect_error() . "<br>";
    exit;
}

echo "<h2>Current Users in Database:</h2>";
echo "<table border='1'>";
echo "<tr><th>ID</th><th>Username</th><th>Full Name</th><th>Password (stored)</th><th>User Level</th></tr>";

$qry = "SELECT * FROM tb_user";
$result = mysql_query($qry);

if($result && mysql_num_rows($result) > 0) {
    while($row = mysql_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['IDno'] . "</td>";
        echo "<td>" . $row['user_name'] . "</td>";
        echo "<td>" . $row['full_name'] . "</td>";
        echo "<td>" . $row['pass_word'] . "</td>";
        echo "<td>" . $row['userlevel'] . "</td>";
        echo "</tr>";
    }
} else {
    echo "<tr><td colspan='5'>No users found or query error: " . mysql_error() . "</td></tr>";
}

echo "</table>";

echo "<h3>MD5 Hash of 'admin':</h3>";
echo "MD5('admin') = " . md5('admin');

echo "<h3>Test Login Values:</h3>";
if(isset($_POST['test_username']) && isset($_POST['test_password'])) {
    $test_username = clean($_POST['test_username']);
    $test_password = md5(clean($_POST['test_password']));

    echo "Input Username: " . htmlspecialchars($test_username) . "<br>";
    echo "Input Password (original): " . htmlspecialchars($_POST['test_password']) . "<br>";
    echo "Input Password (MD5): " . $test_password . "<br>";

    $test_qry = "SELECT * FROM tb_user WHERE user_name='$test_username' AND pass_word='$test_password'";
    $test_result = mysql_query($test_qry);

    echo "Query: " . htmlspecialchars($test_qry) . "<br>";
    echo "Result: " . (($test_result && mysql_num_rows($test_result) > 0) ? "✅ MATCH FOUND!" : "❌ NO MATCH") . "<br>";

    if(!$test_result) {
        echo "Query Error: " . mysql_error() . "<br>";
    }
}
?>

<form method="post">
    <h3>Test Login:</h3>
    Username: <input type="text" name="test_username" value="admin"><br>
    Password: <input type="password" name="test_password" value="admin"><br>
    <input type="submit" value="Test">
</form>
