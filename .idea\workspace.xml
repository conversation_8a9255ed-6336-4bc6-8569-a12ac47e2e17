<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="545cc4b4-4f6a-45d7-833c-927b879c2e82" name="Default" comment="" />
    <ignored path="access.iws" />
    <ignored path=".idea/workspace.xml" />
    <ignored path=".idea/dataSources.local.xml" />
    <option name="EXCLUDED_CONVERTED_TO_IGNORED" value="true" />
    <option name="TRACKING_ENABLED" value="true" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager" flattened_view="true" show_ignored="false" />
  <component name="CreatePatchCommitExecutor">
    <option name="PATCH_PATH" value="" />
  </component>
  <component name="DatabaseView">
    <option name="GROUP_SCHEMA" value="true" />
    <option name="GROUP_CONTENTS" value="false" />
    <option name="SORT_POSITIONED" value="false" />
    <option name="SHOW_TABLE_DETAILS" value="true" />
    <option name="SHOW_EMPTY_GROUPS" value="false" />
    <PATH>
      <PATH_ELEMENT>
        <option name="myItemId" />
        <option name="myItemType" value="com.intellij.database.view.DatabaseStructure$Root" />
      </PATH_ELEMENT>
      <PATH_ELEMENT>
        <option name="myItemId" value="MySQL - db_eas@localhost" />
        <option name="myItemType" value="com.intellij.database.view.DbNodeDescriptor" />
      </PATH_ELEMENT>
    </PATH>
  </component>
  <component name="ExecutionTargetManager" SELECTED_TARGET="default_target" />
  <component name="FavoritesManager">
    <favorites_list name="access" />
  </component>
  <component name="FileEditorManager">
    <leaf SIDE_TABS_SIZE_LIMIT_KEY="300">
      <file leaf-file-name="pg_EditEmployee.php" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/pg_EditEmployee.php">
          <provider selected="true" editor-type-id="text-editor">
            <state vertical-scroll-proportion="-16.346153">
              <caret line="188" column="16" selection-start-line="188" selection-start-column="16" selection-end-line="188" selection-end-column="16" />
              <folding>
                <marker date="1489044455397" expanded="true" signature="396:551" placeholder="SELECT `COLU... COLUMNS" />
                <marker date="1489044455397" expanded="true" signature="1208:1291" placeholder="UPDATE `tbl_... " />
                <marker date="1489044455397" expanded="true" signature="2114:2226" placeholder="UPDATE `tbl_... " />
                <marker date="1489044455397" expanded="true" signature="2466:2570" placeholder="UPDATE `tbl_... " />
                <marker date="1489044455397" expanded="true" signature="2750:2808" placeholder="SELECT * FRO... tbl_personnel" />
                <marker date="1489044455397" expanded="true" signature="7240:7268" placeholder="SELECT * FRO... tbl_arealist" />
              </folding>
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="pg_dashboard.php" pinned="false" current-in-tab="true">
        <entry file="file://$PROJECT_DIR$/pg_dashboard.php">
          <provider selected="true" editor-type-id="text-editor">
            <state vertical-scroll-proportion="0.25030676">
              <caret line="33" column="46" selection-start-line="33" selection-start-column="46" selection-end-line="33" selection-end-column="46" />
              <folding>
                <marker date="1490165326102" expanded="true" signature="1260:1293" placeholder="SELECT AreaN... tbl_arealist" />
                <marker date="1490165326102" expanded="true" signature="1871:2383" placeholder="SELECT UserI... tbl_datalogs" />
                <marker date="1490165326102" expanded="true" signature="4315:4355" placeholder="..." />
                <marker date="1490165326102" expanded="true" signature="4315:4408" placeholder="..." />
                <marker date="1490165326102" expanded="true" signature="4315:4412" placeholder="..." />
                <marker date="1490165326102" expanded="true" signature="4315:4423" placeholder="..." />
                <marker date="1490165326102" expanded="true" signature="4315:4428" placeholder="..." />
                <marker date="1490165326102" expanded="true" signature="4315:4580" placeholder="..." />
                <marker date="1490165326102" expanded="true" signature="4315:4768" placeholder="..." />
              </folding>
            </state>
          </provider>
        </entry>
      </file>
    </leaf>
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="JavaScript File" />
      </list>
    </option>
  </component>
  <component name="IdeDocumentHistory">
    <option name="CHANGED_PATHS">
      <list>
        <option value="$PROJECT_DIR$/function/func_AddPersonnel.php" />
        <option value="$PROJECT_DIR$/js/script_EditPersonnel.js" />
        <option value="$PROJECT_DIR$/pg_accounts.php" />
        <option value="$PROJECT_DIR$/pg_EditEmployee.php" />
        <option value="$PROJECT_DIR$/function/main_function.php" />
        <option value="$PROJECT_DIR$/proc/config.php" />
        <option value="$PROJECT_DIR$/include/include-head.php" />
        <option value="$PROJECT_DIR$/js/script_pg_attendance.js" />
        <option value="$PROJECT_DIR$/report_menu.php" />
        <option value="$PROJECT_DIR$/include/include-main.php" />
        <option value="$PROJECT_DIR$/pg_attendance.php" />
        <option value="$PROJECT_DIR$/css/styles.css" />
        <option value="$PROJECT_DIR$/pg_dashboard.php" />
      </list>
    </option>
  </component>
  <component name="JsBuildToolGruntFileManager" detection-done="true" />
  <component name="JsBuildToolPackageJson" detection-done="true" />
  <component name="JsGulpfileManager">
    <detection-done>true</detection-done>
  </component>
  <component name="PhpServers">
    <servers />
  </component>
  <component name="PhpWorkspaceProjectConfiguration" backward_compatibility_performed="true" />
  <component name="ProjectFrameBounds">
    <option name="x" value="-8" />
    <option name="y" value="-8" />
    <option name="width" value="1296" />
    <option name="height" value="1000" />
  </component>
  <component name="ProjectLevelVcsManager" settingsEditedManually="false">
    <OptionsSetting value="true" id="Add" />
    <OptionsSetting value="true" id="Remove" />
    <OptionsSetting value="true" id="Checkout" />
    <OptionsSetting value="true" id="Update" />
    <OptionsSetting value="true" id="Status" />
    <OptionsSetting value="true" id="Edit" />
    <ConfirmationsSetting value="0" id="Add" />
    <ConfirmationsSetting value="0" id="Remove" />
  </component>
  <component name="ProjectView">
    <navigator currentView="ProjectPane" proportions="" version="1">
      <flattenPackages />
      <showMembers />
      <showModules />
      <showLibraryContents />
      <hideEmptyPackages />
      <abbreviatePackageNames />
      <autoscrollToSource />
      <autoscrollFromSource />
      <sortByType />
      <manualOrder />
      <foldersAlwaysOnTop value="true" />
    </navigator>
    <panes>
      <pane id="Scope" />
      <pane id="ProjectPane">
        <subPane>
          <PATH>
            <PATH_ELEMENT>
              <option name="myItemId" value="access" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.ProjectViewProjectNode" />
            </PATH_ELEMENT>
          </PATH>
          <PATH>
            <PATH_ELEMENT>
              <option name="myItemId" value="access" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.ProjectViewProjectNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="access" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
          </PATH>
          <PATH>
            <PATH_ELEMENT>
              <option name="myItemId" value="access" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.ProjectViewProjectNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="access" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="proc" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
          </PATH>
          <PATH>
            <PATH_ELEMENT>
              <option name="myItemId" value="access" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.ProjectViewProjectNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="access" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="include" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
          </PATH>
          <PATH>
            <PATH_ELEMENT>
              <option name="myItemId" value="access" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.ProjectViewProjectNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="access" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
            <PATH_ELEMENT>
              <option name="myItemId" value="css" />
              <option name="myItemType" value="com.intellij.ide.projectView.impl.nodes.PsiDirectoryNode" />
            </PATH_ELEMENT>
          </PATH>
        </subPane>
      </pane>
      <pane id="Scratches" />
    </panes>
  </component>
  <component name="PropertiesComponent">
    <property name="settings.editor.selected.configurable" value="SQL Dialects" />
    <property name="settings.editor.splitter.proportion" value="0.2" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$" />
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="js-jscs-nodeInterpreter" value="C:\Program Files\nodejs\node.exe" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\xampp\htdocs\access" />
      <recent name="C:\xampp\htdocs\access\dist" />
      <recent name="C:\xampp\htdocs\access\images" />
      <recent name="C:\xampp\htdocs\access\js" />
      <recent name="C:\xampp\htdocs\access\css" />
    </key>
  </component>
  <component name="RunManager">
    <configuration default="true" type="JavascriptDebugType" factoryName="JavaScript Debug">
      <method />
    </configuration>
    <configuration default="true" type="PHPUnitRunConfigurationType" factoryName="PHPUnit">
      <TestRunner />
      <method />
    </configuration>
    <configuration default="true" type="PhpBehatConfigurationType" factoryName="Behat">
      <BehatRunner />
      <method />
    </configuration>
    <configuration default="true" type="PhpLocalRunConfigurationType" factoryName="PHP Console">
      <method />
    </configuration>
    <configuration default="true" type="js.build_tools.gulp" factoryName="Gulp.js">
      <node-options />
      <gulpfile />
      <tasks />
      <arguments />
      <envs />
      <method />
    </configuration>
    <configuration default="true" type="js.build_tools.npm" factoryName="npm">
      <command value="run-script" />
      <scripts />
      <envs />
      <method />
    </configuration>
  </component>
  <component name="ShelveChangesManager" show_recycled="false" />
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="545cc4b4-4f6a-45d7-833c-927b879c2e82" name="Default" comment="" />
      <created>1488954160226</created>
      <option name="number" value="Default" />
      <updated>1488954160226</updated>
    </task>
    <servers />
  </component>
  <component name="ToolWindowManager">
    <frame x="-8" y="-8" width="1296" height="1000" extended-state="6" />
    <editor active="true" />
    <layout>
      <window_info id="Project" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="true" show_stripe_button="true" weight="0.19579288" sideWeight="0.5" order="2" side_tool="false" content_ui="combo" />
      <window_info id="TODO" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="9" side_tool="false" content_ui="tabs" />
      <window_info id="Event Log" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="0" side_tool="true" content_ui="tabs" />
      <window_info id="Database" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.32928804" sideWeight="0.5" order="0" side_tool="false" content_ui="tabs" />
      <window_info id="Version Control" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="1" side_tool="false" content_ui="tabs" />
      <window_info id="Structure" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="3" side_tool="false" content_ui="tabs" />
      <window_info id="Terminal" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="2" side_tool="false" content_ui="tabs" />
      <window_info id="Favorites" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="1" side_tool="true" content_ui="tabs" />
      <window_info id="Cvs" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="7" side_tool="false" content_ui="tabs" />
      <window_info id="Message" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="3" side_tool="false" content_ui="tabs" />
      <window_info id="Commander" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.4" sideWeight="0.5" order="1" side_tool="false" content_ui="tabs" />
      <window_info id="Inspection" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.4" sideWeight="0.5" order="8" side_tool="false" content_ui="tabs" />
      <window_info id="Run" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="5" side_tool="false" content_ui="tabs" />
      <window_info id="Gulp" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="0" side_tool="true" content_ui="tabs" />
      <window_info id="Hierarchy" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="3" side_tool="false" content_ui="combo" />
      <window_info id="Find" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="4" side_tool="false" content_ui="tabs" />
      <window_info id="Ant Build" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="2" side_tool="false" content_ui="tabs" />
      <window_info id="Debug" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.4" sideWeight="0.5" order="6" side_tool="false" content_ui="tabs" />
    </layout>
    <layout-to-restore>
      <window_info id="TODO" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="9" side_tool="false" content_ui="tabs" />
      <window_info id="Cvs" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="7" side_tool="false" content_ui="tabs" />
      <window_info id="Message" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="3" side_tool="false" content_ui="tabs" />
      <window_info id="Commander" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.4" sideWeight="0.5" order="1" side_tool="false" content_ui="tabs" />
      <window_info id="Event Log" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="0" side_tool="true" content_ui="tabs" />
      <window_info id="Inspection" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.4" sideWeight="0.5" order="8" side_tool="false" content_ui="tabs" />
      <window_info id="Version Control" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="1" side_tool="false" content_ui="tabs" />
      <window_info id="Run" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="5" side_tool="false" content_ui="tabs" />
      <window_info id="Terminal" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="2" side_tool="false" content_ui="tabs" />
      <window_info id="Gulp" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="0" side_tool="true" content_ui="tabs" />
      <window_info id="Project" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="true" show_stripe_button="true" weight="0.18608414" sideWeight="0.5" order="2" side_tool="false" content_ui="combo" />
      <window_info id="Hierarchy" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="3" side_tool="false" content_ui="combo" />
      <window_info id="Database" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.32928804" sideWeight="0.5" order="0" side_tool="false" content_ui="tabs" />
      <window_info id="Find" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="4" side_tool="false" content_ui="tabs" />
      <window_info id="Structure" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="3" side_tool="false" content_ui="tabs" />
      <window_info id="Ant Build" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="2" side_tool="false" content_ui="tabs" />
      <window_info id="Favorites" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="1" side_tool="true" content_ui="tabs" />
      <window_info id="Debug" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.4" sideWeight="0.5" order="6" side_tool="false" content_ui="tabs" />
    </layout-to-restore>
  </component>
  <component name="Vcs.Log.UiProperties">
    <option name="RECENTLY_FILTERED_USER_GROUPS">
      <collection />
    </option>
    <option name="RECENTLY_FILTERED_BRANCH_GROUPS">
      <collection />
    </option>
  </component>
  <component name="VcsContentAnnotationSettings">
    <option name="myLimit" value="**********" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <option name="time" value="2" />
    </breakpoint-manager>
    <watches-manager />
  </component>
  <component name="editorHistoryManager">
    <entry file="file://$PROJECT_DIR$/pg_attendance.php">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.0">
          <caret line="0" column="0" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/pg_accounts.php">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.0">
          <caret line="332" column="14" selection-start-line="332" selection-start-column="14" selection-end-line="332" selection-end-column="14" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/css/styles.css">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.0">
          <caret line="628" column="20" selection-start-line="628" selection-start-column="20" selection-end-line="628" selection-end-column="20" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/pg_EditEmployee.php">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.0">
          <caret line="171" column="34" selection-start-line="171" selection-start-column="34" selection-end-line="171" selection-end-column="34" />
          <folding>
            <marker date="1489044455397" expanded="true" signature="396:551" placeholder="SELECT `COLU... COLUMNS" />
            <marker date="1489044455397" expanded="true" signature="1208:1291" placeholder="UPDATE `tbl_... " />
            <marker date="1489044455397" expanded="true" signature="2114:2226" placeholder="UPDATE `tbl_... " />
            <marker date="1489044455397" expanded="true" signature="2466:2570" placeholder="UPDATE `tbl_... " />
            <marker date="1489044455397" expanded="true" signature="2750:2808" placeholder="SELECT * FRO... tbl_personnel" />
            <marker date="1489044455397" expanded="true" signature="7240:7268" placeholder="SELECT * FRO... tbl_arealist" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/include/include-main.php">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.0">
          <caret line="37" column="52" selection-start-line="37" selection-start-column="52" selection-end-line="37" selection-end-column="52" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/pg_reports.php">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.0">
          <caret line="217" column="20" selection-start-line="217" selection-start-column="15" selection-end-line="217" selection-end-column="20" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/report_menu.php">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.0">
          <caret line="21" column="0" selection-start-line="21" selection-start-column="0" selection-end-line="21" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/pg_attendance.php">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.0">
          <caret line="0" column="0" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/js/script_EditPersonnel.js">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.0">
          <caret line="27" column="34" selection-start-line="27" selection-start-column="33" selection-end-line="27" selection-end-column="34" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/pg_accounts.php">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.0">
          <caret line="301" column="74" selection-start-line="301" selection-start-column="67" selection-end-line="301" selection-end-column="74" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/css/styles.css">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.0">
          <caret line="622" column="0" selection-start-line="622" selection-start-column="0" selection-end-line="622" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/proc/config.php">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.0">
          <caret line="32" column="9" selection-start-line="32" selection-start-column="9" selection-end-line="32" selection-end-column="9" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/function/func_AddPersonnel.php">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.0">
          <caret line="20" column="0" selection-start-line="20" selection-start-column="0" selection-end-line="23" selection-end-column="39" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/pg_EditEmployee.php">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.0">
          <caret line="171" column="34" selection-start-line="171" selection-start-column="34" selection-end-line="171" selection-end-column="34" />
          <folding>
            <marker date="1489044455397" expanded="true" signature="396:551" placeholder="SELECT `COLU... COLUMNS" />
            <marker date="1489044455397" expanded="true" signature="1208:1291" placeholder="UPDATE `tbl_... " />
            <marker date="1489044455397" expanded="true" signature="2114:2226" placeholder="UPDATE `tbl_... " />
            <marker date="1489044455397" expanded="true" signature="2466:2570" placeholder="UPDATE `tbl_... " />
            <marker date="1489044455397" expanded="true" signature="2750:2808" placeholder="SELECT * FRO... tbl_personnel" />
            <marker date="1489044455397" expanded="true" signature="7240:7268" placeholder="SELECT * FRO... tbl_arealist" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/include/include-main.php">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.0">
          <caret line="37" column="52" selection-start-line="37" selection-start-column="52" selection-end-line="37" selection-end-column="52" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/pg_reports.php">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.0">
          <caret line="217" column="20" selection-start-line="217" selection-start-column="15" selection-end-line="217" selection-end-column="20" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/pg_attendance.php">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.0">
          <caret line="0" column="0" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/report_menu.php">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.0">
          <caret line="21" column="0" selection-start-line="21" selection-start-column="0" selection-end-line="21" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/js/script_EditPersonnel.js">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.0">
          <caret line="27" column="34" selection-start-line="27" selection-start-column="33" selection-end-line="27" selection-end-column="34" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/pg_accounts.php">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.0">
          <caret line="0" column="0" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/css/styles.css">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.0">
          <caret line="615" column="55" selection-start-line="615" selection-start-column="55" selection-end-line="615" selection-end-column="55" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/include/include-head.php">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.0">
          <caret line="39" column="34" selection-start-line="39" selection-start-column="30" selection-end-line="39" selection-end-column="34" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/css/bootstrap-checkbox.css">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.0">
          <caret line="0" column="0" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/js/script_AddPersonnel.js">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.0">
          <caret line="58" column="9" selection-start-line="58" selection-start-column="9" selection-end-line="58" selection-end-column="9" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/js/bootstrap-checkbox.js">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.0">
          <caret line="0" column="0" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/js/bootstrap-checkbox.min.js">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.044795785">
          <caret line="6" column="26" selection-start-line="6" selection-start-column="26" selection-end-line="6" selection-end-column="26" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/css/bootstrap-checkbox.css">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.0">
          <caret line="0" column="0" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/js/script_AddPersonnel.js">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.020238096">
          <caret line="58" column="9" selection-start-line="58" selection-start-column="9" selection-end-line="58" selection-end-column="9" />
        </state>
      </provider>
    </entry>
    <entry file="jar://$APPLICATION_HOME_DIR$/lib/phpstorm.jar!/resources/html5-schema/html5/core-scripting.rnc">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.33333334">
          <caret line="176" column="18" selection-start-line="176" selection-start-column="18" selection-end-line="176" selection-end-column="18" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/pg_EditVisitor.php">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.0">
          <caret line="0" column="0" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/function/func_AddPersonnel.php">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.0">
          <caret line="20" column="0" selection-start-line="20" selection-start-column="0" selection-end-line="23" selection-end-column="39" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/js/script_EditPersonnel.js">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.0">
          <caret line="27" column="34" selection-start-line="27" selection-start-column="33" selection-end-line="27" selection-end-column="34" />
        </state>
      </provider>
    </entry>
    <entry file="file://$APPLICATION_CONFIG_DIR$/consoles/db/7634bd06-0011-4b09-9a20-9f57211ca8f4/console.sql">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.0">
          <caret line="0" column="0" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$APPLICATION_CONFIG_DIR$/consoles/db/afc53e9e-825e-4104-abbd-aaa97f709688/console.sql">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.0">
          <caret line="0" column="0" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/pg_Receipt.php">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.31326783">
          <caret line="15" column="0" selection-start-line="15" selection-start-column="0" selection-end-line="15" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/proc/config.php">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="1.0417178">
          <caret line="156" column="32" selection-start-line="156" selection-start-column="32" selection-end-line="156" selection-end-column="32" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/Readme.txt">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.30321047">
          <caret line="15" column="0" selection-start-line="15" selection-start-column="0" selection-end-line="15" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/js/General.js">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.0">
          <caret line="0" column="0" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/include/include-body.php">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="-25.5">
          <caret line="39" column="31" selection-start-line="39" selection-start-column="31" selection-end-line="39" selection-end-column="31" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/pg_accounts.php">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.3791411">
          <caret line="305" column="83" selection-start-line="305" selection-start-column="83" selection-end-line="305" selection-end-column="83" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/dist/battatech-excelexport/jquery.battatech.excelexport.js">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.0">
          <caret line="0" column="0" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/js/moment.js">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.********">
          <caret line="9" column="4" selection-start-line="9" selection-start-column="4" selection-end-line="9" selection-end-column="4" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/include/include-head.php">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="-20.923077">
          <caret line="32" column="62" selection-start-line="32" selection-start-column="62" selection-end-line="32" selection-end-column="62" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/js/script_pg_attendance.js">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.28299645">
          <caret line="14" column="21" selection-start-line="14" selection-start-column="21" selection-end-line="14" selection-end-column="21" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/include/include-main.php">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.70920247">
          <caret line="37" column="53" selection-start-line="37" selection-start-column="53" selection-end-line="37" selection-end-column="53" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/pg_attendance.php">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.95582825">
          <caret line="153" column="33" selection-start-line="153" selection-start-column="33" selection-end-line="153" selection-end-column="33" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/css/styles.css">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="-27.423077">
          <caret line="646" column="47" selection-start-line="646" selection-start-column="47" selection-end-line="646" selection-end-column="47" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/report_menu.php">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.79263806">
          <caret line="38" column="59" selection-start-line="38" selection-start-column="59" selection-end-line="38" selection-end-column="59" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/pg_reports.php">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="-1.2306749">
          <caret line="217" column="20" selection-start-line="217" selection-start-column="15" selection-end-line="217" selection-end-column="20" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/pg_EditEmployee.php">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="-16.346153">
          <caret line="188" column="16" selection-start-line="188" selection-start-column="16" selection-end-line="188" selection-end-column="16" />
          <folding>
            <marker date="1489044455397" expanded="true" signature="396:551" placeholder="SELECT `COLU... COLUMNS" />
            <marker date="1489044455397" expanded="true" signature="1208:1291" placeholder="UPDATE `tbl_... " />
            <marker date="1489044455397" expanded="true" signature="2114:2226" placeholder="UPDATE `tbl_... " />
            <marker date="1489044455397" expanded="true" signature="2466:2570" placeholder="UPDATE `tbl_... " />
            <marker date="1489044455397" expanded="true" signature="2750:2808" placeholder="SELECT * FRO... tbl_personnel" />
            <marker date="1489044455397" expanded="true" signature="7240:7268" placeholder="SELECT * FRO... tbl_arealist" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/pg_dashboard.php">
      <provider selected="true" editor-type-id="text-editor">
        <state vertical-scroll-proportion="0.25030676">
          <caret line="33" column="46" selection-start-line="33" selection-start-column="46" selection-end-line="33" selection-end-column="46" />
          <folding>
            <marker date="1490165326102" expanded="true" signature="1260:1293" placeholder="SELECT AreaN... tbl_arealist" />
            <marker date="1490165326102" expanded="true" signature="1871:2383" placeholder="SELECT UserI... tbl_datalogs" />
            <marker date="1490165326102" expanded="true" signature="4315:4355" placeholder="..." />
            <marker date="1490165326102" expanded="true" signature="4315:4408" placeholder="..." />
            <marker date="1490165326102" expanded="true" signature="4315:4412" placeholder="..." />
            <marker date="1490165326102" expanded="true" signature="4315:4423" placeholder="..." />
            <marker date="1490165326102" expanded="true" signature="4315:4428" placeholder="..." />
            <marker date="1490165326102" expanded="true" signature="4315:4580" placeholder="..." />
            <marker date="1490165326102" expanded="true" signature="4315:4768" placeholder="..." />
          </folding>
        </state>
      </provider>
    </entry>
  </component>
</project>