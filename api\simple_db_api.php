<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-API-Key');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Database config
$host = 'localhost';
$username = 'imasedup_saqib';
$password = 'P@ssw0rd@imas';
$database = 'imasedup_biometrics';

// API Security (optional - uncomment to enable)
// $api_key = 'your-secure-api-key-here';
// $provided_key = $_GET['api_key'] ?? $_SERVER['HTTP_X_API_KEY'] ?? '';
// if ($provided_key !== $api_key) {
//     echo json_encode(['status' => 'error', 'message' => 'Invalid API key']);
//     exit();
// }

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    $action = $_GET['action'] ?? '';
    $method = $_SERVER['REQUEST_METHOD'];

    // Handle POST requests for data sync
    if ($method === 'POST' && in_array($action, ['sync_employees', 'sync_attendance'])) {
        $input = json_decode(file_get_contents('php://input'), true);

        if ($action === 'sync_employees') {
            handleEmployeeSync($pdo, $input);
        } elseif ($action === 'sync_attendance') {
            handleAttendanceSync($pdo, $input);
        }
        exit();
    }

    switch($action) {
        case 'test':
            echo json_encode(['status' => 'success', 'message' => 'Connected']);
            break;
            
        case 'get_employees':
            $limit = intval($_GET['limit'] ?? 100);
            $offset = intval($_GET['offset'] ?? 0);
            $branch = $_GET['branch'] ?? '';

            // Build SQL with proper syntax for older MySQL versions
            $sql = "SELECT * FROM tbl_personnel";
            $params = [];

            if ($branch) {
                $sql .= " WHERE AgencyCompany = ?";
                $params[] = $branch;
            }

            $sql .= " ORDER BY AccessID LIMIT $limit OFFSET $offset";

            if ($params) {
                $stmt = $pdo->prepare($sql);
                $stmt->execute($params);
            } else {
                $stmt = $pdo->query($sql);
            }
            $employees = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Get total count
            $count_sql = "SELECT COUNT(*) as total FROM tbl_personnel";
            if ($branch) {
                $count_sql .= " WHERE AgencyCompany = ?";
                $count_stmt = $pdo->prepare($count_sql);
                $count_stmt->execute([$branch]);
            } else {
                $count_stmt = $pdo->query($count_sql);
            }
            $total = $count_stmt->fetch(PDO::FETCH_ASSOC)['total'];

            echo json_encode([
                'status' => 'success',
                'data' => $employees,
                'total' => $total,
                'limit' => $limit,
                'offset' => $offset
            ]);
            break;
            
        case 'add_attendance':
            $accessId = $_POST['access_id'] ?? '';
            $timeFlag = $_POST['time_flag'] ?? 'IN';
            $timeRecord = date('Y-m-d H:i:s');
            $remarks = $_POST['remarks'] ?? 'API Entry';
            
            // Get employee's full details including refno
            $stmt = $pdo->prepare("SELECT FullName, AccessArea, Position, AgencyCompany, ContactNo, EntryID FROM tbl_personnel WHERE AccessID = ?");
            $stmt->execute([$accessId]);
            $employee = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($employee) {
                $fullName = $employee['FullName'];
                $accessArea = $employee['AccessArea'] ?? 'Default Area';
                $position = $employee['Position'] ?? '';
                $company = $employee['AgencyCompany'] ?? '';
                $contact = $employee['ContactNo'] ?? '';
                $refno = $employee['EntryID'] ?? '1';
                
                // Insert attendance record with all required fields including Approved_Status
                $stmt = $pdo->prepare("INSERT INTO tbl_in_out (AccessID, FullName, TimeRecord, TimeFlag, Remarks, AccessArea, Status, ContactPerson, Company, refno, Approved_Status, Logs) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                $stmt->execute([$accessId, $fullName, $timeRecord, $timeFlag, $remarks, $accessArea, 'ACCESS GRANTED', $contact, $company, $refno, 'PENDING', 'API_ENTRY']);
                
                echo json_encode(['status' => 'success', 'message' => 'Attendance recorded for ' . $fullName]);
            } else {
                echo json_encode(['status' => 'error', 'message' => 'Employee not found']);
            }
            break;

        case 'get_attendance':
            $limit = intval($_GET['limit'] ?? 100);
            $offset = intval($_GET['offset'] ?? 0);
            $date_from = $_GET['date_from'] ?? date('Y-m-d', strtotime('-30 days'));
            $date_to = $_GET['date_to'] ?? date('Y-m-d');
            $access_id = $_GET['access_id'] ?? '';

            $sql = "SELECT * FROM tbl_in_out WHERE DATE(TimeRecord) BETWEEN ? AND ?";
            $params = [$date_from, $date_to];

            if ($access_id) {
                $sql .= " AND AccessID = ?";
                $params[] = $access_id;
            }

            $sql .= " ORDER BY TimeRecord DESC LIMIT $limit OFFSET $offset";

            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            $attendance = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Get total count
            $count_sql = "SELECT COUNT(*) as total FROM tbl_in_out WHERE DATE(TimeRecord) BETWEEN ? AND ?";
            $count_params = [$date_from, $date_to];
            if ($access_id) {
                $count_sql .= " AND AccessID = ?";
                $count_params[] = $access_id;
            }

            $count_stmt = $pdo->prepare($count_sql);
            $count_stmt->execute($count_params);
            $total = $count_stmt->fetch(PDO::FETCH_ASSOC)['total'];

            echo json_encode([
                'status' => 'success',
                'data' => $attendance,
                'total' => $total,
                'limit' => $limit,
                'offset' => $offset,
                'date_from' => $date_from,
                'date_to' => $date_to
            ]);
            break;

        case 'get_stats':
            $stats = [];

            // Employee count
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM tbl_personnel");
            $stats['employees'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

            // Attendance count
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM tbl_in_out");
            $stats['attendance'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

            // Recent attendance (last 30 days)
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM tbl_in_out WHERE DATE(TimeRecord) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)");
            $stats['recent_attendance'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

            // Branch count
            $stmt = $pdo->query("SELECT COUNT(DISTINCT AgencyCompany) as count FROM tbl_personnel WHERE AgencyCompany IS NOT NULL AND AgencyCompany != ''");
            $stats['branches'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

            echo json_encode(['status' => 'success', 'data' => $stats]);
            break;

        default:
            echo json_encode(['status' => 'error', 'message' => 'Invalid action']);
    }
    
} catch(PDOException $e) {
    echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
}

// Handle employee sync
function handleEmployeeSync($pdo, $input) {
    if (!isset($input['employees']) || !is_array($input['employees'])) {
        echo json_encode(['status' => 'error', 'message' => 'Invalid employee data']);
        return;
    }

    $created = 0;
    $updated = 0;
    $errors = 0;

    foreach ($input['employees'] as $employee) {
        if (!isset($employee['AccessID']) || empty($employee['AccessID'])) {
            $errors++;
            continue;
        }

        try {
            // Check if employee exists
            $check_stmt = $pdo->prepare("SELECT COUNT(*) FROM tbl_personnel WHERE AccessID = ?");
            $check_stmt->execute([$employee['AccessID']]);
            $exists = $check_stmt->fetchColumn() > 0;

            if ($exists) {
                // Update existing employee
                $update_stmt = $pdo->prepare("
                    UPDATE tbl_personnel SET
                        EmployeeID = ?, FullName = ?, Position = ?, AgencyCompany = ?,
                        ContactNo = ?, DateHired = ?, Status = ?, TimeIN = ?, TimeOut = ?
                    WHERE AccessID = ?
                ");

                $update_stmt->execute([
                    $employee['EmployeeID'] ?? '',
                    $employee['FullName'] ?? '',
                    $employee['Position'] ?? '',
                    $employee['AgencyCompany'] ?? '',
                    $employee['ContactNo'] ?? '',
                    $employee['DateHired'] ?? '',
                    $employee['Status'] ?? '',
                    $employee['TimeIN'] ?? '',
                    $employee['TimeOut'] ?? '',
                    $employee['AccessID']
                ]);
                $updated++;
            } else {
                // Insert new employee
                $insert_stmt = $pdo->prepare("
                    INSERT INTO tbl_personnel (EmployeeID, AccessID, FullName, Position, AgencyCompany, ContactNo, DateHired, Status, TimeIN, TimeOut)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");

                $insert_stmt->execute([
                    $employee['EmployeeID'] ?? '',
                    $employee['AccessID'],
                    $employee['FullName'] ?? '',
                    $employee['Position'] ?? '',
                    $employee['AgencyCompany'] ?? '',
                    $employee['ContactNo'] ?? '',
                    $employee['DateHired'] ?? '',
                    $employee['Status'] ?? '',
                    $employee['TimeIN'] ?? '',
                    $employee['TimeOut'] ?? ''
                ]);
                $created++;
            }
        } catch (PDOException $e) {
            $errors++;
        }
    }

    echo json_encode([
        'status' => 'success',
        'message' => 'Employee sync completed',
        'created' => $created,
        'updated' => $updated,
        'errors' => $errors,
        'total_processed' => count($input['employees'])
    ]);
}

// Handle attendance sync
function handleAttendanceSync($pdo, $input) {
    if (!isset($input['attendance']) || !is_array($input['attendance'])) {
        echo json_encode(['status' => 'error', 'message' => 'Invalid attendance data']);
        return;
    }

    $created = 0;
    $skipped = 0;
    $errors = 0;

    foreach ($input['attendance'] as $record) {
        if (!isset($record['AccessID']) || !isset($record['TimeRecord']) || !isset($record['TimeFlag'])) {
            $errors++;
            continue;
        }

        try {
            // Check if record exists (prevent duplicates)
            $check_stmt = $pdo->prepare("
                SELECT COUNT(*) FROM tbl_in_out
                WHERE AccessID = ? AND TimeRecord = ? AND TimeFlag = ?
            ");
            $check_stmt->execute([$record['AccessID'], $record['TimeRecord'], $record['TimeFlag']]);
            $exists = $check_stmt->fetchColumn() > 0;

            if (!$exists) {
                // Insert new attendance record
                $insert_stmt = $pdo->prepare("
                    INSERT INTO tbl_in_out (AccessID, FullName, TimeRecord, TimeFlag, AccessArea)
                    VALUES (?, ?, ?, ?, ?)
                ");

                $insert_stmt->execute([
                    $record['AccessID'],
                    $record['FullName'] ?? '',
                    $record['TimeRecord'],
                    $record['TimeFlag'],
                    $record['AccessArea'] ?? ''
                ]);
                $created++;
            } else {
                $skipped++;
            }
        } catch (PDOException $e) {
            $errors++;
        }
    }

    echo json_encode([
        'status' => 'success',
        'message' => 'Attendance sync completed',
        'created' => $created,
        'skipped' => $skipped,
        'errors' => $errors,
        'total_processed' => count($input['attendance'])
    ]);
}
?>





