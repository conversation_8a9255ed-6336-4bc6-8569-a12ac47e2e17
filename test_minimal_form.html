<!DOCTYPE html>
<html>
<head>
    <title>Minimal Import Test</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h2>Minimal Import Test</h2>
    
    <h3>Step 1: Create Sample CSV</h3>
    <button onclick="createSample()">Create Sample CSV</button>
    <div id="sample-result"></div>
    
    <h3>Step 2: Test Minimal Import</h3>
    <form id="minimalForm" enctype="multipart/form-data">
        <input type="file" id="minimalFile" name="excel_file" accept=".csv"><br><br>
        <button type="button" onclick="testMinimal()">Test Minimal Import</button>
    </form>
    
    <div id="minimal-result"></div>

    <script>
    function createSample() {
        $('#sample-result').html('Creating sample...');

        $.ajax({
            url: 'create_sample_csv.php',
            type: 'GET',
            dataType: 'json',
            success: function(data) {
                if(data.success) {
                    $('#sample-result').html('✅ Sample created: <a href="' + data.file + '" download>' + data.file + '</a>');
                } else {
                    $('#sample-result').html('❌ Error: ' + data.message);
                }
            },
            error: function(xhr, status, error) {
                $('#sample-result').html('❌ AJAX Error: ' + error + '<br>Status: ' + status + '<br>Response: ' + xhr.responseText);
            }
        });
    }
    
    function testMinimal() {
        const fileInput = document.getElementById('minimalFile');
        if(!fileInput.files.length) {
            alert('Please select a CSV file first');
            return;
        }

        const formData = new FormData(document.getElementById('minimalForm'));

        $('#minimal-result').html('Testing minimal import...');

        $.ajax({
            url: 'test_minimal_import.php',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            dataType: 'json',
            success: function(data) {
                $('#minimal-result').html('<h4>✅ Success:</h4><pre>' + JSON.stringify(data, null, 2) + '</pre>');
            },
            error: function(xhr, status, error) {
                $('#minimal-result').html('<h4>❌ AJAX Error:</h4>' +
                    '<p>Status: ' + status + '</p>' +
                    '<p>Error: ' + error + '</p>' +
                    '<p>Response Text:</p>' +
                    '<pre>' + xhr.responseText + '</pre>' +
                    '<p>Response Length: ' + xhr.responseText.length + '</p>'
                );

                // Try to identify common issues
                const responseText = xhr.responseText;
                if(responseText.includes('Fatal error') || responseText.includes('Parse error')) {
                    $('#minimal-result').append('<p><strong>Issue:</strong> PHP Fatal/Parse Error detected</p>');
                } else if(responseText.includes('<!DOCTYPE') || responseText.includes('<html')) {
                    $('#minimal-result').append('<p><strong>Issue:</strong> HTML page returned instead of JSON</p>');
                } else if(responseText.trim() === '') {
                    $('#minimal-result').append('<p><strong>Issue:</strong> Empty response from server</p>');
                }
            }
        });
    }
    </script>
</body>
</html>
