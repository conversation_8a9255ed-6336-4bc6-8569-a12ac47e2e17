<!DOCTYPE html>
<html>
<head>
    <title>Test Branch Login System</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .test-container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin: 30px 0; padding: 20px; border: 1px solid #e9ecef; border-radius: 8px; }
        .success { background: #d4edda; color: #155724; border-color: #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border-color: #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border-color: #bee5eb; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { padding: 8px; border: 1px solid #ccc; text-align: left; }
        th { background: #f0f0f0; }
        .btn { padding: 10px 15px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; text-decoration: none; display: inline-block; }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #218838; }
    </style>
</head>
<body>
    <div class="test-container">
        <h3>🔐 Branch Login System Test</h3>
        
        <?php
        session_start();
        include('proc/config.php');
        
        // Test 1: Check branches table
        echo "<div class='test-section'>";
        echo "<h4>1. Branches Database Check</h4>";
        
        $check_branches_table = mysql_query("SHOW TABLES LIKE 'tbl_branches'");
        if($check_branches_table && mysql_num_rows($check_branches_table) > 0) {
            echo "<p class='success'>✅ tbl_branches table exists</p>";
            
            $branches_sql = "SELECT * FROM tbl_branches ORDER BY branch_name";
            $branches_result = mysql_query($branches_sql);
            if($branches_result && mysql_num_rows($branches_result) > 0) {
                echo "<p>Available branches: " . mysql_num_rows($branches_result) . "</p>";
                echo "<table>";
                echo "<tr><th>Branch ID</th><th>Branch Name</th><th>Branch Code</th><th>Test URL</th></tr>";
                
                while($branch = mysql_fetch_assoc($branches_result)) {
                    $branch_code = isset($branch['branch_code']) ? $branch['branch_code'] : 'main';
                    $test_url = "dashboard_enhanced.php?branch=" . strtolower($branch_code);
                    echo "<tr>";
                    echo "<td>" . $branch['branch_id'] . "</td>";
                    echo "<td>" . $branch['branch_name'] . "</td>";
                    echo "<td>" . $branch_code . "</td>";
                    echo "<td><a href='$test_url' class='btn btn-success'>Test URL</a></td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<p class='error'>❌ No branches found in table</p>";
            }
        } else {
            echo "<p class='error'>❌ tbl_branches table does not exist</p>";
            echo "<p class='info'>The system will use default 'Main Branch' for login.</p>";
        }
        echo "</div>";
        
        // Test 2: Session information
        echo "<div class='test-section'>";
        echo "<h4>2. Current Session Information</h4>";
        
        if(isset($_SESSION['SESS_USER_ID'])) {
            echo "<p class='success'>✅ User is logged in</p>";
            echo "<p><strong>User:</strong> " . ($_SESSION['SESS_USER_FULL_NAME'] ?? 'N/A') . "</p>";
            echo "<p><strong>Username:</strong> " . ($_SESSION['SESS_USER_NAME'] ?? 'N/A') . "</p>";
            echo "<p><strong>Access Level:</strong> " . ($_SESSION['ACCESSLEVEL'] ?? 'N/A') . "</p>";
            echo "<p><strong>Current Branch ID:</strong> " . ($_SESSION['CURRENT_BRANCH_ID'] ?? 'Not set') . "</p>";
            echo "<p><strong>Current Branch Name:</strong> " . ($_SESSION['CURRENT_BRANCH_NAME'] ?? 'Not set') . "</p>";
            echo "<p><strong>Current Branch Code:</strong> " . ($_SESSION['CURRENT_BRANCH_CODE'] ?? 'Not set') . "</p>";
        } else {
            echo "<p class='info'>ℹ️ No user logged in</p>";
        }
        echo "</div>";
        
        // Test 3: Login form test
        echo "<div class='test-section'>";
        echo "<h4>3. Login Form Test</h4>";
        echo "<p>Test the branch selection in the login form:</p>";
        echo "<a href='index.php' class='btn'>Go to Login Page</a>";
        echo "<p class='info'>ℹ️ The login page should now have a branch selection dropdown.</p>";
        echo "</div>";
        
        // Test 4: URL parameter test
        echo "<div class='test-section'>";
        echo "<h4>4. URL Parameter Test</h4>";
        
        if(isset($_GET['branch'])) {
            $branch_param = $_GET['branch'];
            echo "<p class='success'>✅ Branch parameter received: <strong>$branch_param</strong></p>";
            
            // Test branch code lookup
            $branch_sql = "SELECT * FROM tbl_branches WHERE LOWER(branch_code) = LOWER('$branch_param') LIMIT 1";
            $branch_result = mysql_query($branch_sql);
            if($branch_result && mysql_num_rows($branch_result) > 0) {
                $branch_data = mysql_fetch_assoc($branch_result);
                echo "<p class='success'>✅ Branch found in database:</p>";
                echo "<ul>";
                echo "<li><strong>ID:</strong> " . $branch_data['branch_id'] . "</li>";
                echo "<li><strong>Name:</strong> " . $branch_data['branch_name'] . "</li>";
                echo "<li><strong>Code:</strong> " . $branch_data['branch_code'] . "</li>";
                echo "</ul>";
            } else {
                echo "<p class='error'>❌ Branch code '$branch_param' not found in database</p>";
            }
        } else {
            echo "<p class='info'>ℹ️ No branch parameter in URL</p>";
            echo "<p>Test URLs:</p>";
            echo "<ul>";
            echo "<li><a href='?branch=main'>Test with branch=main</a></li>";
            echo "<li><a href='?branch=sec'>Test with branch=sec</a></li>";
            echo "<li><a href='?branch=third'>Test with branch=third</a></li>";
            echo "</ul>";
        }
        echo "</div>";
        
        // Test 5: Create sample branches
        echo "<div class='test-section'>";
        echo "<h4>5. Sample Branches Setup</h4>";
        
        if(isset($_POST['create_branches'])) {
            $create_sql = "CREATE TABLE IF NOT EXISTS tbl_branches (
                branch_id INT PRIMARY KEY,
                branch_name VARCHAR(255) NOT NULL,
                branch_code VARCHAR(50) NOT NULL,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )";
            
            if(mysql_query($create_sql)) {
                $insert_sql = "INSERT INTO tbl_branches (branch_id, branch_name, branch_code) VALUES 
                               (1, 'Main Branch', 'MAIN'),
                               (2, 'Secondary Branch', 'SEC'),
                               (3, 'Third Branch', 'THIRD')
                               ON DUPLICATE KEY UPDATE branch_name=VALUES(branch_name)";
                
                if(mysql_query($insert_sql)) {
                    echo "<p class='success'>✅ Sample branches created successfully!</p>";
                } else {
                    echo "<p class='error'>❌ Error creating sample branches: " . mysql_error() . "</p>";
                }
            } else {
                echo "<p class='error'>❌ Error creating branches table: " . mysql_error() . "</p>";
            }
        }
        ?>
        
        <form method="POST">
            <button type="submit" name="create_branches" class="btn">Create Sample Branches</button>
            <small style="display: block; margin-top: 5px;">This will create the branches table and add sample data.</small>
        </form>
        
        <?php echo "</div>"; ?>
        
        <!-- Test 6: Login flow simulation -->
        <div class="test-section">
            <h4>6. Complete Login Flow Test</h4>
            <p><strong>Expected Flow:</strong></p>
            <ol>
                <li>User goes to <code>index.php</code></li>
                <li>User selects a branch from dropdown</li>
                <li>User enters username and password</li>
                <li>System validates credentials and branch</li>
                <li>System sets branch in session</li>
                <li>System redirects to <code>dashboard_enhanced.php?branch=BRANCH_CODE</code></li>
                <li>Dashboard reads branch parameter and updates session</li>
                <li>User sees branch-specific data</li>
            </ol>
            
            <p><strong>Test Steps:</strong></p>
            <ol>
                <li><a href="logout.php" class="btn">Logout (if logged in)</a></li>
                <li><a href="index.php" class="btn">Go to Login Page</a></li>
                <li>Select a branch and login</li>
                <li>Verify you're redirected to the correct branch URL</li>
            </ol>
        </div>
        
        <!-- Test 7: Navigation test -->
        <div class="test-section">
            <h4>7. Navigation Test</h4>
            <p>Test that the navigation shows the current branch (not a dropdown):</p>
            <a href="pg_accounts.php" class="btn">Go to Accounts Page</a>
            <p class="info">ℹ️ The navigation should show the current branch name, not a dropdown selector.</p>
        </div>
    </div>
</body>
</html>
