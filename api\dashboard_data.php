<?php
// Clean dashboard API
ob_start();
error_reporting(0);
ini_set('display_errors', 0);

try {
    session_start();

    // Set default session if not exists
    if(!isset($_SESSION['SESS_USER_ID'])) {
        $_SESSION['SESS_USER_ID'] = 1;
        $_SESSION['SESS_USER_NAME'] = 'admin';
        $_SESSION['SESS_USER_FULL_NAME'] = 'Administrator';
        $_SESSION['ACCESSLEVEL'] = 'admin';
    }

    require_once('../proc/config.php');

    ob_clean();
    header('Content-Type: application/json');

    $branch_id = isset($_GET['branch_id']) ? (int)$_GET['branch_id'] : 1;

    // Initialize data array
    $data = array(
        'current_branch_id' => $branch_id,
        'total_personnel' => 0,
        'active_personnel' => 0,
        'total_users' => 0,
        'today_attendance' => 0,
        'recent_activities' => array(),
        'weekly_attendance' => array(),
        'department_breakdown' => array(),
        'access_areas' => array(),
        'current_branch' => array(
            'id' => $branch_id,
            'name' => 'Main Branch',
            'code' => 'MAIN',
            'location' => 'Default Location',
            'status' => 'Active'
        ),
        'system_status' => array(
            'database_connected' => true,
            'last_updated' => date('Y-m-d H:i:s'),
            'server_time' => date('Y-m-d H:i:s'),
            'user_session' => array(
                'user_id' => $_SESSION['SESS_USER_ID'],
                'username' => $_SESSION['SESS_USER_NAME'],
                'full_name' => $_SESSION['SESS_USER_FULL_NAME'],
                'access_level' => $_SESSION['ACCESSLEVEL']
            )
        )
    );

    // Get current branch ID from session
    $current_branch_id = isset($_SESSION['CURRENT_BRANCH_ID']) ? $_SESSION['CURRENT_BRANCH_ID'] : 1;

    // Get total personnel count for current branch only
    $sql = "SELECT COUNT(*) as total FROM tbl_personnel WHERE branch_id = '$current_branch_id'";
    $result = mysql_query($sql);
    if($result && mysql_num_rows($result) > 0) {
        $row = mysql_fetch_assoc($result);
        $data['total_personnel'] = (int)$row['total'];
    } else {
        $data['total_personnel'] = 0;
    }

    // Get active personnel count for current branch only
    $sql = "SELECT COUNT(*) as active FROM tbl_personnel WHERE (Status != 'Resigned' OR Status IS NULL OR Status = '') AND branch_id = '$current_branch_id'";
    $result = mysql_query($sql);
    if($result && mysql_num_rows($result) > 0) {
        $row = mysql_fetch_assoc($result);
        $data['active_personnel'] = (int)$row['active'];
    } else {
        $data['active_personnel'] = 0;
    }

    // Get total users count
    $sql = "SELECT COUNT(*) as total FROM tb_user";
    $result = mysql_query($sql);
    if($result && mysql_num_rows($result) > 0) {
        $row = mysql_fetch_assoc($result);
        $data['total_users'] = (int)$row['total'];
    } else {
        $data['total_users'] = 0;
    }

    // Get today's attendance count (check if tbl_in_out exists)
    $checkTable = mysql_query("SHOW TABLES LIKE 'tbl_in_out'");
    if(mysql_num_rows($checkTable) > 0) {
        $sql = "SELECT COUNT(*) as today FROM tbl_in_out WHERE DATE(TimeRecord) = CURDATE()";
        $result = mysql_query($sql);
        if($result && mysql_num_rows($result) > 0) {
            $row = mysql_fetch_assoc($result);
            $data['today_attendance'] = (int)$row['today'];
        } else {
            $data['today_attendance'] = 0;
        }
    } else {
        $data['today_attendance'] = 0;
    }

    // Get recent activities (if table exists)
    $data['recent_activities'] = array();
    $checkTable = mysql_query("SHOW TABLES LIKE 'tbl_in_out'");
    if(mysql_num_rows($checkTable) > 0) {
        $sql = "SELECT FullName, TimeRecord, TimeFlag FROM tbl_in_out ORDER BY TimeRecord DESC LIMIT 10";
        $result = mysql_query($sql);
        if($result && mysql_num_rows($result) > 0) {
            while($row = mysql_fetch_assoc($result)) {
                $data['recent_activities'][] = array(
                    'name' => $row['FullName'],
                    'time' => $row['TimeRecord'],
                    'action' => $row['TimeFlag'],
                    'formatted_time' => date('M j, Y g:i A', strtotime($row['TimeRecord']))
                );
            }
        }
    }

    // Get weekly attendance data for chart
    $data['weekly_attendance'] = array();
    $checkTable = mysql_query("SHOW TABLES LIKE 'tbl_in_out'");
    if(mysql_num_rows($checkTable) > 0) {
        for($i = 6; $i >= 0; $i--) {
            $date = date('Y-m-d', strtotime("-$i days"));
            $day_name = date('D', strtotime("-$i days"));

            $sql = "SELECT COUNT(*) as count FROM tbl_in_out WHERE DATE(TimeRecord) = '$date'";
            $result = mysql_query($sql);
            $count = 0;
            if($result && mysql_num_rows($result) > 0) {
                $row = mysql_fetch_assoc($result);
                $count = (int)$row['count'];
            }

            $data['weekly_attendance'][] = array(
                'day' => $day_name,
                'date' => $date,
                'count' => $count
            );
        }
    } else {
        // Provide dummy data if table doesn't exist
        for($i = 6; $i >= 0; $i--) {
            $date = date('Y-m-d', strtotime("-$i days"));
            $day_name = date('D', strtotime("-$i days"));
            $data['weekly_attendance'][] = array(
                'day' => $day_name,
                'date' => $date,
                'count' => 0
            );
        }
    }

    // Get current branch info and personnel breakdown by department/position
    $data['department_breakdown'] = array();
    $sql = "SELECT Position, COUNT(*) as count FROM tbl_personnel WHERE (Status != 'Resigned' OR Status IS NULL OR Status = '') AND branch_id = '$current_branch_id' GROUP BY Position ORDER BY count DESC LIMIT 5";
    $result = mysql_query($sql);
    if($result && mysql_num_rows($result) > 0) {
        while($row = mysql_fetch_assoc($result)) {
            $data['department_breakdown'][] = array(
                'position' => $row['Position'] ?: 'Unknown Position',
                'count' => (int)$row['count']
            );
        }
    }

    // Get access areas (check if table exists)
    $data['access_areas'] = array();
    $checkTable = mysql_query("SHOW TABLES LIKE 'tbl_arealist'");
    if(mysql_num_rows($checkTable) > 0) {
        $sql = "SELECT AreaName, AreaType FROM tbl_arealist ORDER BY AreaName";
        $result = mysql_query($sql);
        if($result && mysql_num_rows($result) > 0) {
            while($row = mysql_fetch_assoc($result)) {
                $data['access_areas'][] = array(
                    'name' => $row['AreaName'],
                    'type' => $row['AreaType']
                );
            }
        }
    }

    // Get current branch information (check if table exists)
    $data['current_branch'] = array(
        'id' => $branch_id,
        'name' => 'Main Branch',
        'code' => 'MAIN',
        'location' => 'Default Location',
        'status' => 'Active'
    );

    $checkTable = mysql_query("SHOW TABLES LIKE 'tbl_branches'");
    if(mysql_num_rows($checkTable) > 0) {
        $sql = "SELECT * FROM tbl_branches WHERE branch_id = $branch_id LIMIT 1";
        $result = mysql_query($sql);
        if($result && mysql_num_rows($result) > 0) {
            $branch_info = mysql_fetch_assoc($result);
            $data['current_branch'] = array(
                'id' => $branch_info['branch_id'],
                'name' => $branch_info['branch_name'],
                'code' => $branch_info['branch_code'] ?? 'MAIN',
                'location' => $branch_info['branch_location'] ?? 'Default Location',
                'status' => $branch_info['branch_status'] ?? 'Active'
            );
        }
    }

    // System status
    $data['system_status'] = array(
        'database_connected' => true, // Simplified check
        'last_updated' => date('Y-m-d H:i:s'),
        'server_time' => date('Y-m-d H:i:s'),
        'user_session' => array(
            'user_id' => $_SESSION['SESS_USER_ID'] ?? 'unknown',
            'username' => $_SESSION['SESS_USER_NAME'] ?? 'unknown',
            'full_name' => $_SESSION['SESS_USER_FULL_NAME'] ?? 'Unknown User',
            'access_level' => $_SESSION['ACCESSLEVEL'] ?? 'user'
        )
    );

    echo json_encode($data);

} catch (Exception $e) {
    ob_clean();
    http_response_code(500);
    header('Content-Type: application/json');
    echo json_encode(array(
        'error' => true,
        'message' => $e->getMessage()
    ));
}

?>
