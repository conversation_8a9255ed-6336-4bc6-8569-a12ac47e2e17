<?php
// Debug the 500 error
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h3>Debugging 500 Error</h3>";

// Test 1: Check if the file exists
$api_file = 'api/dashboard_data.php';
echo "<h4>File Existence Check:</h4>";
if(file_exists($api_file)) {
    echo "<p>✅ File exists: $api_file</p>";
    echo "<p>File size: " . filesize($api_file) . " bytes</p>";
    echo "<p>File permissions: " . substr(sprintf('%o', fileperms($api_file)), -4) . "</p>";
} else {
    echo "<p>❌ File not found: $api_file</p>";
}

// Test 2: Check PHP syntax
echo "<h4>PHP Syntax Check:</h4>";
$syntax_check = shell_exec("php -l $api_file 2>&1");
if(strpos($syntax_check, 'No syntax errors') !== false) {
    echo "<p>✅ PHP syntax is valid</p>";
} else {
    echo "<p>❌ PHP syntax error:</p>";
    echo "<pre>" . htmlspecialchars($syntax_check) . "</pre>";
}

// Test 3: Direct execution test
echo "<h4>Direct Execution Test:</h4>";
echo "<p>Testing the debug version...</p>";

// Set up session for testing
session_start();
if(!isset($_SESSION['SESS_USER_ID'])) {
    $_SESSION['SESS_USER_ID'] = 1;
    $_SESSION['SESS_USER_NAME'] = 'test_user';
    $_SESSION['SESS_USER_FULL_NAME'] = 'Test User';
    $_SESSION['ACCESSLEVEL'] = 'admin';
}

echo "<iframe src='api/dashboard_data_debug.php?branch_id=2' width='100%' height='300' style='border: 1px solid #ccc;'></iframe>";

// Test 4: cURL test
echo "<h4>cURL Test:</h4>";
if(function_exists('curl_init')) {
    $url = 'http://localhost/bwp/api/dashboard_data_debug.php?branch_id=2';
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HEADER, true);
    curl_setopt($ch, CURLOPT_NOBODY, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "<p><strong>HTTP Code:</strong> $http_code</p>";
    if($error) {
        echo "<p><strong>cURL Error:</strong> $error</p>";
    }
    
    echo "<p><strong>Response:</strong></p>";
    echo "<pre>" . htmlspecialchars($response) . "</pre>";
} else {
    echo "<p>cURL not available</p>";
}

// Test 5: Check database connection
echo "<h4>Database Connection Test:</h4>";
try {
    include('proc/config.php');
    
    // Test basic query
    $result = mysql_query("SELECT 1 as test");
    if($result) {
        echo "<p>✅ Database connection successful</p>";
        
        // Test tbl_personnel
        $result = mysql_query("SELECT COUNT(*) as count FROM tbl_personnel");
        if($result) {
            $row = mysql_fetch_assoc($result);
            echo "<p>✅ tbl_personnel accessible, records: " . $row['count'] . "</p>";
        } else {
            echo "<p>❌ tbl_personnel error: " . mysql_error() . "</p>";
        }
    } else {
        echo "<p>❌ Database connection failed: " . mysql_error() . "</p>";
    }
} catch(Exception $e) {
    echo "<p>❌ Database test exception: " . $e->getMessage() . "</p>";
}

// Test 6: Check PHP error log
echo "<h4>PHP Error Log Check:</h4>";
$error_log = ini_get('error_log');
if($error_log && file_exists($error_log)) {
    echo "<p>Error log location: $error_log</p>";
    $recent_errors = shell_exec("tail -20 '$error_log' 2>&1");
    if($recent_errors) {
        echo "<p>Recent errors:</p>";
        echo "<pre>" . htmlspecialchars($recent_errors) . "</pre>";
    }
} else {
    echo "<p>Error log not found or not configured</p>";
}

// Test 7: JavaScript test
echo "<h4>JavaScript AJAX Test:</h4>";
echo "<button onclick='testOriginalAPI()'>Test Original API</button>";
echo "<button onclick='testDebugAPI()'>Test Debug API</button>";
echo "<div id='testResult'></div>";
?>

<script>
function testOriginalAPI() {
    fetch('api/dashboard_data.php?branch_id=2')
        .then(response => {
            document.getElementById('testResult').innerHTML = 
                '<h5>Original API Response:</h5>' +
                '<p>Status: ' + response.status + ' ' + response.statusText + '</p>';
            
            if(response.ok) {
                return response.json();
            } else {
                return response.text().then(text => {
                    throw new Error('HTTP ' + response.status + ': ' + text);
                });
            }
        })
        .then(data => {
            document.getElementById('testResult').innerHTML += 
                '<p style="color: green;">✅ Success!</p>' +
                '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
        })
        .catch(error => {
            document.getElementById('testResult').innerHTML += 
                '<p style="color: red;">❌ Error: ' + error.message + '</p>';
        });
}

function testDebugAPI() {
    fetch('api/dashboard_data_debug.php?branch_id=2')
        .then(response => {
            document.getElementById('testResult').innerHTML = 
                '<h5>Debug API Response:</h5>' +
                '<p>Status: ' + response.status + ' ' + response.statusText + '</p>';
            
            if(response.ok) {
                return response.json();
            } else {
                return response.text().then(text => {
                    throw new Error('HTTP ' + response.status + ': ' + text);
                });
            }
        })
        .then(data => {
            document.getElementById('testResult').innerHTML += 
                '<p style="color: green;">✅ Success!</p>' +
                '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
        })
        .catch(error => {
            document.getElementById('testResult').innerHTML += 
                '<p style="color: red;">❌ Error: ' + error.message + '</p>';
        });
}
</script>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; max-height: 300px; }
button { padding: 10px 15px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
button:hover { background: #0056b3; }
#testResult { margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 4px; }
iframe { margin: 10px 0; }
</style>
