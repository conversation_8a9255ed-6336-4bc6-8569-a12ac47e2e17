<?xml version="1.0" encoding="UTF-8"?>
<component name="dataSourceStorage">
  <data-source name="MySQL - db_eas@localhost" uuid="afc53e9e-825e-4104-abbd-aaa97f709688">
    <database-info product="MySQL" version="5.5.39" jdbc-version="4.0" driver-name="MySQL Connector Java" driver-version="mysql-connector-java-5.1.35 ( Revision: 5fb9c5849535c13917c2cf9baaece6ef9693ef27 )">
      <extra-name-characters>#@</extra-name-characters>
      <identifier-quote-string>`</identifier-quote-string>
    </database-info>
    <case-sensitivity plain-identifiers="lower" quoted-identifiers="lower"/>
    <database-model serializer="dbm" rdbms="MYSQL" format-version="2.4">
      <schema id="1" name="db_eas"/>
      <table id="2" parent="1" name="tb_user"/>
      <table id="3" parent="1" name="tbl_arealist"/>
      <table id="4" parent="1" name="tbl_datalogs"/>
      <table id="5" parent="1" name="tbl_personnel"/>
      <table id="6" parent="1" name="tbl_visitor"/>
      <table id="7" parent="1" name="tbl_visitortype"/>
      <column id="8" parent="2" name="IDno">
        <mandatory>1</mandatory>
        <data-type>int(3)</data-type>
      </column>
      <column id="9" parent="2" name="user_name">
        <mandatory>1</mandatory>
        <data-type>varchar(50)</data-type>
      </column>
      <column id="10" parent="2" name="full_name">
        <mandatory>1</mandatory>
        <data-type>varchar(50)</data-type>
      </column>
      <column id="11" parent="2" name="pass_word">
        <mandatory>1</mandatory>
        <data-type>varchar(50)</data-type>
      </column>
      <column id="12" parent="2" name="email_add">
        <mandatory>1</mandatory>
        <data-type>varchar(50)</data-type>
      </column>
      <column id="13" parent="2" name="userlevel">
        <mandatory>1</mandatory>
        <data-type>varchar(50)</data-type>
      </column>
      <key id="14" parent="2" name="PRIMARY">
        <columns>IDno</columns>
        <primary>1</primary>
      </key>
      <column id="15" parent="3" name="ID">
        <mandatory>1</mandatory>
        <data-type>int(6) unsigned zerofill</data-type>
      </column>
      <column id="16" parent="3" name="AreaName">
        <mandatory>1</mandatory>
        <data-type>varchar(50)</data-type>
      </column>
      <column id="17" parent="3" name="AreaType">
        <mandatory>1</mandatory>
        <data-type>varchar(50)</data-type>
      </column>
      <key id="18" parent="3" name="PRIMARY">
        <columns>ID</columns>
        <primary>1</primary>
      </key>
      <column id="19" parent="4" name="ID">
        <mandatory>1</mandatory>
        <data-type>int(11)</data-type>
      </column>
      <column id="20" parent="4" name="UserID">
        <mandatory>1</mandatory>
        <data-type>varchar(50)</data-type>
      </column>
      <column id="21" parent="4" name="Company">
        <mandatory>1</mandatory>
        <data-type>varchar(50)</data-type>
      </column>
      <column id="22" parent="4" name="TimeIN">
        <mandatory>1</mandatory>
        <data-type>datetime</data-type>
      </column>
      <column id="23" parent="4" name="TimeOut">
        <mandatory>1</mandatory>
        <data-type>datetime</data-type>
      </column>
      <column id="24" parent="4" name="Remarks">
        <mandatory>1</mandatory>
        <data-type>text</data-type>
      </column>
      <column id="25" parent="4" name="FullName">
        <mandatory>1</mandatory>
        <data-type>varchar(50)</data-type>
      </column>
      <column id="26" parent="4" name="AccessArea">
        <mandatory>1</mandatory>
        <data-type>varchar(50)</data-type>
      </column>
      <column id="27" parent="4" name="Status">
        <mandatory>1</mandatory>
        <data-type>varchar(50)</data-type>
      </column>
      <column id="28" parent="4" name="ContactPerson">
        <mandatory>1</mandatory>
        <data-type>varchar(50)</data-type>
      </column>
      <column id="29" parent="4" name="AccessID">
        <mandatory>1</mandatory>
        <data-type>varchar(50)</data-type>
      </column>
      <index id="30" parent="4" name="UserID">
        <columns>UserID</columns>
        <desc-columns></desc-columns>
      </index>
      <key id="31" parent="4" name="PRIMARY">
        <columns>ID</columns>
        <primary>1</primary>
      </key>
      <column id="32" parent="5" name="EmployeeID">
        <mandatory>1</mandatory>
        <data-type>varchar(12)</data-type>
      </column>
      <column id="33" parent="5" name="FullName">
        <mandatory>1</mandatory>
        <data-type>varchar(50)</data-type>
      </column>
      <column id="34" parent="5" name="DateHired">
        <mandatory>1</mandatory>
        <data-type>date</data-type>
      </column>
      <column id="35" parent="5" name="Position">
        <mandatory>1</mandatory>
        <data-type>varchar(50)</data-type>
      </column>
      <column id="36" parent="5" name="AgencyCompany">
        <mandatory>1</mandatory>
        <data-type>varchar(50)</data-type>
      </column>
      <column id="37" parent="5" name="ContactNo">
        <mandatory>1</mandatory>
        <data-type>varchar(50)</data-type>
      </column>
      <column id="38" parent="5" name="AccessArea">
        <mandatory>1</mandatory>
        <data-type>text</data-type>
      </column>
      <column id="39" parent="5" name="Image">
        <mandatory>1</mandatory>
        <data-type>varchar(50)</data-type>
      </column>
      <column id="40" parent="5" name="Status">
        <mandatory>1</mandatory>
        <data-type>varchar(50)</data-type>
      </column>
      <column id="41" parent="5" name="Remarks">
        <mandatory>1</mandatory>
        <data-type>text</data-type>
      </column>
      <column id="42" parent="5" name="TimeIN">
        <data-type>time</data-type>
      </column>
      <column id="43" parent="5" name="TimeOut">
        <data-type>time</data-type>
      </column>
      <column id="44" parent="5" name="schedule_dates">
        <data-type>varchar(255)</data-type>
      </column>
      <column id="45" parent="5" name="Password">
        <mandatory>1</mandatory>
        <data-type>varchar(50)</data-type>
      </column>
      <index id="46" parent="5" name="EmployeeID">
        <columns>EmployeeID</columns>
        <desc-columns></desc-columns>
      </index>
      <key id="47" parent="5" name="PRIMARY">
        <columns>EmployeeID</columns>
        <primary>1</primary>
      </key>
      <column id="48" parent="6" name="VisitorID">
        <mandatory>1</mandatory>
        <data-type>varchar(8)</data-type>
      </column>
      <column id="49" parent="6" name="FullName">
        <mandatory>1</mandatory>
        <data-type>varchar(50)</data-type>
      </column>
      <column id="50" parent="6" name="Address">
        <mandatory>1</mandatory>
        <data-type>varchar(50)</data-type>
      </column>
      <column id="51" parent="6" name="TimeIN">
        <data-type>datetime</data-type>
      </column>
      <column id="52" parent="6" name="TimeOut">
        <data-type>datetime</data-type>
      </column>
      <column id="53" parent="6" name="ContactNo">
        <mandatory>1</mandatory>
        <data-type>varchar(50)</data-type>
      </column>
      <column id="54" parent="6" name="ContactPerson">
        <mandatory>1</mandatory>
        <data-type>varchar(50)</data-type>
      </column>
      <column id="55" parent="6" name="AccessArea">
        <mandatory>1</mandatory>
        <data-type>varchar(50)</data-type>
      </column>
      <column id="56" parent="6" name="ValidID">
        <mandatory>1</mandatory>
        <data-type>varchar(50)</data-type>
      </column>
      <column id="57" parent="6" name="Purpose">
        <mandatory>1</mandatory>
        <data-type>text</data-type>
      </column>
      <column id="58" parent="6" name="Image">
        <mandatory>1</mandatory>
        <data-type>varchar(100)</data-type>
      </column>
      <column id="59" parent="6" name="Remarks">
        <mandatory>1</mandatory>
        <data-type>text</data-type>
      </column>
      <column id="60" parent="6" name="AccessID">
        <mandatory>1</mandatory>
        <data-type>varchar(50)</data-type>
      </column>
      <column id="61" parent="6" name="VisitorType">
        <mandatory>1</mandatory>
        <data-type>varchar(50)</data-type>
      </column>
      <column id="62" parent="6" name="DateVisit">
        <mandatory>1</mandatory>
        <data-type>datetime</data-type>
      </column>
      <index id="63" parent="6" name="VisitorID">
        <columns>VisitorID</columns>
        <desc-columns></desc-columns>
        <unique>1</unique>
      </index>
      <key id="64" parent="6" name="PRIMARY">
        <columns>VisitorID</columns>
        <primary>1</primary>
      </key>
      <key id="65" parent="6" name="VisitorID">
        <columns>VisitorID</columns>
        <underlying-index>VisitorID</underlying-index>
      </key>
      <column id="66" parent="7" name="ID">
        <mandatory>1</mandatory>
        <data-type>int(11)</data-type>
      </column>
      <column id="67" parent="7" name="VisitorType">
        <mandatory>1</mandatory>
        <data-type>varchar(50)</data-type>
      </column>
      <column id="68" parent="7" name="Description">
        <mandatory>1</mandatory>
        <data-type>varchar(50)</data-type>
      </column>
      <column id="69" parent="7" name="Remarks">
        <mandatory>1</mandatory>
        <data-type>varchar(50)</data-type>
      </column>
      <key id="70" parent="7" name="PRIMARY">
        <columns>ID</columns>
        <primary>1</primary>
      </key>
    </database-model>
  </data-source>
</component>