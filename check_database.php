<?php
// Check database structure and data
include('proc/config.php');

echo "<h3>Database Structure Check</h3>";

// Check if table exists and show structure
$sql = "DESCRIBE tbl_personnel";
$result = mysql_query($sql);

if($result) {
    echo "<h4>Table Structure:</h4>";
    echo "<table border='1'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    while($row = mysql_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "Error describing table: " . mysql_error();
}

// Check data count
$sql = "SELECT COUNT(*) as count FROM tbl_personnel";
$result = mysql_query($sql);

if($result) {
    $row = mysql_fetch_assoc($result);
    echo "<h4>Total Records: " . $row['count'] . "</h4>";
} else {
    echo "Error counting records: " . mysql_error();
}

// Show first few records
$sql = "SELECT EntryID, EmployeeID, FullName, AccessID FROM tbl_personnel LIMIT 3";
$result = mysql_query($sql);

if($result) {
    echo "<h4>Sample Records:</h4>";
    echo "<table border='1'>";
    echo "<tr><th>EntryID</th><th>EmployeeID</th><th>FullName</th><th>AccessID</th></tr>";
    
    while($row = mysql_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['EntryID'] . "</td>";
        echo "<td>" . $row['EmployeeID'] . "</td>";
        echo "<td>" . $row['FullName'] . "</td>";
        echo "<td>" . $row['AccessID'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "Error fetching records: " . mysql_error();
}
?>

<style>
table { border-collapse: collapse; margin: 10px 0; }
th, td { padding: 8px; border: 1px solid #ccc; }
th { background: #f0f0f0; }
</style>
