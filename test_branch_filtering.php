<!DOCTYPE html>
<html>
<head>
    <title>Test Branch Data Filtering</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .test-container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin: 30px 0; padding: 20px; border: 1px solid #e9ecef; border-radius: 8px; }
        .success { background: #d4edda; color: #155724; border-color: #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border-color: #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border-color: #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border-color: #ffeaa7; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { padding: 12px; border: 1px solid #ccc; text-align: left; }
        th { background: #f0f0f0; font-weight: bold; }
        .btn { padding: 10px 15px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; text-decoration: none; display: inline-block; }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #218838; }
        .branch-badge { background: #fff3e0; color: #f57c00; padding: 4px 8px; border-radius: 4px; font-size: 12px; }
    </style>
</head>
<body>
    <div class="test-container">
        <h3>🏢 Branch Data Filtering System Test</h3>
        
        <?php
        session_start();
        include('proc/config.php');
        
        // Test 1: Current session branch
        echo "<div class='test-section'>";
        echo "<h4>1. Current Session Branch</h4>";
        
        $current_branch_id = isset($_SESSION['CURRENT_BRANCH_ID']) ? $_SESSION['CURRENT_BRANCH_ID'] : 1;
        $current_branch_name = isset($_SESSION['CURRENT_BRANCH_NAME']) ? $_SESSION['CURRENT_BRANCH_NAME'] : 'Not set';
        
        echo "<p><strong>Current Branch ID:</strong> $current_branch_id</p>";
        echo "<p><strong>Current Branch Name:</strong> $current_branch_name</p>";
        
        if(isset($_SESSION['SESS_USER_ID'])) {
            echo "<p class='success'>✅ User is logged in</p>";
            echo "<p><strong>User:</strong> " . $_SESSION['SESS_USER_FULL_NAME'] . "</p>";
        } else {
            echo "<p class='warning'>⚠️ No user logged in - <a href='index.php'>Login first</a></p>";
        }
        echo "</div>";
        
        // Test 2: All employees by branch
        echo "<div class='test-section'>";
        echo "<h4>2. Employee Distribution by Branch</h4>";
        
        $branch_stats_sql = "SELECT b.branch_id, b.branch_name, COUNT(p.EntryID) as employee_count 
                            FROM tbl_branches b 
                            LEFT JOIN tbl_personnel p ON b.branch_id = p.branch_id 
                            GROUP BY b.branch_id, b.branch_name 
                            ORDER BY b.branch_id";
        $branch_stats_result = mysql_query($branch_stats_sql);
        
        if($branch_stats_result && mysql_num_rows($branch_stats_result) > 0) {
            echo "<table>";
            echo "<tr><th>Branch ID</th><th>Branch Name</th><th>Employee Count</th><th>Current Branch</th></tr>";
            
            while($row = mysql_fetch_assoc($branch_stats_result)) {
                $is_current = ($row['branch_id'] == $current_branch_id);
                $current_indicator = $is_current ? '<span class="branch-badge">CURRENT</span>' : '';
                $row_class = $is_current ? 'style="background: #e8f5e8;"' : '';
                
                echo "<tr $row_class>";
                echo "<td>" . $row['branch_id'] . "</td>";
                echo "<td>" . $row['branch_name'] . "</td>";
                echo "<td>" . $row['employee_count'] . "</td>";
                echo "<td>$current_indicator</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='error'>❌ No branch data found</p>";
        }
        echo "</div>";
        
        // Test 3: Current branch employees only
        echo "<div class='test-section'>";
        echo "<h4>3. Current Branch Employees (What User Should See)</h4>";
        
        $current_employees_sql = "SELECT p.*, b.branch_name 
                                 FROM tbl_personnel p 
                                 LEFT JOIN tbl_branches b ON p.branch_id = b.branch_id 
                                 WHERE p.branch_id = '$current_branch_id' 
                                 ORDER BY p.EntryID DESC";
        $current_employees_result = mysql_query($current_employees_sql);
        
        if($current_employees_result && mysql_num_rows($current_employees_result) > 0) {
            echo "<p class='success'>✅ Found " . mysql_num_rows($current_employees_result) . " employees in current branch</p>";
            echo "<table>";
            echo "<tr><th>Employee ID</th><th>Biometric ID</th><th>Full Name</th><th>Position</th><th>Branch</th></tr>";
            
            while($row = mysql_fetch_assoc($current_employees_result)) {
                echo "<tr>";
                echo "<td>" . $row['EmployeeID'] . "</td>";
                echo "<td>" . $row['AccessID'] . "</td>";
                echo "<td>" . $row['FullName'] . "</td>";
                echo "<td>" . ($row['Position'] ?: 'N/A') . "</td>";
                echo "<td>" . ($row['branch_name'] ?: 'Unknown') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='warning'>⚠️ No employees found in current branch (ID: $current_branch_id)</p>";
        }
        echo "</div>";
        
        // Test 4: Dashboard API test
        echo "<div class='test-section'>";
        echo "<h4>4. Dashboard API Data Test</h4>";
        
        echo "<p>Testing dashboard API with current branch filtering:</p>";
        echo "<iframe src='api/dashboard_data.php' width='100%' height='300' style='border: 1px solid #ccc; border-radius: 4px;'></iframe>";
        echo "<p><a href='api/dashboard_data.php' target='_blank' class='btn'>View API Response</a></p>";
        echo "</div>";
        
        // Test 5: Switch branch simulation
        echo "<div class='test-section'>";
        echo "<h4>5. Branch Switch Simulation</h4>";
        
        if(isset($_POST['simulate_branch'])) {
            $new_branch_id = (int)$_POST['new_branch_id'];
            $_SESSION['CURRENT_BRANCH_ID'] = $new_branch_id;
            
            // Get branch name
            $branch_name_sql = "SELECT branch_name FROM tbl_branches WHERE branch_id = '$new_branch_id'";
            $branch_name_result = mysql_query($branch_name_sql);
            if($branch_name_result && mysql_num_rows($branch_name_result) > 0) {
                $branch_row = mysql_fetch_assoc($branch_name_result);
                $_SESSION['CURRENT_BRANCH_NAME'] = $branch_row['branch_name'];
            }
            
            echo "<p class='success'>✅ Simulated branch switch to ID: $new_branch_id</p>";
            echo "<script>setTimeout(() => window.location.reload(), 1000);</script>";
        }
        
        echo "<form method='POST' style='background: #f8f9fa; padding: 15px; border-radius: 4px;'>";
        echo "<h5>Simulate Branch Switch (for testing):</h5>";
        echo "<select name='new_branch_id' required>";
        
        $all_branches_sql = "SELECT * FROM tbl_branches ORDER BY branch_name";
        $all_branches_result = mysql_query($all_branches_sql);
        if($all_branches_result) {
            while($branch = mysql_fetch_assoc($all_branches_result)) {
                $selected = ($branch['branch_id'] == $current_branch_id) ? 'selected' : '';
                echo "<option value='" . $branch['branch_id'] . "' $selected>" . $branch['branch_name'] . " (ID: " . $branch['branch_id'] . ")</option>";
            }
        }
        
        echo "</select>";
        echo "<button type='submit' name='simulate_branch' class='btn'>Switch Branch</button>";
        echo "<small style='display: block; margin-top: 5px;'>This simulates what happens when user logs in with different branch</small>";
        echo "</form>";
        echo "</div>";
        
        // Test 6: Add sample employees to different branches
        echo "<div class='test-section'>";
        echo "<h4>6. Add Sample Employees (for testing)</h4>";
        
        if(isset($_POST['add_sample_employees'])) {
            $sample_employees = array(
                array('name' => 'John Doe', 'branch_id' => 1, 'position' => 'Manager'),
                array('name' => 'Jane Smith', 'branch_id' => 1, 'position' => 'Assistant'),
                array('name' => 'Bob Johnson', 'branch_id' => 2, 'position' => 'Supervisor'),
                array('name' => 'Alice Brown', 'branch_id' => 2, 'position' => 'Clerk'),
                array('name' => 'Charlie Wilson', 'branch_id' => 3, 'position' => 'Guard')
            );
            
            foreach($sample_employees as $emp) {
                $insert_sql = "INSERT INTO tbl_personnel (EmployeeID, AccessID, FullName, Position, branch_id, DateHired) 
                              VALUES ('EMP" . rand(1000,9999) . "', 'B" . str_pad(rand(1,999), 3, '0', STR_PAD_LEFT) . "', '" . $emp['name'] . "', '" . $emp['position'] . "', '" . $emp['branch_id'] . "', NOW())";
                mysql_query($insert_sql);
            }
            
            echo "<p class='success'>✅ Sample employees added to different branches!</p>";
            echo "<script>setTimeout(() => window.location.reload(), 1000);</script>";
        }
        
        echo "<form method='POST'>";
        echo "<button type='submit' name='add_sample_employees' class='btn btn-success'>Add Sample Employees</button>";
        echo "<small style='display: block; margin-top: 5px;'>This will add test employees to different branches for testing</small>";
        echo "</form>";
        echo "</div>";
        
        // Test 7: Navigation test
        echo "<div class='test-section info'>";
        echo "<h4>7. Test Pages</h4>";
        echo "<p>Test the branch filtering on actual pages:</p>";
        echo "<a href='dashboard_enhanced.php' class='btn'>Dashboard (should show current branch data)</a>";
        echo "<a href='pg_accounts.php' class='btn'>Employees (should show current branch employees only)</a>";
        echo "<p class='info'>ℹ️ Navigation should show current branch name (not dropdown)</p>";
        echo "</div>";
        ?>
        
        <!-- Instructions -->
        <div class="test-section info">
            <h4>📋 Branch Filtering System Overview</h4>
            <ul>
                <li><strong>Login Branch Selection:</strong> Users select branch during login</li>
                <li><strong>Session Storage:</strong> Branch ID stored in session throughout user session</li>
                <li><strong>Data Filtering:</strong> All queries filtered by current branch ID</li>
                <li><strong>No Branch Switching:</strong> Users cannot switch branches after login</li>
                <li><strong>Branch-Specific Data:</strong> Dashboard, employees, attendance all filtered</li>
                <li><strong>New Employees:</strong> Automatically assigned to current branch</li>
            </ul>
            
            <h5>Expected Behavior:</h5>
            <ul>
                <li>Dashboard shows only current branch statistics</li>
                <li>Employee list shows only current branch employees</li>
                <li>New employees automatically assigned to current branch</li>
                <li>Navigation shows current branch name (read-only)</li>
                <li>No branch switching dropdown in interface</li>
            </ul>
        </div>
    </div>
</body>
</html>
