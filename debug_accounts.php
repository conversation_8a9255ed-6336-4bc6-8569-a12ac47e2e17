<?php
// Debug version to check the accounts data
include('proc/config.php');

echo "<h3>Debug: Accounts Data</h3>";

// Check what getAccounts.php returns
echo "<h4>Raw Data from getAccounts.php:</h4>";
include('function/getAccounts.php');

echo "<hr>";

// Check individual employee data
echo "<h4>Individual Employee Test:</h4>";
$sql = "SELECT * FROM tbl_personnel LIMIT 1";
$result = mysql_query($sql);

if($result && mysql_num_rows($result) > 0) {
    $employee = mysql_fetch_assoc($result);
    echo "<pre>";
    print_r($employee);
    echo "</pre>";
    
    echo "<h4>Test getEmployeeData.php with ID: " . $employee['EntryID'] . "</h4>";
    
    // Test the function
    $_POST['employee_id'] = $employee['EntryID'];
    ob_start();
    include('function/getEmployeeData.php');
    $output = ob_get_clean();
    
    echo "<pre>$output</pre>";
} else {
    echo "No employees found or database error: " . mysql_error();
}
?>
