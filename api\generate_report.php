<?php
session_start();
require_once('../proc/config.php');

// Check authentication
if(!isset($_SESSION['SESS_USER_ID'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

// Get parameters
$report_type = isset($_REQUEST['report_type']) ? $_REQUEST['report_type'] : '';
$branch_id = isset($_REQUEST['branch_id']) ? $_REQUEST['branch_id'] : 'all';
$date_from = isset($_REQUEST['date_from']) ? $_REQUEST['date_from'] : date('Y-m-01');
$date_to = isset($_REQUEST['date_to']) ? $_REQUEST['date_to'] : date('Y-m-d');
$format = isset($_REQUEST['format']) ? $_REQUEST['format'] : 'html';

// Branch filter condition
$branch_condition = '';
if($branch_id !== 'all') {
    $branch_condition = " AND branch_id = " . (int)$branch_id;
}

// Get branch name for title
$branch_name = 'All Branches';
if($branch_id !== 'all') {
    $sql = "SELECT branch_name FROM tbl_branches WHERE branch_id = " . (int)$branch_id;
    $result = mysql_query($sql);
    if($result && mysql_num_rows($result) > 0) {
        $row = mysql_fetch_assoc($result);
        $branch_name = $row['branch_name'];
    }
}

// Generate report based on type
switch($report_type) {
    case 'attendance-summary':
        generateAttendanceSummary($branch_condition, $branch_name, $date_from, $date_to, $format);
        break;
    case 'attendance-detailed':
        generateDetailedAttendance($branch_condition, $branch_name, $date_from, $date_to, $format);
        break;
    case 'late-arrivals':
        generateLateArrivals($branch_condition, $branch_name, $date_from, $date_to, $format);
        break;
    case 'employee-list':
        generateEmployeeList($branch_condition, $branch_name, $format);
        break;
    case 'employee-performance':
        generateEmployeePerformance($branch_condition, $branch_name, $date_from, $date_to, $format);
        break;
    case 'department-wise':
        generateDepartmentAnalysis($branch_condition, $branch_name, $date_from, $date_to, $format);
        break;
    case 'branch-comparison':
        generateBranchComparison($date_from, $date_to, $format);
        break;
    case 'access-logs':
        generateAccessLogs($branch_condition, $branch_name, $date_from, $date_to, $format);
        break;
    case 'monthly-analytics':
        generateMonthlyAnalytics($branch_condition, $branch_name, $date_from, $date_to, $format);
        break;
    case 'attendance-status':
        generateAttendanceStatus($branch_condition, $branch_name, $date_from, $date_to, $format);
        break;
    default:
        echo '<div class="alert alert-danger">Invalid report type: ' . htmlspecialchars($report_type) . '</div>';
        echo '<div class="alert alert-info">Available types: attendance-summary, attendance-detailed, late-arrivals, employee-list, employee-performance, department-wise, branch-comparison, access-logs, monthly-analytics, attendance-status</div>';
        exit;
}

function generateAttendanceSummary($branch_condition, $branch_name, $date_from, $date_to, $format) {
    // Get attendance summary data
    $sql = "SELECT 
                DATE(TimeRecord) as attendance_date,
                COUNT(DISTINCT CASE WHEN TimeFlag = 'IN' THEN AccessID END) as total_in,
                COUNT(DISTINCT CASE WHEN TimeFlag = 'OUT' THEN AccessID END) as total_out,
                COUNT(*) as total_records
            FROM tbl_in_out 
            WHERE DATE(TimeRecord) BETWEEN '$date_from' AND '$date_to' $branch_condition
            GROUP BY DATE(TimeRecord)
            ORDER BY attendance_date DESC";
    
    $result = mysql_query($sql);
    
    if($format === 'html') {
        echo "<div class='report-header'>";
        echo "<h2>Attendance Summary Report</h2>";
        echo "<p><strong>Branch:</strong> $branch_name</p>";
        echo "<p><strong>Period:</strong> $date_from to $date_to</p>";
        echo "<p><strong>Generated:</strong> " . date('Y-m-d H:i:s') . "</p>";
        echo "</div>";
        
        echo "<table class='table table-striped table-bordered'>";
        echo "<thead><tr>";
        echo "<th>Date</th><th>Total Check-ins</th><th>Total Check-outs</th><th>Total Records</th>";
        echo "</tr></thead><tbody>";
        
        $total_in = $total_out = $total_records = 0;
        
        if($result && mysql_num_rows($result) > 0) {
            while($row = mysql_fetch_assoc($result)) {
                echo "<tr>";
                echo "<td>" . date('M j, Y', strtotime($row['attendance_date'])) . "</td>";
                echo "<td>" . $row['total_in'] . "</td>";
                echo "<td>" . $row['total_out'] . "</td>";
                echo "<td>" . $row['total_records'] . "</td>";
                echo "</tr>";
                
                $total_in += $row['total_in'];
                $total_out += $row['total_out'];
                $total_records += $row['total_records'];
            }
        } else {
            echo "<tr><td colspan='4' class='text-center'>No attendance data found</td></tr>";
        }
        
        echo "</tbody>";
        echo "<tfoot><tr class='info'>";
        echo "<th>Total</th><th>$total_in</th><th>$total_out</th><th>$total_records</th>";
        echo "</tr></tfoot>";
        echo "</table>";
    }
}

function generateDetailedAttendance($branch_condition, $branch_name, $date_from, $date_to, $format) {
    $sql = "SELECT DISTINCT
                i.AccessID,
                i.FullName,
                i.TimeRecord,
                i.TimeFlag,
                i.AccessArea,
                p.Position,
                p.Address
            FROM tbl_in_out i
            LEFT JOIN tbl_personnel p ON i.AccessID = p.AccessID
            WHERE DATE(i.TimeRecord) BETWEEN '$date_from' AND '$date_to' $branch_condition
            GROUP BY i.AccessID, i.TimeRecord, i.TimeFlag
            ORDER BY i.TimeRecord DESC";
    
    $result = mysql_query($sql);
    
    if($format === 'html') {
        echo "<div class='report-header'>";
        echo "<h2>Detailed Attendance Report</h2>";
        echo "<p><strong>Branch:</strong> $branch_name</p>";
        echo "<p><strong>Period:</strong> $date_from to $date_to</p>";
        echo "<p><strong>Generated:</strong> " . date('Y-m-d H:i:s') . "</p>";
        echo "</div>";
        
        echo "<table class='table table-striped table-bordered'>";
        echo "<thead><tr>";
        echo "<th>Access ID</th><th>Employee Name</th><th>Date & Time</th><th>Action</th><th>Access Area</th><th>Position</th><th>Address</th>";
        echo "</tr></thead><tbody>";
        
        if($result && mysql_num_rows($result) > 0) {
            while($row = mysql_fetch_assoc($result)) {
                $badge_class = $row['TimeFlag'] == 'IN' ? 'success' : 'warning';
                echo "<tr>";
                echo "<td>" . $row['AccessID'] . "</td>";
                echo "<td>" . $row['FullName'] . "</td>";
                echo "<td>" . date('M j, Y g:i A', strtotime($row['TimeRecord'])) . "</td>";
                echo "<td><span class='label label-$badge_class'>" . $row['TimeFlag'] . "</span></td>";
                echo "<td>" . $row['AccessArea'] . "</td>";
                echo "<td>" . ($row['Position'] ?: 'N/A') . "</td>";
                echo "<td>" . ($row['Address'] ?: 'N/A') . "</td>";
                echo "</tr>";
            }
        } else {
            echo "<tr><td colspan='7' class='text-center'>No attendance records found</td></tr>";
        }
        
        echo "</tbody></table>";
    }
}

function generateLateArrivals($branch_condition, $branch_name, $date_from, $date_to, $format) {
    $sql = "SELECT 
                i.AccessID,
                i.FullName,
                DATE(i.TimeRecord) as attendance_date,
                MIN(TIME(i.TimeRecord)) as first_in_time,
                p.TimeIN as scheduled_time,
                p.Position,
                p.Address
            FROM tbl_in_out i
            LEFT JOIN tbl_personnel p ON i.AccessID = p.AccessID
            WHERE i.TimeFlag = 'IN' 
            AND DATE(i.TimeRecord) BETWEEN '$date_from' AND '$date_to' $branch_condition
            GROUP BY i.AccessID, DATE(i.TimeRecord)
            HAVING TIME(first_in_time) > p.TimeIN
            ORDER BY attendance_date DESC, first_in_time DESC";
    
    $result = mysql_query($sql);
    
    if($format === 'html') {
        echo "<div class='report-header'>";
        echo "<h2>Late Arrivals Report</h2>";
        echo "<p><strong>Branch:</strong> $branch_name</p>";
        echo "<p><strong>Period:</strong> $date_from to $date_to</p>";
        echo "<p><strong>Generated:</strong> " . date('Y-m-d H:i:s') . "</p>";
        echo "</div>";
        
        echo "<table class='table table-striped table-bordered'>";
        echo "<thead><tr>";
        echo "<th>Access ID</th><th>Employee Name</th><th>Date</th><th>Scheduled Time</th><th>Actual Time</th><th>Late By</th><th>Position</th><th>Department</th>";
        echo "</tr></thead><tbody>";
        
        if($result && mysql_num_rows($result) > 0) {
            while($row = mysql_fetch_assoc($result)) {
                $scheduled = strtotime($row['scheduled_time']);
                $actual = strtotime($row['first_in_time']);
                $late_minutes = ($actual - $scheduled) / 60;
                
                echo "<tr>";
                echo "<td>" . $row['AccessID'] . "</td>";
                echo "<td>" . $row['FullName'] . "</td>";
                echo "<td>" . date('M j, Y', strtotime($row['attendance_date'])) . "</td>";
                echo "<td>" . date('g:i A', $scheduled) . "</td>";
                echo "<td>" . date('g:i A', $actual) . "</td>";
                echo "<td><span class='label label-danger'>" . round($late_minutes) . " min</span></td>";
                echo "<td>" . ($row['Position'] ?: 'N/A') . "</td>";
                echo "<td>" . ($row['Address'] ?: 'N/A') . "</td>";
                echo "</tr>";
            }
        } else {
            echo "<tr><td colspan='8' class='text-center'>No late arrivals found</td></tr>";
        }
        
        echo "</tbody></table>";
    }
}

function generateEmployeeList($branch_condition, $branch_name, $format) {
    $sql = "SELECT
                MAX(EmployeeID) as EmployeeID,
                AccessID,
                MAX(FullName) as FullName,
                MAX(Position) as Position,
                MAX(CASE WHEN AgencyCompany IS NOT NULL AND AgencyCompany != '' THEN AgencyCompany ELSE 'N/A' END) as AgencyCompany,
                MAX(ContactNo) as ContactNo,
                MAX(DateHired) as DateHired,
                MAX(Status) as Status,
                MAX(TimeIN) as TimeIN,
                MAX(TimeOut) as TimeOut,
                MAX(schedule_dates) as schedule_dates
            FROM tbl_personnel
            WHERE 1=1 $branch_condition
            GROUP BY AccessID
            ORDER BY MAX(FullName)";
    
    $result = mysql_query($sql);
    
    if($format === 'html') {
        echo "<div class='report-header'>";
        echo "<h2>Employee Directory Report</h2>";
        echo "<p><strong>Branch:</strong> $branch_name</p>";
        echo "<p><strong>Generated:</strong> " . date('Y-m-d H:i:s') . "</p>";
        echo "</div>";
        
        echo "<table class='table table-striped table-bordered'>";
        echo "<thead><tr>";
        echo "<th>Employee ID</th><th>Access ID</th><th>Full Name</th><th>Position</th><th>Department</th><th>Contact</th><th>Date Hired</th><th>Schedule</th><th>Status</th>";
        echo "</tr></thead><tbody>";
        
        if($result && mysql_num_rows($result) > 0) {
            while($row = mysql_fetch_assoc($result)) {
                $status_class = ($row['Status'] == 'Resigned') ? 'danger' : 'success';
                $status_text = $row['Status'] ?: 'Active';
                
                echo "<tr>";
                echo "<td>" . $row['EmployeeID'] . "</td>";
                echo "<td>" . $row['AccessID'] . "</td>";
                echo "<td>" . $row['FullName'] . "</td>";
                echo "<td>" . ($row['Position'] ?: 'N/A') . "</td>";
                echo "<td>" . ($row['AgencyCompany'] ?: 'N/A') . "</td>";
                echo "<td>" . ($row['ContactNo'] ?: 'N/A') . "</td>";
                echo "<td>" . ($row['DateHired'] ? date('M j, Y', strtotime($row['DateHired'])) : 'N/A') . "</td>";
                echo "<td>" . date('g:i A', strtotime($row['TimeIN'])) . " - " . date('g:i A', strtotime($row['TimeOut'])) . "</td>";
                echo "<td><span class='label label-$status_class'>$status_text</span></td>";
                echo "</tr>";
            }
        } else {
            echo "<tr><td colspan='9' class='text-center'>No employees found</td></tr>";
        }
        
        echo "</tbody></table>";
    }
}

function generateBranchComparison($date_from, $date_to, $format) {
    $sql = "SELECT 
                b.branch_name,
                b.branch_location,
                COUNT(DISTINCT p.AccessID) as total_employees,
                COUNT(DISTINCT CASE WHEN p.Status != 'Resigned' OR p.Status IS NULL THEN p.AccessID END) as active_employees,
                COUNT(DISTINCT CASE WHEN DATE(i.TimeRecord) BETWEEN '$date_from' AND '$date_to' THEN i.AccessID END) as attended_employees,
                COUNT(CASE WHEN DATE(i.TimeRecord) BETWEEN '$date_from' AND '$date_to' THEN i.IDno END) as total_attendance_records
            FROM tbl_branches b
            LEFT JOIN tbl_personnel p ON b.branch_id = p.branch_id
            LEFT JOIN tbl_in_out i ON b.branch_id = i.branch_id
            GROUP BY b.branch_id, b.branch_name, b.branch_location
            ORDER BY b.branch_name";
    
    $result = mysql_query($sql);
    
    if($format === 'html') {
        echo "<div class='report-header'>";
        echo "<h2>Branch Comparison Report</h2>";
        echo "<p><strong>Period:</strong> $date_from to $date_to</p>";
        echo "<p><strong>Generated:</strong> " . date('Y-m-d H:i:s') . "</p>";
        echo "</div>";
        
        echo "<table class='table table-striped table-bordered'>";
        echo "<thead><tr>";
        echo "<th>Branch Name</th><th>Location</th><th>Total Employees</th><th>Active Employees</th><th>Attended Employees</th><th>Total Records</th><th>Attendance Rate</th>";
        echo "</tr></thead><tbody>";
        
        if($result && mysql_num_rows($result) > 0) {
            while($row = mysql_fetch_assoc($result)) {
                $attendance_rate = $row['active_employees'] > 0 ? round(($row['attended_employees'] / $row['active_employees']) * 100, 1) : 0;
                
                echo "<tr>";
                echo "<td>" . $row['branch_name'] . "</td>";
                echo "<td>" . $row['branch_location'] . "</td>";
                echo "<td>" . $row['total_employees'] . "</td>";
                echo "<td>" . $row['active_employees'] . "</td>";
                echo "<td>" . $row['attended_employees'] . "</td>";
                echo "<td>" . $row['total_attendance_records'] . "</td>";
                echo "<td><span class='label label-info'>$attendance_rate%</span></td>";
                echo "</tr>";
            }
        } else {
            echo "<tr><td colspan='7' class='text-center'>No branch data found</td></tr>";
        }
        
        echo "</tbody></table>";
    }
}

// Add CSS for better report styling
if($format === 'html') {
    echo "<style>
        .report-header { 
            background: #f8f9fa; 
            padding: 20px; 
            border-radius: 5px; 
            margin-bottom: 20px; 
            border-left: 4px solid #007bff;
        }
        .report-header h2 { 
            margin-top: 0; 
            color: #333; 
        }
        .table { 
            font-size: 0.9rem; 
        }
        .table th { 
            background: #007bff; 
            color: white; 
            font-weight: bold;
        }
        .label { 
            padding: 4px 8px; 
            border-radius: 3px; 
            font-size: 0.8rem;
        }
        .label-success { background: #5cb85c; }
        .label-warning { background: #f0ad4e; }
        .label-danger { background: #d9534f; }
        .label-info { background: #5bc0de; }
        .table-responsive {
            border: 1px solid #ddd;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .attendance-table {
            margin-bottom: 0;
            font-size: 12px;
        }
        .attendance-table th {
            border-bottom: 2px solid #007bff !important;
            font-weight: bold;
            text-align: center;
        }
        .attendance-table td {
            border: 1px solid #e0e0e0;
            vertical-align: middle;
        }
        .employee-info-cell {
            background: #f8f9fa;
            border-right: 2px solid #007bff !important;
        }
        .label {
            font-weight: normal;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .input-group-addon {
            background: #f8f9fa;
            border: 1px solid #ddd;
            color: #666;
        }
        #employeeSearch {
            border-left: none;
        }
        #employeeSearch:focus {
            border-color: #007bff;
            box-shadow: 0 0 5px rgba(0,123,255,0.3);
        }
        .btn-group .btn {
            margin-right: 2px;
        }
        .btn-group .btn.active {
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        .search-info {
            margin-top: 5px;
        }
        .employee-row {
            transition: opacity 0.3s ease;
        }
        .employee-row:hidden {
            opacity: 0;
        }
    </style>";
}

function generateAttendanceStatus($branch_condition, $branch_name, $date_from, $date_to, $format) {
    // Set timezone at the very beginning
    date_default_timezone_set('Asia/Karachi');
    $current_date_today = date('Y-m-d');

    // Get employees - Remove duplicates by AccessID with proper aggregation
    $sql = "SELECT
                AccessID,
                MAX(FullName) as FullName,
                MAX(Position) as Position,
                MAX(CASE WHEN Address IS NOT NULL AND Address != '' THEN Address ELSE 'N/A' END) as Address
            FROM tbl_personnel p
            WHERE (Status != 'Resigned' OR Status IS NULL OR Status = '')
            $branch_condition
            GROUP BY AccessID
            ORDER BY MAX(FullName)";
    $result = mysql_query($sql);

    if(!$result || mysql_num_rows($result) == 0) {
        echo "<div class='alert alert-warning'>No employees found for the selected branch.</div>";
        return;
    }

    $employees = array();
    while($emp = mysql_fetch_assoc($result)) {
        $employees[] = $emp;
    }

    // Generate date range (limit to 31 days for better performance)
    $start_date = new DateTime($date_from);
    $end_date = new DateTime($date_to);
    $date_range = array();
    $day_count = 0;
    $max_days = 31; // Limit to 31 days for better table display

    while($start_date <= $end_date && $day_count < $max_days) {
        $date_range[] = $start_date->format('Y-m-d');
        $start_date->add(new DateInterval('P1D'));
        $day_count++;
    }

    if($day_count >= $max_days) {
        echo "<div class='alert alert-info'><i class='fa fa-info-circle'></i> <strong>Note:</strong> Showing first $max_days days of the selected period for better display. Use date filters to view specific periods.</div>";
    }

    echo "<div class='report-header'>";
    echo "<h2>Employee Attendance Status Report</h2>";
    echo "<p><strong>Branch:</strong> $branch_name</p>";
    echo "<p><strong>Period:</strong> $date_from to $date_to</p>";
    echo "<p><strong>Generated:</strong> " . date('Y-m-d H:i:s') . "</p>";
    echo "</div>";

    // Search Box
    echo "<div class='row' style='margin-bottom: 20px;'>";
    echo "<div class='col-md-6'>";
    echo "<div class='input-group'>";
    echo "<span class='input-group-addon'><i class='fa fa-search'></i></span>";
    echo "<input type='text' id='employeeSearch' class='form-control' placeholder='Search employees by name, ID, position, or department...' style='border-radius: 0 4px 4px 0;'>";
    echo "</div>";
    echo "<small class='text-muted'>Type to filter employees in real-time</small>";
    echo "</div>";
    echo "<div class='col-md-6 text-right'>";
    echo "<div class='btn-group' role='group'>";
    echo "<button type='button' class='btn btn-sm btn-default' onclick='filterByStatus(\"all\")' id='filter-all'>All</button>";
    echo "<button type='button' class='btn btn-sm btn-success' onclick='filterByStatus(\"present\")' id='filter-present'>Present</button>";
    echo "<button type='button' class='btn btn-sm btn-warning' onclick='filterByStatus(\"not-punched\")' id='filter-not-punched'>Not Punched</button>";
    echo "<button type='button' class='btn btn-sm btn-danger' onclick='filterByStatus(\"absent\")' id='filter-absent'>Absent</button>";
    echo "</div>";
    echo "<br><small class='text-muted'>Filter by today's status</small>";
    echo "</div>";
    echo "</div>";

    // Attendance Status Table
    echo "<div class='table-responsive' style='max-height: 600px; overflow-x: auto; overflow-y: auto; border: 1px solid #ddd; border-radius: 5px;'>";
    echo "<table class='table table-bordered table-striped attendance-table' style='min-width: 100%; white-space: nowrap;'>";
    echo "<thead style='background: #007bff; color: white; position: sticky; top: 0; z-index: 10;'>";
    echo "<tr>";
    echo "<th class='employee-info' style='position: sticky; left: 0; background: #007bff; z-index: 11; min-width: 200px; max-width: 200px;'>Employee</th>";
    echo "<th style='min-width: 120px; max-width: 120px;'>Position</th>";
    echo "<th style='min-width: 120px; max-width: 120px;'>Department</th>";

    foreach($date_range as $date) {
        $day_name = date('D', strtotime($date));
        $day_num = date('j', strtotime($date));
        $month = date('M', strtotime($date));
        echo "<th class='attendance-cell text-center' style='min-width: 80px; max-width: 80px; padding: 8px 4px;'>";
        echo "<div style='font-size: 11px; line-height: 1.2;'>";
        echo "<div style='font-weight: bold;'>$day_name</div>";
        echo "<div>$day_num $month</div>";
        echo "</div>";
        echo "</th>";
    }
    echo "</tr>";
    echo "</thead>";
    echo "<tbody>";

    foreach($employees as $employee) {
        // Calculate today's status for filtering
        $today = date('Y-m-d');
        $today_status = 'absent';

        $check_sql = "SELECT COUNT(CASE WHEN TimeFlag = 'IN' THEN 1 END) as checkins FROM tbl_in_out WHERE AccessID = '" . $employee['AccessID'] . "' AND DATE(TimeRecord) = '$today'";
        $check_result = mysql_query($check_sql);
        if($check_result && mysql_num_rows($check_result) > 0) {
            $check_data = mysql_fetch_assoc($check_result);
            if($check_data['checkins'] > 0) {
                $today_status = 'present';
            } else {
                $today_status = 'not-punched';
            }
        } else {
            $today_status = 'not-punched';
        }

        echo "<tr class='employee-row' data-employee-name='" . strtolower($employee['FullName']) . "' data-employee-id='" . strtolower($employee['AccessID']) . "' data-position='" . strtolower($employee['Position'] ?: '') . "' data-department='" . strtolower($employee['Address'] ?: '') . "' data-today-status='$today_status'>";
        echo "<td class='employee-info' style='position: sticky; left: 0; background: white; z-index: 5; border-right: 2px solid #007bff; min-width: 200px; max-width: 200px; padding: 8px;'>";
        echo "<div style='font-weight: bold; font-size: 13px; margin-bottom: 2px;'>" . $employee['FullName'] . "</div>";
        echo "<div style='font-size: 11px; color: #666;'>ID: " . $employee['AccessID'] . "</div>";
        echo "</td>";
        echo "<td style='min-width: 120px; max-width: 120px; padding: 8px; font-size: 12px;'>" . ($employee['Position'] ?: 'N/A') . "</td>";
        echo "<td style='min-width: 120px; max-width: 120px; padding: 8px; font-size: 12px;'>" . ($employee['Address'] ?: 'N/A') . "</td>";

        foreach($date_range as $date) {
            // Check attendance for this employee on this date - Remove duplicates
            $check_sql = "SELECT
                            COUNT(DISTINCT CASE WHEN TimeFlag = 'IN' THEN CONCAT(DATE(TimeRecord), '-', HOUR(TimeRecord), '-', MINUTE(TimeRecord)) END) as checkins,
                            COUNT(DISTINCT CASE WHEN TimeFlag = 'OUT' THEN CONCAT(DATE(TimeRecord), '-', HOUR(TimeRecord), '-', MINUTE(TimeRecord)) END) as checkouts,
                            MIN(CASE WHEN TimeFlag = 'IN' THEN TimeRecord END) as first_in,
                            MAX(CASE WHEN TimeFlag = 'OUT' THEN TimeRecord END) as last_out
                        FROM tbl_in_out
                        WHERE AccessID = '" . $employee['AccessID'] . "'
                        AND DATE(TimeRecord) = '$date'
                        GROUP BY AccessID, DATE(TimeRecord)";

            $att_result = mysql_query($check_sql);
            $status_class = 'danger';
            $status_text = 'Absent';
            $tooltip = 'No attendance record';

            if($att_result && mysql_num_rows($att_result) > 0) {
                $att_data = mysql_fetch_assoc($att_result);
                $checkins = $att_data['checkins'];
                $checkouts = $att_data['checkouts'];
                $first_in = $att_data['first_in'];
                $last_out = $att_data['last_out'];

                // Check if today's date using the consistent current date
                $is_today = ($date == $current_date_today);

                // Debug: Log the date comparison
                error_log("DEBUG - Date: $date, Current Date: $current_date_today, Is Today: " . ($is_today ? 'YES' : 'NO'));

                if($checkins > 0 && $checkouts > 0) {
                    // Complete attendance - both in and out
                    $status_class = 'success';
                    $status_text = 'Present';
                    $tooltip = 'Check-in: ' . date('g:i A', strtotime($first_in)) . ' | Check-out: ' . date('g:i A', strtotime($last_out));
                } elseif($checkins > 0 && $checkouts == 0) {
                    // Only check-in - consider as Present
                    $status_class = 'success';
                    $status_text = 'Present';
                    if($is_today) {
                        $tooltip = 'Check-in: ' . date('g:i A', strtotime($first_in)) . ' | Still at work';
                    } else {
                        $tooltip = 'Check-in: ' . date('g:i A', strtotime($first_in)) . ' | No check-out recorded';
                    }
                } elseif($checkins == 0 && $checkouts > 0) {
                    // Only check-out - unusual case, mark as Not Punched
                    $status_class = 'warning';
                    $status_text = 'Not Punched';
                    $tooltip = 'Check-out: ' . date('g:i A', strtotime($last_out)) . ' | Missing check-in';
                } else {
                    // No attendance
                    if($is_today) {
                        $status_class = 'warning';
                        $status_text = 'Not Punched';
                        $tooltip = 'No attendance record for today';
                    } else {
                        $status_class = 'danger';
                        $status_text = 'Absent';
                        $tooltip = 'No attendance record';
                    }
                }
            } else {
                // No attendance record found
                // Use the same consistent current date
                $is_today = ($date == $current_date_today);

                // Debug: Log the date comparison for no records
                error_log("DEBUG NO RECORD - Date: $date, Current Date: $current_date_today, Is Today: " . ($is_today ? 'YES' : 'NO'));

                if($is_today) {
                    $status_class = 'warning';
                    $status_text = 'Not Punched';
                    $tooltip = 'No attendance record for today';
                } else {
                    $status_class = 'danger';
                    $status_text = 'Absent';
                    $tooltip = 'No attendance record';
                }
            }

            echo "<td class='attendance-cell text-center' style='min-width: 80px; max-width: 80px; padding: 4px; vertical-align: middle;'>";
            echo "<span class='label label-$status_class' title='$tooltip' data-toggle='tooltip' style='font-size: 10px; padding: 3px 6px; display: inline-block; min-width: 60px;'>$status_text</span>";
            echo "</td>";
        }
        echo "</tr>";
    }

    echo "</tbody>";
    echo "</table>";
    echo "</div>";

    // Today's Summary Statistics
    echo "<div class='row' style='margin-top: 30px;'>";
    echo "<div class='col-md-12'>";

    // Use the same consistent current date
    $today = $current_date_today;

    echo "<h4>Today's Summary (" . date('M j, Y', strtotime($today)) . ") - Current Date: $today</h4>";
    echo "<div class='row'>";

    // Calculate TODAY's statistics only
    $total_employees = count($employees);
    $present_today = 0;
    $not_punched_today = 0;

    foreach($employees as $employee) {
        $check_sql = "SELECT
                        COUNT(DISTINCT CASE WHEN TimeFlag = 'IN' THEN CONCAT(DATE(TimeRecord), '-', HOUR(TimeRecord), '-', MINUTE(TimeRecord)) END) as checkins,
                        COUNT(DISTINCT CASE WHEN TimeFlag = 'OUT' THEN CONCAT(DATE(TimeRecord), '-', HOUR(TimeRecord), '-', MINUTE(TimeRecord)) END) as checkouts
                    FROM tbl_in_out
                    WHERE AccessID = '" . $employee['AccessID'] . "'
                    AND DATE(TimeRecord) = '$today'
                    GROUP BY AccessID, DATE(TimeRecord)";

        $check_result = mysql_query($check_sql);
        $checkins = 0;
        $checkouts = 0;

        if($check_result && mysql_num_rows($check_result) > 0) {
            $check_data = mysql_fetch_assoc($check_result);
            $checkins = $check_data['checkins'];
            $checkouts = $check_data['checkouts'];
        }

        if($checkins > 0) {
            // Has check-in (with or without check-out) = Present
            $present_today++;
        } else {
            // No check-in = Not Punched (for today)
            $not_punched_today++;
        }
    }

    $present_percentage = $total_employees > 0 ? round(($present_today / $total_employees) * 100, 1) : 0;
    $not_punched_percentage = $total_employees > 0 ? round(($not_punched_today / $total_employees) * 100, 1) : 0;

    echo "<div class='col-md-4'>";
    echo "<div class='panel panel-success'>";
    echo "<div class='panel-body text-center'>";
    echo "<h3>$present_today</h3>";
    echo "<p>Present Today<br><small>$present_percentage%</small></p>";
    echo "</div></div></div>";

    echo "<div class='col-md-4'>";
    echo "<div class='panel panel-warning'>";
    echo "<div class='panel-body text-center'>";
    echo "<h3>$not_punched_today</h3>";
    echo "<p>Not Punched Today<br><small>$not_punched_percentage%</small></p>";
    echo "</div></div></div>";

    echo "<div class='col-md-4'>";
    echo "<div class='panel panel-info'>";
    echo "<div class='panel-body text-center'>";
    echo "<h3>$total_employees</h3>";
    echo "<p>Total Employees<br><small>100%</small></p>";
    echo "</div></div></div>";

    echo "</div></div></div>";

    // Add JavaScript for search and filtering
    echo "<script>";
    echo "
    $(document).ready(function() {
        // Employee search functionality
        $('#employeeSearch').on('keyup', function() {
            var searchTerm = $(this).val().toLowerCase();
            filterEmployees();
        });

        // Set initial filter state
        $('#filter-all').addClass('active');

        function filterEmployees() {
            var searchTerm = $('#employeeSearch').val().toLowerCase();
            var statusFilter = $('.btn-group .btn.active').attr('id');
            var visibleCount = 0;

            $('.employee-row').each(function() {
                var \$row = $(this);
                var employeeName = \$row.data('employee-name');
                var employeeId = \$row.data('employee-id');
                var position = \$row.data('position');
                var department = \$row.data('department');
                var todayStatus = \$row.data('today-status');

                // Check search term match
                var searchMatch = searchTerm === '' ||
                    employeeName.includes(searchTerm) ||
                    employeeId.includes(searchTerm) ||
                    position.includes(searchTerm) ||
                    department.includes(searchTerm);

                // Check status filter match
                var statusMatch = true;
                if (statusFilter === 'filter-present') {
                    statusMatch = todayStatus === 'present';
                } else if (statusFilter === 'filter-not-punched') {
                    statusMatch = todayStatus === 'not-punched';
                } else if (statusFilter === 'filter-absent') {
                    statusMatch = todayStatus === 'absent';
                }

                if (searchMatch && statusMatch) {
                    \$row.show();
                    visibleCount++;
                } else {
                    \$row.hide();
                }
            });

            // Update search results info
            updateSearchInfo(visibleCount);
        }

        function updateSearchInfo(visibleCount) {
            var totalCount = $('.employee-row').length;
            var searchTerm = $('#employeeSearch').val();

            // Remove existing search info
            $('.search-info').remove();

            if (searchTerm || $('.btn-group .btn.active').attr('id') !== 'filter-all') {
                var infoText = 'Showing ' + visibleCount + ' of ' + totalCount + ' employees';
                $('#employeeSearch').parent().parent().append('<div class=\"search-info\"><small class=\"text-info\"><i class=\"fa fa-info-circle\"></i> ' + infoText + '</small></div>');
            }
        }

        // Clear search
        $('#employeeSearch').on('input', function() {
            if ($(this).val() === '') {
                filterEmployees();
            }
        });
    });

    // Status filter functions
    function filterByStatus(status) {
        // Update button states
        $('.btn-group .btn').removeClass('active');
        $('#filter-' + status).addClass('active');

        // Apply filter
        filterEmployees();
    }

    // Make filterEmployees available globally
    function filterEmployees() {
        var searchTerm = $('#employeeSearch').val().toLowerCase();
        var statusFilter = $('.btn-group .btn.active').attr('id');
        var visibleCount = 0;

        $('.employee-row').each(function() {
            var \$row = $(this);
            var employeeName = \$row.data('employee-name');
            var employeeId = \$row.data('employee-id');
            var position = \$row.data('position');
            var department = \$row.data('department');
            var todayStatus = \$row.data('today-status');

            // Check search term match
            var searchMatch = searchTerm === '' ||
                employeeName.includes(searchTerm) ||
                employeeId.includes(searchTerm) ||
                position.includes(searchTerm) ||
                department.includes(searchTerm);

            // Check status filter match
            var statusMatch = true;
            if (statusFilter === 'filter-present') {
                statusMatch = todayStatus === 'present';
            } else if (statusFilter === 'filter-not-punched') {
                statusMatch = todayStatus === 'not-punched';
            } else if (statusFilter === 'filter-absent') {
                statusMatch = todayStatus === 'absent';
            }

            if (searchMatch && statusMatch) {
                \$row.show();
                visibleCount++;
            } else {
                \$row.hide();
            }
        });

        // Update search results info
        updateSearchInfo(visibleCount);
    }

    function updateSearchInfo(visibleCount) {
        var totalCount = $('.employee-row').length;
        var searchTerm = $('#employeeSearch').val();

        // Remove existing search info
        $('.search-info').remove();

        if (searchTerm || $('.btn-group .btn.active').attr('id') !== 'filter-all') {
            var infoText = 'Showing ' + visibleCount + ' of ' + totalCount + ' employees';
            $('#employeeSearch').parent().parent().append('<div class=\"search-info\"><small class=\"text-info\"><i class=\"fa fa-info-circle\"></i> ' + infoText + '</small></div>');
        }
    }
    ";
    echo "</script>";
}
?>
