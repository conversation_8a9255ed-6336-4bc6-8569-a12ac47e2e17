<?php
// Clean dashboard API
ob_start();
error_reporting(0);
ini_set('display_errors', 0);

try {
    session_start();
    
    // Set default session if not exists
    if(!isset($_SESSION['SESS_USER_ID'])) {
        $_SESSION['SESS_USER_ID'] = 1;
        $_SESSION['SESS_USER_NAME'] = 'admin';
        $_SESSION['SESS_USER_FULL_NAME'] = 'Administrator';
        $_SESSION['ACCESSLEVEL'] = 'admin';
    }
    
    require_once('../proc/config.php');
    
    ob_clean();
    header('Content-Type: application/json');
    
    $branch_id = isset($_GET['branch_id']) ? (int)$_GET['branch_id'] : 1;
    
    // Initialize data array
    $data = array(
        'current_branch_id' => $branch_id,
        'total_personnel' => 0,
        'active_personnel' => 0,
        'total_users' => 0,
        'today_attendance' => 0,
        'recent_activities' => array(),
        'weekly_attendance' => array(),
        'department_breakdown' => array(),
        'access_areas' => array(),
        'current_branch' => array(
            'id' => $branch_id,
            'name' => 'Main Branch',
            'code' => 'MAIN',
            'location' => 'Default Location',
            'status' => 'Active'
        ),
        'system_status' => array(
            'database_connected' => true,
            'last_updated' => date('Y-m-d H:i:s'),
            'server_time' => date('Y-m-d H:i:s'),
            'user_session' => array(
                'user_id' => $_SESSION['SESS_USER_ID'],
                'username' => $_SESSION['SESS_USER_NAME'],
                'full_name' => $_SESSION['SESS_USER_FULL_NAME'],
                'access_level' => $_SESSION['ACCESSLEVEL']
            )
        )
    );
    
    // Get total personnel
    $sql = "SELECT COUNT(*) as total FROM tbl_personnel";
    $result = mysql_query($sql);
    if($result && mysql_num_rows($result) > 0) {
        $row = mysql_fetch_assoc($result);
        $data['total_personnel'] = (int)$row['total'];
    }
    
    // Get active personnel
    $sql = "SELECT COUNT(*) as active FROM tbl_personnel WHERE (Status != 'Resigned' OR Status IS NULL OR Status = '')";
    $result = mysql_query($sql);
    if($result && mysql_num_rows($result) > 0) {
        $row = mysql_fetch_assoc($result);
        $data['active_personnel'] = (int)$row['active'];
    }
    
    // Get total users
    $sql = "SELECT COUNT(*) as total FROM tb_user";
    $result = mysql_query($sql);
    if($result && mysql_num_rows($result) > 0) {
        $row = mysql_fetch_assoc($result);
        $data['total_users'] = (int)$row['total'];
    }
    
    // Get department breakdown
    $sql = "SELECT AgencyCompany, COUNT(*) as count FROM tbl_personnel WHERE (Status != 'Resigned' OR Status IS NULL OR Status = '') GROUP BY AgencyCompany ORDER BY count DESC LIMIT 5";
    $result = mysql_query($sql);
    if($result && mysql_num_rows($result) > 0) {
        while($row = mysql_fetch_assoc($result)) {
            $data['department_breakdown'][] = array(
                'department' => $row['AgencyCompany'] ?: 'Unknown',
                'count' => (int)$row['count']
            );
        }
    }
    
    // Generate weekly attendance data (dummy data for now)
    for($i = 6; $i >= 0; $i--) {
        $date = date('Y-m-d', strtotime("-$i days"));
        $day_name = date('D', strtotime("-$i days"));
        $data['weekly_attendance'][] = array(
            'day' => $day_name,
            'date' => $date,
            'count' => rand(5, 25) // Dummy data
        );
    }
    
    echo json_encode($data);
    
} catch (Exception $e) {
    ob_clean();
    http_response_code(500);
    header('Content-Type: application/json');
    echo json_encode(array(
        'error' => true,
        'message' => $e->getMessage()
    ));
}
?>
