<?php
// Test the duplicate ID fix
include('proc/config.php');
include('function/generateEmployeeIDs.php');

echo "<h3>Testing Duplicate ID Prevention</h3>";

// Show current IDs in database
echo "<h4>Current Employee IDs in Database:</h4>";
$sql = "SELECT EmployeeID, AccessID, FullName FROM tbl_personnel ORDER BY CAST(EmployeeID AS UNSIGNED)";
$result = mysql_query($sql);

if($result) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr style='background: #f0f0f0;'><th>Employee ID</th><th>Biometric ID</th><th>Full Name</th></tr>";
    
    $employeeIDs = array();
    $biometricIDs = array();
    
    while($row = mysql_fetch_assoc($result)) {
        $employeeIDs[] = $row['EmployeeID'];
        $biometricIDs[] = $row['AccessID'];
        
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['EmployeeID']) . "</td>";
        echo "<td>" . htmlspecialchars($row['AccessID']) . "</td>";
        echo "<td>" . htmlspecialchars($row['FullName']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<p><strong>Existing Employee IDs:</strong> " . implode(', ', $employeeIDs) . "</p>";
    echo "<p><strong>Existing Biometric IDs:</strong> " . implode(', ', $biometricIDs) . "</p>";
    
} else {
    echo "Error: " . mysql_error();
}

// Test ID generation
echo "<h4>Testing ID Generation (10 attempts):</h4>";
echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr style='background: #f0f0f0;'><th>Attempt</th><th>Generated Employee ID</th><th>Generated Biometric ID</th><th>Employee Unique?</th><th>Biometric Unique?</th></tr>";

for($i = 1; $i <= 10; $i++) {
    $empID = generateUniqueEmployeeID();
    $bioID = generateUniqueBiometricID();
    
    $empUnique = isEmployeeIDUnique($empID) ? 'YES' : 'NO';
    $bioUnique = isBiometricIDUnique($bioID) ? 'YES' : 'NO';
    
    echo "<tr>";
    echo "<td>$i</td>";
    echo "<td>$empID</td>";
    echo "<td>$bioID</td>";
    echo "<td style='color: " . ($empUnique == 'YES' ? 'green' : 'red') . ";'>$empUnique</td>";
    echo "<td style='color: " . ($bioUnique == 'YES' ? 'green' : 'red') . ";'>$bioUnique</td>";
    echo "</tr>";
}
echo "</table>";

// Test the duplicate check logic
echo "<h4>Testing Duplicate Check Logic:</h4>";
$testEmpID = '1003'; // The ID that was causing the error
$testBioID = 'B003';

$checkDuplicate = mysql_query("SELECT COUNT(*) as count FROM tbl_personnel WHERE EmployeeID='$testEmpID' OR AccessID='$testBioID'");
$duplicateRow = mysql_fetch_assoc($checkDuplicate);

echo "<p><strong>Testing Employee ID '$testEmpID' and Biometric ID '$testBioID':</strong></p>";
echo "<p>Duplicates found: " . $duplicateRow['count'] . "</p>";

if($duplicateRow['count'] > 0) {
    echo "<p style='color: orange;'>⚠️ These IDs already exist - system will generate new ones</p>";
    $newEmpID = generateUniqueEmployeeID();
    $newBioID = generateUniqueBiometricID();
    echo "<p>New Employee ID would be: <strong>$newEmpID</strong></p>";
    echo "<p>New Biometric ID would be: <strong>$newBioID</strong></p>";
} else {
    echo "<p style='color: green;'>✅ These IDs are available</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { border-collapse: collapse; margin: 10px 0; }
th, td { padding: 8px; border: 1px solid #ccc; text-align: left; }
th { background: #f0f0f0; }
</style>
