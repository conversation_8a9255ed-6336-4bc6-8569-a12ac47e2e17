
// Row highlighting on edit styles
//
// To change the colour of the highlight, simply modify the variable below and
// recompile the SCSS stylesheet (if you don't have SASS installed, you can use
// the online service at http://sassmeister.com/ .
//
// The DataTables styles below match the default DataTables stylesheet:
// http://next.datatables.net/manual/styling/classes so you can retain the full
// benefits of the DataTables styling options.

$table-row-highlight: #FFFBCC;

table.dataTable {
	tbody {
		tr.highlight {
			background-color: $table-row-highlight !important;
		}

		tr.highlight,
		tr.noHighlight,
		tr.highlight td,
		tr.noHighlight td {
			@include background-transision();
		}
	}

	&.stripe tbody,
	&.display tbody {
		tr.odd {
			&.highlight {
				background-color: shade($table-row-highlight, 2.35%);
			}
		}
	}

	// Hover classes - add "hover" class to the table to activate
	&.hover tbody,
	&.display tbody {
		tr:hover,
		tr.odd:hover,
		tr.even:hover {
			&.highlight {
				background-color: shade($table-row-highlight, 3.6%);
			}
		}
	}


	// Sort column highlighting - add "hover" class to the table to activate
	&.order-column,
	&.display {
		tbody {
			tr.highlight>.sorting_1,
			tr.highlight>.sorting_2,
			tr.highlight>.sorting_3 {
				background-color: shade($table-row-highlight, 2%);
			}
		}
	}

	&.display tbody,
	&.order-column.stripe tbody {
		tr.odd {
			&.highlight {
				>.sorting_1 { background-color: shade($table-row-highlight, 5.4%);}
				>.sorting_2 { background-color: shade($table-row-highlight, 4.7%);}
				>.sorting_3 { background-color: shade($table-row-highlight, 3.9%);}
			}
		}

		tr.even {
			&.highlight {
				>.sorting_1 { background-color: shade($table-row-highlight, 2%); }
				>.sorting_2 { background-color: shade($table-row-highlight, 1.2%); }
				>.sorting_3 { background-color: shade($table-row-highlight, 0.4%); }
			}
		}
	}

	&.display tbody,
	&.order-column.hover tbody {
		tr:hover,
		tr.odd:hover,
		tr.even:hover {
			&.highlight {
				>.sorting_1 { background-color: shade($table-row-highlight, 8.2%); }
				>.sorting_2 { background-color: shade($table-row-highlight, 7.5%); }
				>.sorting_3 { background-color: shade($table-row-highlight, 6.3%); }
			}
		}
	}
}