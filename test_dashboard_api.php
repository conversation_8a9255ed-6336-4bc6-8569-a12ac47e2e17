<?php
// Test the dashboard API
session_start();

// Set up a basic session for testing
if(!isset($_SESSION['SESS_USER_ID'])) {
    $_SESSION['SESS_USER_ID'] = 1;
    $_SESSION['SESS_USER_NAME'] = 'test_user';
    $_SESSION['SESS_USER_FULL_NAME'] = 'Test User';
    $_SESSION['ACCESSLEVEL'] = 'admin';
}

echo "<h3>Testing Dashboard API</h3>";

// Test direct include
echo "<h4>Direct API Test:</h4>";
echo "<pre>";
ob_start();
$_GET['branch_id'] = 2; // Set the branch ID that was causing the error
include('api/dashboard_data.php');
$output = ob_get_clean();

echo "Raw API Output:\n";
echo htmlspecialchars($output);
echo "</pre>";

// Test JSON validity
echo "<h4>JSON Validation:</h4>";
$json = json_decode($output, true);
if($json === null) {
    echo "<p style='color: red;'>❌ Invalid JSON! Error: " . json_last_error_msg() . "</p>";
} else {
    echo "<p style='color: green;'>✅ Valid JSON!</p>";
    echo "<h5>Data Summary:</h5>";
    echo "<ul>";
    echo "<li>Total Personnel: " . ($json['total_personnel'] ?? 'N/A') . "</li>";
    echo "<li>Active Personnel: " . ($json['active_personnel'] ?? 'N/A') . "</li>";
    echo "<li>Total Users: " . ($json['total_users'] ?? 'N/A') . "</li>";
    echo "<li>Today's Attendance: " . ($json['today_attendance'] ?? 'N/A') . "</li>";
    echo "<li>Recent Activities: " . count($json['recent_activities'] ?? []) . " items</li>";
    echo "<li>Weekly Attendance: " . count($json['weekly_attendance'] ?? []) . " days</li>";
    echo "<li>Department Breakdown: " . count($json['department_breakdown'] ?? []) . " departments</li>";
    echo "</ul>";
}

// Test database tables
echo "<h4>Database Table Check:</h4>";
include('proc/config.php');

$tables_to_check = ['tbl_personnel', 'tb_user', 'tbl_in_out', 'tbl_arealist', 'tbl_branches'];

foreach($tables_to_check as $table) {
    $result = mysql_query("SHOW TABLES LIKE '$table'");
    $exists = mysql_num_rows($result) > 0;
    $status = $exists ? '✅' : '❌';
    echo "<p>$status Table '$table': " . ($exists ? 'EXISTS' : 'NOT FOUND') . "</p>";
    
    if($exists) {
        $count_result = mysql_query("SELECT COUNT(*) as count FROM $table");
        if($count_result) {
            $count_row = mysql_fetch_assoc($count_result);
            echo "<p style='margin-left: 20px;'>Records: " . $count_row['count'] . "</p>";
        }
    }
}

// Test AJAX call simulation
echo "<h4>AJAX Simulation Test:</h4>";
echo "<button onclick='testAjaxCall()'>Test AJAX Call</button>";
echo "<div id='ajaxResult'></div>";
?>

<script>
function testAjaxCall() {
    fetch('api/dashboard_data.php?branch_id=2')
        .then(response => {
            if (!response.ok) {
                throw new Error('HTTP ' + response.status + ': ' + response.statusText);
            }
            return response.json();
        })
        .then(data => {
            document.getElementById('ajaxResult').innerHTML = 
                '<h5>AJAX Success!</h5>' +
                '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
        })
        .catch(error => {
            document.getElementById('ajaxResult').innerHTML = 
                '<h5 style="color: red;">AJAX Error!</h5>' +
                '<p>' + error.message + '</p>';
        });
}
</script>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
button { padding: 10px 15px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
button:hover { background: #0056b3; }
#ajaxResult { margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 4px; }
</style>
