<?php
	// Online Database Configuration
	// Replace these values with your hosting provider's database details
	define('DB_HOST', 'localhost');        // Your database server hostname
	define('DB_USER', 'root');        // Your database username
	define('DB_PASSWORD', '');    // Your database password
	define('DB_DATABASE', 'biometrics');        // Your database name
	date_default_timezone_set('Asia/Karachi');
	$CurTime = date('Y-m-d H:i:s');
    $link = mysqli_connect(DB_HOST, DB_USER, DB_PASSWORD, DB_DATABASE);


    if(!$link){
        die('Failed to connect to server: ' . mysqli_connect_error());
    }

	//Function to sanitize values received from the form. Prevents SQL injection
	if (!function_exists('clean')) {
		function clean($str) {
			global $link;
			$str = @trim($str);
			// get_magic_quotes_gpc() was removed in PHP 7.0+
			// Magic quotes are always off in modern PHP versions
			return mysqli_real_escape_string($link, $str);
		}
	}

	//Wrapper functions for backward compatibility
	if (!function_exists('mysql_query')) {
		function mysql_query($query) {
			global $link;
			return mysqli_query($link, $query);
		}
	}

	if (!function_exists('mysql_fetch_assoc')) {
		function mysql_fetch_assoc($result) {
			return mysqli_fetch_assoc($result);
		}
	}

	if (!function_exists('mysql_fetch_array')) {
		function mysql_fetch_array($result, $result_type = MYSQLI_BOTH) {
			return mysqli_fetch_array($result, $result_type);
		}
	}

	if (!function_exists('mysql_num_rows')) {
		function mysql_num_rows($result) {
			return mysqli_num_rows($result);
		}
	}

	if (!function_exists('mysql_error')) {
		function mysql_error() {
			global $link;
			return mysqli_error($link);
		}
	}

	if (!function_exists('mysql_insert_id')) {
		function mysql_insert_id() {
			global $link;
			return mysqli_insert_id($link);
		}
	}

	if (!function_exists('mysql_affected_rows')) {
		function mysql_affected_rows() {
			global $link;
			return mysqli_affected_rows($link);
		}
	}

	//Global_MySQL function for backward compatibility
	if (!function_exists('Global_MySQL')) {
		function Global_MySQL($database, $query, $return_array = false) {
			global $link;
			$result = mysqli_query($link, $query);

			if (!$result) {
				die("Query failed: " . mysqli_error($link));
			}

			if ($return_array) {
				$data = array();
				while ($row = mysqli_fetch_assoc($result)) {
					$data[] = $row;
				}
				return $data;
			}

			return $result;
		}
	}

	if (!function_exists('getDateRange')) {
		function getDateRange($startDate, $endDate, $format="Y-m-d")
		{
			//Create output variable
			$datesArray = array();
			//Calculate date range
			$total_days = round(abs(strtotime($endDate) - strtotime($startDate)) / 86400, 0) + 1;
			if($total_days<0) { return false; }
			//Populate array of weekdays and counts
			for($day=0; $day<$total_days; $day++)
			{
				$datesArray[] = date($format, strtotime("{$startDate} + {$day} days"));
			}
			//Return results array
			return $datesArray;
		}
	}


?>