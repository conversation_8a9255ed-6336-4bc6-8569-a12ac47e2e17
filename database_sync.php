<?php
// Database Synchronization Tool
// Sync data between Local and Online databases
session_start();

// Database configurations
$local_config = array(
    'host' => 'localhost',
    'user' => 'root',
    'password' => '',
    'database' => 'biometrics'
);

$online_config = array(
    'host' => 'your-online-host.com',        // Replace with your online host
    'user' => 'your-online-username',        // Replace with your online username
    'password' => 'your-online-password',    // Replace with your online password
    'database' => 'your-online-database'     // Replace with your online database
);

// Handle AJAX requests
if (isset($_GET['action'])) {
    handleAjaxRequest($_GET['action']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_GET['action']) && $_GET['action'] === 'sync') {
    $input = json_decode(file_get_contents('php://input'), true);
    handleSyncRequest($input);
    exit;
}

// Database connection functions
function connectToDatabase($config) {
    $connection = mysql_connect($config['host'], $config['user'], $config['password']);
    if ($connection && mysql_select_db($config['database'], $connection)) {
        return $connection;
    }
    return false;
}

// Handle AJAX requests
function handleAjaxRequest($action) {
    global $local_config, $online_config;

    switch ($action) {
        case 'check_local':
            $conn = connectToDatabase($local_config);
            if ($conn) {
                mysql_close($conn);
                echo json_encode(['status' => true, 'message' => 'Local database connected']);
            } else {
                echo json_encode(['status' => false, 'message' => 'Failed to connect to local database']);
            }
            break;

        case 'check_online':
            $conn = connectToDatabase($online_config);
            if ($conn) {
                mysql_close($conn);
                echo json_encode(['status' => true, 'message' => 'Online database connected']);
            } else {
                echo json_encode(['status' => false, 'message' => 'Failed to connect to online database']);
            }
            break;

        case 'get_counts':
            $counts = getDataCounts();
            echo json_encode($counts);
            break;
    }
}

// Get data counts from both databases
function getDataCounts() {
    global $local_config, $online_config;

    $counts = [
        'local' => ['employees' => 0, 'attendance' => 0],
        'online' => ['employees' => 0, 'attendance' => 0]
    ];

    // Local counts
    $local_conn = connectToDatabase($local_config);
    if ($local_conn) {
        $result = mysql_query("SELECT COUNT(*) as count FROM tbl_personnel", $local_conn);
        if ($result) {
            $row = mysql_fetch_assoc($result);
            $counts['local']['employees'] = $row['count'];
        }

        $result = mysql_query("SELECT COUNT(*) as count FROM tbl_in_out", $local_conn);
        if ($result) {
            $row = mysql_fetch_assoc($result);
            $counts['local']['attendance'] = $row['count'];
        }
        mysql_close($local_conn);
    }

    // Online counts
    $online_conn = connectToDatabase($online_config);
    if ($online_conn) {
        $result = mysql_query("SELECT COUNT(*) as count FROM tbl_personnel", $online_conn);
        if ($result) {
            $row = mysql_fetch_assoc($result);
            $counts['online']['employees'] = $row['count'];
        }

        $result = mysql_query("SELECT COUNT(*) as count FROM tbl_in_out", $online_conn);
        if ($result) {
            $row = mysql_fetch_assoc($result);
            $counts['online']['attendance'] = $row['count'];
        }
        mysql_close($online_conn);
    }

    return $counts;
}

// Handle sync requests
function handleSyncRequest($input) {
    $type = $input['type'];
    $direction = $input['direction'];
    $options = $input['options'];

    $result = [
        'success' => false,
        'message' => '',
        'processed' => 0,
        'errors' => 0
    ];

    try {
        switch ($type) {
            case 'employees':
                $result = syncEmployeeData($direction, $options);
                break;
            case 'attendance':
                $result = syncAttendanceData($direction, $options);
                break;
            case 'full':
                $emp_result = syncEmployeeData($direction, $options);
                $att_result = syncAttendanceData($direction, $options);
                $result = [
                    'success' => $emp_result['success'] && $att_result['success'],
                    'message' => 'Employees: ' . $emp_result['message'] . ' | Attendance: ' . $att_result['message'],
                    'processed' => $emp_result['processed'] + $att_result['processed'],
                    'errors' => $emp_result['errors'] + $att_result['errors']
                ];
                break;
        }
    } catch (Exception $e) {
        $result['message'] = 'Sync failed: ' . $e->getMessage();
    }

    echo json_encode($result);
}

// Sync employee data
function syncEmployeeData($direction, $options) {
    global $local_config, $online_config;

    $processed = 0;
    $errors = 0;

    if ($direction === 'local_to_online') {
        $source_conn = connectToDatabase($local_config);
        $target_conn = connectToDatabase($online_config);
    } else {
        $source_conn = connectToDatabase($online_config);
        $target_conn = connectToDatabase($local_config);
    }

    if (!$source_conn || !$target_conn) {
        return ['success' => false, 'message' => 'Database connection failed', 'processed' => 0, 'errors' => 1];
    }

    // Get all employees from source
    $sql = "SELECT * FROM tbl_personnel ORDER BY AccessID";
    $result = mysql_query($sql, $source_conn);

    if (!$result) {
        return ['success' => false, 'message' => 'Failed to fetch employee data', 'processed' => 0, 'errors' => 1];
    }

    while ($employee = mysql_fetch_assoc($result)) {
        // Check if employee exists in target
        $check_sql = "SELECT COUNT(*) as count FROM tbl_personnel WHERE AccessID = '" . mysql_real_escape_string($employee['AccessID'], $target_conn) . "'";
        $check_result = mysql_query($check_sql, $target_conn);
        $exists = false;

        if ($check_result) {
            $check_row = mysql_fetch_assoc($check_result);
            $exists = $check_row['count'] > 0;
        }

        if (!$exists || $options['overwrite']) {
            if ($exists && $options['overwrite']) {
                // Update existing record
                $update_sql = "UPDATE tbl_personnel SET
                    EmployeeID = '" . mysql_real_escape_string($employee['EmployeeID'], $target_conn) . "',
                    FullName = '" . mysql_real_escape_string($employee['FullName'], $target_conn) . "',
                    Position = '" . mysql_real_escape_string($employee['Position'], $target_conn) . "',
                    AgencyCompany = '" . mysql_real_escape_string($employee['AgencyCompany'], $target_conn) . "',
                    ContactNo = '" . mysql_real_escape_string($employee['ContactNo'], $target_conn) . "',
                    DateHired = '" . mysql_real_escape_string($employee['DateHired'], $target_conn) . "',
                    Status = '" . mysql_real_escape_string($employee['Status'], $target_conn) . "',
                    TimeIN = '" . mysql_real_escape_string($employee['TimeIN'], $target_conn) . "',
                    TimeOut = '" . mysql_real_escape_string($employee['TimeOut'], $target_conn) . "'
                    WHERE AccessID = '" . mysql_real_escape_string($employee['AccessID'], $target_conn) . "'";

                if (mysql_query($update_sql, $target_conn)) {
                    $processed++;
                } else {
                    $errors++;
                }
            } else {
                // Insert new record
                $insert_sql = "INSERT INTO tbl_personnel (EmployeeID, AccessID, FullName, Position, AgencyCompany, ContactNo, DateHired, Status, TimeIN, TimeOut) VALUES (
                    '" . mysql_real_escape_string($employee['EmployeeID'], $target_conn) . "',
                    '" . mysql_real_escape_string($employee['AccessID'], $target_conn) . "',
                    '" . mysql_real_escape_string($employee['FullName'], $target_conn) . "',
                    '" . mysql_real_escape_string($employee['Position'], $target_conn) . "',
                    '" . mysql_real_escape_string($employee['AgencyCompany'], $target_conn) . "',
                    '" . mysql_real_escape_string($employee['ContactNo'], $target_conn) . "',
                    '" . mysql_real_escape_string($employee['DateHired'], $target_conn) . "',
                    '" . mysql_real_escape_string($employee['Status'], $target_conn) . "',
                    '" . mysql_real_escape_string($employee['TimeIN'], $target_conn) . "',
                    '" . mysql_real_escape_string($employee['TimeOut'], $target_conn) . "'
                )";

                if (mysql_query($insert_sql, $target_conn)) {
                    $processed++;
                } else {
                    $errors++;
                }
            }
        }
    }

    mysql_close($source_conn);
    mysql_close($target_conn);

    return [
        'success' => $errors === 0,
        'message' => "Processed $processed employees with $errors errors",
        'processed' => $processed,
        'errors' => $errors
    ];
}

// Sync attendance data
function syncAttendanceData($direction, $options) {
    global $local_config, $online_config;

    $processed = 0;
    $errors = 0;

    if ($direction === 'local_to_online') {
        $source_conn = connectToDatabase($local_config);
        $target_conn = connectToDatabase($online_config);
    } else {
        $source_conn = connectToDatabase($online_config);
        $target_conn = connectToDatabase($local_config);
    }

    if (!$source_conn || !$target_conn) {
        return ['success' => false, 'message' => 'Database connection failed', 'processed' => 0, 'errors' => 1];
    }

    // Get attendance records from source (limit to recent records for performance)
    $sql = "SELECT * FROM tbl_in_out ORDER BY TimeRecord DESC LIMIT 10000";
    $result = mysql_query($sql, $source_conn);

    if (!$result) {
        return ['success' => false, 'message' => 'Failed to fetch attendance data', 'processed' => 0, 'errors' => 1];
    }

    while ($attendance = mysql_fetch_assoc($result)) {
        // Check if record exists in target
        $check_sql = "SELECT COUNT(*) as count FROM tbl_in_out WHERE
            AccessID = '" . mysql_real_escape_string($attendance['AccessID'], $target_conn) . "' AND
            TimeRecord = '" . mysql_real_escape_string($attendance['TimeRecord'], $target_conn) . "' AND
            TimeFlag = '" . mysql_real_escape_string($attendance['TimeFlag'], $target_conn) . "'";
        $check_result = mysql_query($check_sql, $target_conn);
        $exists = false;

        if ($check_result) {
            $check_row = mysql_fetch_assoc($check_result);
            $exists = $check_row['count'] > 0;
        }

        if (!$exists || $options['overwrite']) {
            if (!$exists) {
                // Insert new record
                $insert_sql = "INSERT INTO tbl_in_out (AccessID, FullName, TimeRecord, TimeFlag, AccessArea) VALUES (
                    '" . mysql_real_escape_string($attendance['AccessID'], $target_conn) . "',
                    '" . mysql_real_escape_string($attendance['FullName'], $target_conn) . "',
                    '" . mysql_real_escape_string($attendance['TimeRecord'], $target_conn) . "',
                    '" . mysql_real_escape_string($attendance['TimeFlag'], $target_conn) . "',
                    '" . mysql_real_escape_string($attendance['AccessArea'], $target_conn) . "'
                )";

                if (mysql_query($insert_sql, $target_conn)) {
                    $processed++;
                } else {
                    $errors++;
                }
            }
        }
    }

    mysql_close($source_conn);
    mysql_close($target_conn);

    return [
        'success' => $errors === 0,
        'message' => "Processed $processed attendance records with $errors errors",
        'processed' => $processed,
        'errors' => $errors
    ];
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Synchronization Tool</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sync-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            margin: 20px auto;
            max-width: 1200px;
            padding: 30px;
        }
        .sync-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            margin: 15px 0;
            padding: 20px;
            transition: transform 0.3s ease;
        }
        .sync-card:hover {
            transform: translateY(-3px);
        }
        .btn-sync {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .btn-sync:hover {
            background: linear-gradient(135deg, #5a6fd8, #6a4190);
            transform: translateY(-2px);
            color: white;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-online { background-color: #28a745; }
        .status-offline { background-color: #dc3545; }
        .status-unknown { background-color: #ffc107; }
        .progress-container {
            display: none;
            margin: 15px 0;
        }
        .log-container {
            background: #f8f9fa;
            border-radius: 8px;
            max-height: 300px;
            overflow-y: auto;
            padding: 15px;
            margin: 15px 0;
        }
        .sync-direction {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 20px 0;
        }
        .db-box {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            width: 200px;
        }
        .sync-arrow {
            font-size: 2rem;
            color: #667eea;
            margin: 0 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="sync-container">
            <div class="text-center mb-4">
                <h1><i class="fas fa-sync-alt me-3"></i>Database Synchronization Tool</h1>
                <p class="text-muted">Sync employee and attendance data between Local and Online databases</p>
            </div>

            <!-- Connection Status -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="sync-card">
                        <h5><i class="fas fa-database me-2"></i>Local Database</h5>
                        <p><span class="status-indicator" id="local-status"></span><span id="local-status-text">Checking...</span></p>
                        <small class="text-muted">Host: <?php echo $local_config['host']; ?> | DB: <?php echo $local_config['database']; ?></small>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="sync-card">
                        <h5><i class="fas fa-cloud-database me-2"></i>Online Database</h5>
                        <p><span class="status-indicator" id="online-status"></span><span id="online-status-text">Checking...</span></p>
                        <small class="text-muted">Host: <?php echo $online_config['host']; ?> | DB: <?php echo $online_config['database']; ?></small>
                    </div>
                </div>
            </div>

            <!-- Sync Options -->
            <div class="sync-card">
                <h4><i class="fas fa-exchange-alt me-2"></i>Synchronization Options</h4>
                
                <!-- Employee Data Sync -->
                <div class="sync-direction">
                    <div class="db-box">
                        <i class="fas fa-desktop fa-2x mb-2"></i>
                        <h6>Local Database</h6>
                        <small>Employee Records: <span id="local-employees">-</span></small>
                    </div>
                    <div class="text-center">
                        <div class="btn-group-vertical">
                            <button class="btn btn-sync btn-sm mb-2" onclick="syncEmployees('local_to_online')">
                                <i class="fas fa-arrow-right me-1"></i> Sync to Online
                            </button>
                            <button class="btn btn-sync btn-sm" onclick="syncEmployees('online_to_local')">
                                <i class="fas fa-arrow-left me-1"></i> Sync from Online
                            </button>
                        </div>
                        <div class="mt-2">
                            <small class="text-muted">Employee Data</small>
                        </div>
                    </div>
                    <div class="db-box">
                        <i class="fas fa-cloud fa-2x mb-2"></i>
                        <h6>Online Database</h6>
                        <small>Employee Records: <span id="online-employees">-</span></small>
                    </div>
                </div>

                <!-- Attendance Data Sync -->
                <div class="sync-direction">
                    <div class="db-box">
                        <i class="fas fa-desktop fa-2x mb-2"></i>
                        <h6>Local Database</h6>
                        <small>Attendance Records: <span id="local-attendance">-</span></small>
                    </div>
                    <div class="text-center">
                        <div class="btn-group-vertical">
                            <button class="btn btn-sync btn-sm mb-2" onclick="syncAttendance('local_to_online')">
                                <i class="fas fa-arrow-right me-1"></i> Sync to Online
                            </button>
                            <button class="btn btn-sync btn-sm" onclick="syncAttendance('online_to_local')">
                                <i class="fas fa-arrow-left me-1"></i> Sync from Online
                            </button>
                        </div>
                        <div class="mt-2">
                            <small class="text-muted">Attendance Data</small>
                        </div>
                    </div>
                    <div class="db-box">
                        <i class="fas fa-cloud fa-2x mb-2"></i>
                        <h6>Online Database</h6>
                        <small>Attendance Records: <span id="online-attendance">-</span></small>
                    </div>
                </div>

                <!-- Full Sync Options -->
                <div class="text-center mt-4">
                    <h5>Complete Synchronization</h5>
                    <div class="btn-group">
                        <button class="btn btn-sync" onclick="fullSync('local_to_online')">
                            <i class="fas fa-upload me-2"></i>Full Sync: Local → Online
                        </button>
                        <button class="btn btn-sync" onclick="fullSync('online_to_local')">
                            <i class="fas fa-download me-2"></i>Full Sync: Online → Local
                        </button>
                    </div>
                </div>
            </div>

            <!-- Progress Bar -->
            <div class="progress-container">
                <div class="sync-card">
                    <h5>Synchronization Progress</h5>
                    <div class="progress mb-3">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" 
                             role="progressbar" style="width: 0%" id="sync-progress"></div>
                    </div>
                    <div id="sync-status">Ready to sync...</div>
                </div>
            </div>

            <!-- Sync Log -->
            <div class="sync-card">
                <h5><i class="fas fa-list-alt me-2"></i>Synchronization Log</h5>
                <div class="log-container" id="sync-log">
                    <p class="text-muted">Sync operations will be logged here...</p>
                </div>
                <button class="btn btn-outline-secondary btn-sm" onclick="clearLog()">
                    <i class="fas fa-trash me-1"></i>Clear Log
                </button>
            </div>

            <!-- Advanced Options -->
            <div class="sync-card">
                <h5><i class="fas fa-cogs me-2"></i>Advanced Options</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="backup-before-sync" checked>
                            <label class="form-check-label" for="backup-before-sync">
                                Create backup before sync
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="overwrite-existing" checked>
                            <label class="form-check-label" for="overwrite-existing">
                                Overwrite existing records
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="sync-only-new">
                            <label class="form-check-label" for="sync-only-new">
                                Sync only new records
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="verify-after-sync" checked>
                            <label class="form-check-label" for="verify-after-sync">
                                Verify data after sync
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation -->
            <div class="text-center mt-4">
                <a href="index.php" class="btn btn-outline-primary me-2">
                    <i class="fas fa-home me-1"></i>Home
                </a>
                <a href="reports_dashboard.php" class="btn btn-outline-primary">
                    <i class="fas fa-chart-bar me-1"></i>Reports
                </a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            checkConnections();
            loadDataCounts();
        });

        // Check database connections
        function checkConnections() {
            // Check local database
            fetch('database_sync.php?action=check_local')
                .then(response => response.json())
                .then(data => {
                    updateConnectionStatus('local', data.status, data.message);
                })
                .catch(error => {
                    updateConnectionStatus('local', false, 'Connection failed');
                });

            // Check online database
            fetch('database_sync.php?action=check_online')
                .then(response => response.json())
                .then(data => {
                    updateConnectionStatus('online', data.status, data.message);
                })
                .catch(error => {
                    updateConnectionStatus('online', false, 'Connection failed');
                });
        }

        // Update connection status indicators
        function updateConnectionStatus(type, status, message) {
            const statusElement = document.getElementById(type + '-status');
            const textElement = document.getElementById(type + '-status-text');
            
            if (status) {
                statusElement.className = 'status-indicator status-online';
                textElement.textContent = 'Connected - ' + message;
            } else {
                statusElement.className = 'status-indicator status-offline';
                textElement.textContent = 'Disconnected - ' + message;
            }
        }

        // Load data counts
        function loadDataCounts() {
            fetch('database_sync.php?action=get_counts')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('local-employees').textContent = data.local.employees || '0';
                    document.getElementById('local-attendance').textContent = data.local.attendance || '0';
                    document.getElementById('online-employees').textContent = data.online.employees || '0';
                    document.getElementById('online-attendance').textContent = data.online.attendance || '0';
                });
        }

        // Sync functions
        function syncEmployees(direction) {
            startSync('employees', direction);
        }

        function syncAttendance(direction) {
            startSync('attendance', direction);
        }

        function fullSync(direction) {
            if (confirm('Are you sure you want to perform a full synchronization?\nThis will sync both employees and attendance data.')) {
                startSync('full', direction);
            }
        }

        // Start synchronization process
        function startSync(type, direction) {
            const options = {
                backup: document.getElementById('backup-before-sync').checked,
                overwrite: document.getElementById('overwrite-existing').checked,
                onlyNew: document.getElementById('sync-only-new').checked,
                verify: document.getElementById('verify-after-sync').checked
            };

            showProgress(true);
            logMessage('Starting ' + type + ' sync: ' + direction.replace('_', ' → '));

            fetch('database_sync.php?action=sync', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    type: type,
                    direction: direction,
                    options: options
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    logMessage('✅ Sync completed successfully!');
                    logMessage('Records processed: ' + data.processed);
                    if (data.errors > 0) {
                        logMessage('⚠️ Errors encountered: ' + data.errors);
                    }
                } else {
                    logMessage('❌ Sync failed: ' + data.message);
                }
                showProgress(false);
                loadDataCounts(); // Refresh counts
            })
            .catch(error => {
                logMessage('❌ Sync error: ' + error.message);
                showProgress(false);
            });
        }

        // Show/hide progress bar
        function showProgress(show) {
            const container = document.querySelector('.progress-container');
            if (show) {
                container.style.display = 'block';
                updateProgress(0, 'Initializing...');
            } else {
                container.style.display = 'none';
            }
        }

        // Update progress bar
        function updateProgress(percent, status) {
            document.getElementById('sync-progress').style.width = percent + '%';
            document.getElementById('sync-status').textContent = status;
        }

        // Log message
        function logMessage(message) {
            const log = document.getElementById('sync-log');
            const timestamp = new Date().toLocaleTimeString();
            log.innerHTML += '<div>[' + timestamp + '] ' + message + '</div>';
            log.scrollTop = log.scrollHeight;
        }

        // Clear log
        function clearLog() {
            document.getElementById('sync-log').innerHTML = '<p class="text-muted">Sync operations will be logged here...</p>';
        }
    </script>
</body>
</html>
