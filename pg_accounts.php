
<?php
include('auth.php');
require_once('include/include-head.php');
require_once('include/include-main.php');
require_once('proc/config.php');
/*require_once('css/styles.css'); This is only set just to automatically see immediately the file :D */

// Get current branch information
$current_branch_id = isset($_SESSION['CURRENT_BRANCH_ID']) ? $_SESSION['CURRENT_BRANCH_ID'] : 1;

// Default branch data
$current_branch = array(
    'branch_id' => $current_branch_id,
    'branch_name' => 'Main Branch',
    'branch_code' => 'MAIN'
);

// Check if branches table exists
$check_branches_table = mysql_query("SHOW TABLES LIKE 'tbl_branches'");
if($check_branches_table && mysql_num_rows($check_branches_table) > 0) {
    // Table exists, get current branch details
    $branch_sql = "SELECT * FROM tbl_branches WHERE branch_id = '$current_branch_id' LIMIT 1";
    $branch_result = mysql_query($branch_sql);
    if($branch_result && mysql_num_rows($branch_result) > 0) {
        $current_branch = mysql_fetch_assoc($branch_result);
    }

    // Get all branches for dropdown
    $branches = array();
    $branches_sql = "SELECT * FROM tbl_branches ORDER BY branch_name";
    $branches_result = mysql_query($branches_sql);
    if($branches_result && mysql_num_rows($branches_result) > 0) {
        while($branch_row = mysql_fetch_assoc($branches_result)) {
            $branches[] = $branch_row;
        }
    } else {
        // No branches found, use default
        $branches = array($current_branch);
    }
} else {
    // Table doesn't exist, use default branch
    $branches = array($current_branch);
}

if(isset($_POST['biometricsID'])){

    $biometricsID = clean($_POST['biometricsID']);
    $employeeID = clean($_POST['employeeid']);
    $fullname = clean($_POST['fullname']);
    $datehired = clean($_POST['datehired']);
    $position = clean($_POST['position']);
    $phone = clean($_POST['phone']);
    $address = clean($_POST['address']);
    // $company = clean($_POST['company']);
    $project = clean($_POST['project']);
    $timein = clean($_POST['timein']);
    $timeout = clean($_POST['timeout']);
    $schedule = clean($_POST['txtSchedule']);
    // Use current branch from session instead of form input
    $branch_id = isset($_SESSION['CURRENT_BRANCH_ID']) ? $_SESSION['CURRENT_BRANCH_ID'] : 1;

    // Check for duplicate Employee ID or Biometric ID before inserting
    $checkDuplicate = mysql_query("SELECT COUNT(*) as count FROM tbl_personnel WHERE EmployeeID='$employeeID' OR AccessID='$biometricsID'");
    $duplicateRow = mysql_fetch_assoc($checkDuplicate);

    if($duplicateRow['count'] > 0) {
        // IDs already exist, generate new ones
        include('function/generateEmployeeIDs.php');
        $biometricsID = generateUniqueBiometricID();
        $employeeID = generateUniqueEmployeeID();
    }

    // Check if Address column exists before including it
    $checkColumn = mysql_query("SHOW COLUMNS FROM tbl_personnel LIKE 'Address'");
    $hasAddressColumn = mysql_num_rows($checkColumn) > 0;

    if($hasAddressColumn) {
        $sql = "INSERT INTO tbl_personnel (EmployeeID,AccessID,Fullname,DateHired,Position,ProjectAssigned,branch_id,TimeIN,TimeOut,schedule_dates,ContactNo,Address) VALUES('$employeeID','$biometricsID','$fullname','$datehired','$position','$project','$branch_id','$timein','$timeout','$schedule','$phone','$address')";
    } else {
        $sql = "INSERT INTO tbl_personnel (EmployeeID,AccessID,Fullname,DateHired,Position,ProjectAssigned,branch_id,TimeIN,TimeOut,schedule_dates,ContactNo) VALUES('$employeeID','$biometricsID','$fullname','$datehired','$position','$project','$branch_id','$timein','$timeout','$schedule','$phone')";
    }

    $res = mysql_query($sql);

    if(!$res) {
        // If still fails, try one more time with fresh IDs
        include('function/generateEmployeeIDs.php');
        $biometricsID = generateUniqueBiometricID();
        $employeeID = generateUniqueEmployeeID();

        if($hasAddressColumn) {
            $sql = "INSERT INTO tbl_personnel (EmployeeID,AccessID,Fullname,DateHired,Position,AgencyCompany,ProjectAssigned,branch_id,TimeIN,TimeOut,schedule_dates,ContactNo,Address) VALUES('$employeeID','$biometricsID','$fullname','$datehired','$position','$company','$project','$branch_id','$timein','$timeout','$schedule','$phone','$address')";
        } else {
            $sql = "INSERT INTO tbl_personnel (EmployeeID,AccessID,Fullname,DateHired,Position,AgencyCompany,ProjectAssigned,branch_id,TimeIN,TimeOut,schedule_dates,ContactNo) VALUES('$employeeID','$biometricsID','$fullname','$datehired','$position','$company','$project','$branch_id','$timein','$timeout','$schedule','$phone')";
        }

        $res = mysql_query($sql) or die("Failed to insert employee after retry: " . mysql_error());
    }
    if($res){}

}

?>

<style>
	.chosen-select{
		width: 100% !important;
	}

	/* Modern Card Design */
	.accounts-card {
		border-radius: 12px;
		box-shadow: 0 4px 20px rgba(0,0,0,0.08);
		border: none;
		background: #ffffff;
		margin-bottom: 30px;
	}

	.accounts-header {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: white;
		border-radius: 12px 12px 0 0;
		padding: 20px 25px;
		border: none;
	}

	.accounts-header h4 {
		margin: 0;
		font-weight: 600;
		font-size: 18px;
		display: flex;
		align-items: center;
		gap: 10px;
	}

	.accounts-body {
		padding: 25px;
		background: #ffffff;
	}

	/* Action Buttons */
	.action-buttons {
		margin-bottom: 25px;
		display: flex;
		justify-content: space-between;
		align-items: center;
		flex-wrap: wrap;
		gap: 15px;
	}

	.btn-modern {
		border-radius: 8px;
		padding: 10px 20px;
		font-weight: 500;
		border: none;
		transition: all 0.3s ease;
		display: inline-flex;
		align-items: center;
		gap: 8px;
	}

	.btn-primary-modern {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: white;
	}

	.btn-primary-modern:hover {
		transform: translateY(-2px);
		box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
		color: white;
	}

	.btn-success-modern {
		background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
		color: white;
	}

	.btn-success-modern:hover {
		transform: translateY(-2px);
		box-shadow: 0 6px 20px rgba(17, 153, 142, 0.4);
		color: white;
	}

	.btn-info-modern {
		background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%);
		color: white;
	}

	.btn-info-modern:hover {
		transform: translateY(-2px);
		box-shadow: 0 6px 20px rgba(23, 162, 184, 0.4);
		color: white;
	}

	/* Table Styling */
	.modern-table {
		border-radius: 8px;
		overflow: hidden;
		box-shadow: 0 2px 10px rgba(0,0,0,0.05);
	}

	.modern-table thead th {
		background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
		color: #495057;
		font-weight: 600;
		text-transform: uppercase;
		font-size: 12px;
		letter-spacing: 0.5px;
		padding: 15px 12px;
		border: none;
	}

	.modern-table tbody tr {
		transition: all 0.2s ease;
	}

	.modern-table tbody tr:hover {
		background-color: #f8f9ff;
		transform: scale(1.01);
	}

	.modern-table tbody td {
		padding: 12px;
		vertical-align: middle;
		border-color: #f1f3f4;
	}

	/* Search and Filter */
	.search-filter-section {
		background: #f8f9fa;
		padding: 20px;
		border-radius: 8px;
		margin-bottom: 20px;
	}

	.search-input {
		border-radius: 25px;
		border: 2px solid #e9ecef;
		padding: 10px 20px;
		transition: all 0.3s ease;
	}

	.search-input:focus {
		border-color: #667eea;
		box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
	}

	/* Stats Cards */
	.stats-container {
		display: flex;
		gap: 20px;
		margin-bottom: 25px;
		flex-wrap: wrap;
	}

	.stat-card {
		flex: 1;
		min-width: 200px;
		background: white;
		border-radius: 12px;
		padding: 20px;
		box-shadow: 0 2px 10px rgba(0,0,0,0.05);
		border-left: 4px solid #667eea;
	}

	.stat-number {
		font-size: 28px;
		font-weight: 700;
		color: #667eea;
		margin: 0;
	}

	.stat-label {
		color: #6c757d;
		font-size: 14px;
		margin: 5px 0 0 0;
	}

	/* Modal Steps Styling */
	.progress-steps {
		display: flex;
		justify-content: center;
		align-items: center;
		margin-bottom: 20px;
		position: relative;
	}

	.progress-steps::before {
		content: '';
		position: absolute;
		top: 20px;
		left: 25%;
		right: 25%;
		height: 2px;
		background: #e9ecef;
		z-index: 1;
	}

	.step {
		display: flex;
		flex-direction: column;
		align-items: center;
		position: relative;
		z-index: 2;
		flex: 1;
		max-width: 120px;
	}

	.step-circle {
		width: 40px;
		height: 40px;
		border-radius: 50%;
		background: #e9ecef;
		color: #6c757d;
		display: flex;
		align-items: center;
		justify-content: center;
		font-weight: 600;
		margin-bottom: 8px;
		transition: all 0.3s ease;
	}

	.step.active .step-circle {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: white;
	}

	.step.completed .step-circle {
		background: #28a745;
		color: white;
	}

	.step-label {
		font-size: 12px;
		color: #6c757d;
		text-align: center;
		font-weight: 500;
	}

	.step.active .step-label {
		color: #667eea;
		font-weight: 600;
	}

	/* Form Steps */
	.form-step {
		display: none;
		animation: fadeIn 0.3s ease-in;
	}

	.form-step.active {
		display: block;
	}

	@keyframes fadeIn {
		from { opacity: 0; transform: translateY(10px); }
		to { opacity: 1; transform: translateY(0); }
	}

	.step-title {
		color: #495057;
		font-weight: 600;
		margin-bottom: 20px;
		padding-bottom: 10px;
		border-bottom: 2px solid #f8f9fa;
		display: flex;
		align-items: center;
		gap: 10px;
	}

	/* Modern Form Inputs */
	.form-label {
		font-weight: 600;
		color: #495057;
		margin-bottom: 8px;
		display: flex;
		align-items: center;
		gap: 8px;
	}

	.modern-input, .modern-select {
		border: 2px solid #e9ecef;
		border-radius: 8px;
		padding: 12px 15px;
		transition: all 0.3s ease;
		font-size: 14px;
		width: 100%;
		height: auto;
		min-height: 45px;
		box-sizing: border-box;
	}

	.modern-input:focus, .modern-select:focus {
		border-color: #667eea;
		box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
		outline: none;
	}

	/* Fix for select dropdown display */
	.modern-select {
		appearance: none;
		-webkit-appearance: none;
		-moz-appearance: none;
		background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
		background-repeat: no-repeat;
		background-position: right 12px center;
		background-size: 16px;
		padding-right: 40px;
	}

	/* Ensure form groups have proper spacing */
	.form-group {
		margin-bottom: 20px;
		position: relative;
	}

	.form-group select {
		width: 100% !important;
		max-width: 100%;
	}

	.form-text {
		font-size: 12px;
		margin-top: 5px;
	}

	/* Step Navigation */
	.step-navigation {
		display: flex;
		justify-content: space-between;
		align-items: center;
		width: 100%;
	}

	.step-navigation .btn {
		min-width: 120px;
	}

	/* Table Content Styling */
	.employee-info {
		display: flex;
		flex-direction: column;
	}

	.employee-name {
		font-weight: 600;
		color: #495057;
		font-size: 14px;
	}

	.badge {
		padding: 6px 12px;
		border-radius: 20px;
		font-size: 11px;
		font-weight: 600;
		text-transform: uppercase;
		letter-spacing: 0.5px;
	}

	.badge-primary {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: white;
	}

	.date-badge, .position-badge, .company-badge {
		display: inline-block;
		padding: 4px 8px;
		border-radius: 12px;
		font-size: 12px;
		font-weight: 500;
	}

	.date-badge {
		background: #e3f2fd;
		color: #1976d2;
	}

	.position-badge {
		background: #f3e5f5;
		color: #7b1fa2;
	}

	.company-badge {
		background: #e8f5e8;
		color: #388e3c;
	}

	.branch-badge {
		background: #fff3e0;
		color: #f57c00;
	}

	/* Branch Select Specific Styling */
	#branchSelect, #editBranchSelect {
		width: 100% !important;
		height: 45px !important;
		padding: 10px 15px !important;
		border: 2px solid #e9ecef !important;
		border-radius: 8px !important;
		font-size: 14px !important;
		background-color: white !important;
		box-sizing: border-box !important;
	}

	#branchSelect:focus, #editBranchSelect:focus {
		border-color: #667eea !important;
		box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25) !important;
		outline: none !important;
	}

	/* Override any conflicting styles */
	.modal-body select {
		width: 100% !important;
		max-width: 100% !important;
		min-width: 100% !important;
	}

	/* Ensure modal columns have proper width */
	.modal-body .col-md-6 {
		padding-left: 15px;
		padding-right: 15px;
	}

	.modal-body .row {
		margin-left: -15px;
		margin-right: -15px;
	}

	/* Fix for any overflow issues */
	.modal-body {
		overflow-x: visible;
	}

	.form-step {
		overflow-x: visible;
	}

	.action-buttons {
		display: flex;
		gap: 5px;
		justify-content: center;
	}

	.action-buttons .btn {
		border-radius: 6px;
		padding: 4px 8px;
		border: none;
		transition: all 0.2s ease;
	}

	.action-buttons .btn:hover {
		transform: translateY(-1px);
		box-shadow: 0 2px 8px rgba(0,0,0,0.15);
	}

	/* Responsive Design */
	@media (max-width: 768px) {
		.stats-container {
			flex-direction: column;
		}

		.action-buttons {
			flex-direction: column;
			gap: 10px;
		}

		.search-section {
			width: 100%;
			margin-top: 15px;
		}

		.search-input {
			width: 100%;
		}
	}

	/* Loading States */
	.loading {
		opacity: 0.6;
		pointer-events: none;
	}

	.spinner {
		display: inline-block;
		width: 20px;
		height: 20px;
		border: 3px solid rgba(255,255,255,.3);
		border-radius: 50%;
		border-top-color: #fff;
		animation: spin 1s ease-in-out infinite;
	}

	@keyframes spin {
		to { transform: rotate(360deg); }
	}

	/* Form Validation Styles */
	.is-invalid {
		border-color: #dc3545 !important;
		box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
	}

	.is-valid {
		border-color: #28a745 !important;
		box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
	}

	.invalid-feedback {
		display: block;
		width: 100%;
		margin-top: 0.25rem;
		font-size: 0.875em;
		color: #dc3545;
	}

	.valid-feedback {
		display: block;
		width: 100%;
		margin-top: 0.25rem;
		font-size: 0.875em;
		color: #28a745;
	}

	/* Notification Styles */
	.alert {
		border-radius: 8px;
		box-shadow: 0 4px 12px rgba(0,0,0,0.15);
		border: none;
	}

	.alert-success {
		background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
		color: white;
	}

	.alert-danger {
		background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
		color: white;
	}

	.alert-info {
		background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%);
		color: white;
	}

	.alert .close {
		color: white;
		opacity: 0.8;
		text-shadow: none;
	}

	.alert .close:hover {
		opacity: 1;
	}

	/* Input Group Styling */
	.input-group {
		display: flex;
		width: 100%;
	}

	.input-group .form-control {
		flex: 1;
		border-top-right-radius: 0;
		border-bottom-right-radius: 0;
	}

	.input-group-addon {
		display: flex;
		align-items: center;
		padding: 0;
		border: 2px solid #e9ecef;
		border-left: none;
		border-radius: 0 8px 8px 0;
		background: #f8f9fa;
	}

	.input-group-addon .btn {
		border: none;
		border-radius: 0;
		background: transparent;
		color: #667eea;
		padding: 8px 12px;
		margin: 0;
	}

	.input-group-addon .btn:hover {
		background: #667eea;
		color: white;
	}

	/* Read-only input styling */
	.form-control[readonly] {
		background-color: #f8f9fa;
		border-color: #e9ecef;
		color: #6c757d;
		cursor: not-allowed;
	}

	/* Fix checkbox styling issues */
	.checkbox-container input[type="checkbox"] {
		-webkit-appearance: checkbox !important;
		-moz-appearance: checkbox !important;
		appearance: checkbox !important;
		width: auto !important;
		height: auto !important;
		margin: 0 8px 0 0 !important;
		position: relative !important;
		opacity: 1 !important;
		visibility: visible !important;
		display: inline-block !important;
		cursor: pointer !important;
	}

	.checkbox-label {
		cursor: pointer !important;
		user-select: none;
		display: flex !important;
		align-items: center !important;
	}

	.checkbox-label:hover {
		background-color: #f8f9fa;
		border-radius: 4px;
	}

	/* Override any conflicting Bootstrap checkbox styles */
	#BulkImportModal .checkbox input[type="checkbox"] {
		position: relative !important;
		margin-left: 0 !important;
		margin-right: 8px !important;
		opacity: 1 !important;
		z-index: 1 !important;
	}

	#BulkImportModal .checkbox label {
		padding-left: 0 !important;
		cursor: pointer !important;
	}
</style>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-inverse navbar-fixed-top" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-collapse">
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="dashboard_enhanced.php">
                    <i class="fa fa-fingerprint"></i> Biometric Access Control System
                </a>
            </div>
            <div class="navbar-collapse collapse">
                <ul class="nav navbar-nav navbar-right">
                    <!-- Current Branch Display -->
                    <li class="navbar-text" style="color: #fff; padding: 15px;">
                        <i class="fa fa-building"></i> <?php echo $current_branch['branch_name']; ?>
                    </li>
                    <li><a href="#" id="refresh-data"><i class="fa fa-refresh"></i> Refresh</a></li>
                    <?php if($_SESSION['ACCESSLEVEL']=='Admin') { ?>
                        <li><a href="branch_management.php"><i class="fa fa-building"></i> Branches</a></li>
                        <li><a href="pg_accounts.php"><i class="fa fa-users"></i> Employees</a></li>
                        <li><a href="biometrics_logs.php"><i class="fa fa-clock-o"></i> Attendance</a></li>
                        <li><a href="reports_dashboard.php"><i class="fa fa-bar-chart"></i> Reports</a></li>
                        <li><a href="pg_settings.php"><i class="fa fa-cog"></i> Settings</a></li>
                        <li><a href="logout.php"><i class="fa fa-sign-out"></i> Logout</a></li>
                    <?php } ?>
                </ul>
            </div>
        </div>
    </nav>
    <div class="col col-md-12">
        <!-- Stats Cards -->
        <div class="stats-container">
            <div class="stat-card">
                <h3 class="stat-number" id="totalEmployees">0</h3>
                <p class="stat-label">Total Employees</p>
            </div>
            <div class="stat-card">
                <h3 class="stat-number" id="activeEmployees">0</h3>
                <p class="stat-label">Active This Month</p>
            </div>
            <div class="stat-card">
                <h3 class="stat-number" id="newEmployees">0</h3>
                <p class="stat-label">New This Month</p>
            </div>
        </div>

        <!-- Main Accounts Card -->
        <div class="panel accounts-card">
            <div class="panel-heading accounts-header">
                <h4><i class="fa fa-users"></i> Employee Accounts Management</h4>
            </div>
            <div class="panel-body accounts-body">
                <!-- Action Buttons -->
                <div class="action-buttons">
                    <div>
                        <button class="btn btn-modern btn-primary-modern" id="showAddModal">
                            <i class="fa fa-plus"></i> Add New Employee
                        </button>
                        <button class="btn btn-modern btn-info-modern" id="showBulkImportModal">
                            <i class="fa fa-upload"></i> Bulk Import from Excel
                        </button>
                        <button class="btn btn-modern btn-success-modern" id="exportBtn">
                            <i class="fa fa-download"></i> Export Data
                        </button>
                        <button class="btn btn-modern btn-primary-modern" id="testEditBtn" onclick="testEditFunction()">
                            <i class="fa fa-test"></i> Test Edit
                        </button>
                    </div>
                    <div class="search-section">
                        <input type="text" class="form-control search-input" id="globalSearch" placeholder="Search employees...">
                    </div>
                </div>

                <!-- Enhanced Table -->
                <div class="table-responsive">
                    <table class="table table-striped modern-table" id="accounts">
                        <thead>
                            <tr>
                                <th><i class="fa fa-fingerprint"></i> Biometric ID</th>
                                <th><i class="fa fa-id-card"></i> Employee ID</th>
                                <th><i class="fa fa-user"></i> Full Name</th>
                                <th><i class="fa fa-calendar"></i> Date Hired</th>
                                <th><i class="fa fa-briefcase"></i> Position</th>
                                <th><i class="fa fa-building"></i> Branch</th>
                                <th><i class="fa fa-cogs"></i> Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Data will be loaded via DataTables -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modern Add Employee Modal -->
<div class="modal fade" role="dialog" id="AddModal">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="border-radius: 15px; border: none; box-shadow: 0 10px 40px rgba(0,0,0,0.15);">
            <!-- Modal Header -->
            <div class="modal-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 15px 15px 0 0; padding: 20px 30px;">
                <h4 class="modal-title" style="margin: 0; font-weight: 600;">
                    <i class="fa fa-user-plus"></i> Add New Employee
                </h4>
                <button type="button" class="close" data-dismiss="modal" style="color: white; opacity: 0.8; font-size: 24px;">
                    <span>&times;</span>
                </button>
            </div>

            <!-- Progress Steps -->
            <div class="modal-progress" style="padding: 20px 30px 0; background: #f8f9fa;">
                <div class="progress-steps">
                    <div class="step active" data-step="1">
                        <div class="step-circle">1</div>
                        <div class="step-label">Basic Info</div>
                    </div>
                    <div class="step" data-step="2">
                        <div class="step-circle">2</div>
                        <div class="step-label">Work Details</div>
                    </div>
                    <div class="step" data-step="3">
                        <div class="step-circle">3</div>
                        <div class="step-label">Schedule & Access</div>
                    </div>
                </div>
            </div>

            <div class="modal-body" style="padding: 30px;">
                <form class="form-horizontal" action="" method="POST" id="employeeForm">

                    <!-- Step 1: Basic Information -->
                    <div class="form-step active" id="step1">
                        <h5 class="step-title"><i class="fa fa-user"></i> Basic Information</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label"><i class="fa fa-fingerprint"></i> Biometric ID *</label>
                                    <div class="input-group">
                                        <input type="text" name="biometricsID" id="biometricsID" class="form-control modern-input" required readonly placeholder="Auto-generated">
                                        <div class="input-group-addon">
                                            <button type="button" class="btn btn-sm btn-info" id="generateBiometricID" title="Generate New ID">
                                                <i class="fa fa-refresh"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <small class="form-text text-muted">Auto-generated unique identifier (e.g., B001, B002...)</small>
                                </div>

                                <div class="form-group">
                                    <label class="form-label"><i class="fa fa-id-card"></i> Employee ID *</label>
                                    <div class="input-group">
                                        <input type="text" name="employeeid" id="employeeid" class="form-control modern-input" required readonly placeholder="Auto-generated">
                                        <div class="input-group-addon">
                                            <button type="button" class="btn btn-sm btn-info" id="generateEmployeeID" title="Generate New ID">
                                                <i class="fa fa-refresh"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <small class="form-text text-muted">Auto-generated unique identifier (e.g., 1001, 1002...)</small>
                                </div>

                                <div class="form-group">
                                    <label class="form-label"><i class="fa fa-user"></i> Full Name *</label>
                                    <input type="text" name="fullname" class="form-control modern-input" required placeholder="Enter full name">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label"><i class="fa fa-phone"></i> Phone Number</label>
                                    <input type="text" name="phone" class="form-control modern-input" placeholder="Enter phone number">
                                </div>

                                <div class="form-group">
                                    <label class="form-label"><i class="fa fa-calendar"></i> Date Hired *</label>
                                    <input type="date" name="datehired" class="form-control modern-input" required>
                                </div>

                                <div class="form-group">
                                    <label class="form-label"><i class="fa fa-map-marker"></i> Address</label>
                                    <input type="text" name="address" class="form-control modern-input" placeholder="Enter address">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 2: Work Details -->
                    <div class="form-step" id="step2">
                        <h5 class="step-title"><i class="fa fa-briefcase"></i> Work Details</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label"><i class="fa fa-briefcase"></i> Position *</label>
                                    <input type="text" name="position" class="form-control modern-input" required placeholder="Enter position/job title">
                                </div>

                                <div class="form-group">
                                    <label class="form-label"><i class="fa fa-building"></i> Company</label>
                                    <input type="text" name="company" class="form-control modern-input" placeholder="Enter company name">
                                </div>

                                <div class="form-group">
                                    <label class="form-label"><i class="fa fa-project-diagram"></i> Project</label>
                                    <input type="text" name="project" class="form-control modern-input" placeholder="Enter project assignment">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label"><i class="fa fa-building"></i> Branch</label>
                                    <div style="width: 100%; position: relative;">
                                        <input type="text" class="form-control" value="<?php echo $current_branch['branch_name']; ?>" readonly style="width: 100%; height: 45px; padding: 10px 15px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 14px; background-color: #f8f9fa; color: #6c757d;">
                                        <input type="hidden" name="branch_id" value="<?php echo $current_branch['branch_id']; ?>">
                                    </div>
                                    <small class="form-text text-muted">Employee will be assigned to your current branch</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 3: Schedule & Access -->
                    <div class="form-step" id="step3">
                        <h5 class="step-title"><i class="fa fa-clock-o"></i> Schedule & Access</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label"><i class="fa fa-clock-o"></i> Time In</label>
                                    <input type="time" name="timein" class="form-control modern-input">
                                    <small class="form-text text-muted">Default check-in time</small>
                                </div>

                                <div class="form-group">
                                    <label class="form-label"><i class="fa fa-clock-o"></i> Time Out</label>
                                    <input type="time" name="timeout" class="form-control modern-input">
                                    <small class="form-text text-muted">Default check-out time</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label"><i class="fa fa-calendar"></i> Work Schedule *</label>
                                    <select id="Schedule" name="Schedule" multiple class="form-control chosen chosen-select chosen-deselect modern-select" data-placeholder="Select Work Days" required>
                                        <option></option>
                                        <option>Monday</option>
                                        <option>Tuesday</option>
                                        <option>Wednesday</option>
                                        <option>Thursday</option>
                                        <option>Friday</option>
                                        <option>Saturday</option>
                                        <option>Sunday</option>
                                    </select>
                                    <input type="hidden" name="txtSchedule" id="txtSchedule">
                                    <small class="form-text text-muted">Select working days</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Modal Footer with Navigation -->
            <div class="modal-footer" style="padding: 20px 30px; background: #f8f9fa; border-radius: 0 0 15px 15px;">
                <div class="step-navigation">
                    <button type="button" class="btn btn-secondary" id="prevStep" style="display: none;">
                        <i class="fa fa-arrow-left"></i> Previous
                    </button>
                    <button type="button" class="btn btn-modern btn-primary-modern" id="nextStep">
                        Next <i class="fa fa-arrow-right"></i>
                    </button>
                    <button type="submit" class="btn btn-modern btn-success-modern" id="submitForm" style="display: none;" form="employeeForm">
                        <i class="fa fa-save"></i> Save Employee
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    let currentStep = 1;
    const totalSteps = 3;

    document.addEventListener("DOMContentLoaded", function(){
        // Test if JavaScript is working
        console.log('=== ACCOUNTS PAGE DEBUG ===');
        console.log('DOMContentLoaded event fired');
        console.log('jQuery available:', typeof $ !== 'undefined');

        try {
            console.log('Calling getAccounts...');
            getAccounts();
            console.log('getAccounts completed');
        } catch(e) {
            console.error('Error in getAccounts:', e);
        }

        try {
            console.log('Calling loadStats...');
            loadStats();
            console.log('loadStats completed');
        } catch(e) {
            console.error('Error in loadStats:', e);
        }

        try {
            console.log('Calling initializeModal...');
            initializeModal();
            console.log('initializeModal completed');
        } catch(e) {
            console.error('Error in initializeModal:', e);
        }

        // Set default checkbox states for bulk import modal
        setTimeout(function() {
            try {
                console.log('Setting default checkbox states...');
                $('input[name="skip_duplicates"]').prop('checked', true);
                $('input[name="auto_generate_ids"]').prop('checked', true);
                console.log('Default checkbox states set on page load');

                // Verify checkboxes exist
                console.log('Skip duplicates checkbox found:', $('input[name="skip_duplicates"]').length);
                console.log('Auto generate checkbox found:', $('input[name="auto_generate_ids"]').length);
            } catch(e) {
                console.error('Error setting checkbox states:', e);
            }
        }, 500);

        console.log('=== DOMContentLoaded COMPLETED ===');
    }, false);

    function getAccounts(){
        $('#accounts').dataTable({
            'bProcessing': true,
            'sAjaxSource': 'function/getAccounts.php',
            'sAjaxDataProp': "aaData",
            'lengthMenu': [[15,50,100,200,500,-1],[15,50,100,200,500,'ALL']],
            'destroy': true,
            "order": [[ 2, "asc" ]],
            'fnDrawCallback': function() {
                console.log('DataTable drawn, checking for buttons...');
                console.log('Edit buttons found:', $('.edit-btn').length);
                console.log('Delete buttons found:', $('.delete-btn').length);
            },
            'fnServerData': function(sSource, aoData, fnCallback) {
                $.ajax({
                    'dataType': 'json',
                    'type': 'GET',
                    'url': sSource,
                    'data': aoData,
                    'success': function(json) {
                        console.log('AJAX Success - Raw response:', json);
                        if(json.error) {
                            console.error('Server error:', json.error);
                            showNotification('Error loading data: ' + json.error, 'error');
                        }
                        fnCallback(json);
                    },
                    'error': function(xhr, status, error) {
                        console.error('AJAX Error:', status, error);
                        console.error('Response text:', xhr.responseText);
                        showNotification('Error loading employee data: ' + error, 'error');

                        // Try to show what was actually returned
                        if(xhr.responseText) {
                            console.log('Raw response text:', xhr.responseText);
                        }
                    }
                });
            }
        });
    }

    function loadStats() {
        // Load statistics (you can implement AJAX calls here)
        $('#totalEmployees').text('0');
        $('#activeEmployees').text('0');
        $('#newEmployees').text('0');
    }

    function initializeModal() {
        console.log('Initializing modal...');

        try {
            // Initialize chosen selects
            $(".chosen,.chosen_body").chosen({width: "100%"});
            console.log('Chosen selects initialized');

            // Reset modal on show
            $('#AddModal').on('show.bs.modal', function() {
                resetModal();
            });
            console.log('Modal event handlers set');

            // Generate initial IDs when page loads (with error handling)
            try {
                generateEmployeeIDs();
                console.log('Employee ID generation initiated');
            } catch(e) {
                console.error('Error in generateEmployeeIDs:', e);
            }
        } catch(e) {
            console.error('Error in initializeModal:', e);
        }

        console.log('Modal initialization completed');
    }

    function resetModal() {
        currentStep = 1;
        showStep(1);
        $('#employeeForm')[0].reset();
        $('.step').removeClass('active completed');
        $('.step[data-step="1"]').addClass('active');

        // Generate new IDs when modal is reset
        generateEmployeeIDs();
    }

    function showStep(step) {
        $('.form-step').removeClass('active');
        $('#step' + step).addClass('active');

        // Update progress steps
        $('.step').removeClass('active completed');
        for(let i = 1; i < step; i++) {
            $('.step[data-step="' + i + '"]').addClass('completed');
        }
        $('.step[data-step="' + step + '"]').addClass('active');

        // Update navigation buttons
        if(step === 1) {
            $('#prevStep').hide();
        } else {
            $('#prevStep').show();
        }

        if(step === totalSteps) {
            $('#nextStep').hide();
            $('#submitForm').show();
        } else {
            $('#nextStep').show();
            $('#submitForm').hide();
        }
    }

    // Modal event handlers
    $(document).on('click', '#showAddModal', function(){
        $('#AddModal').modal('show');
    });

    $(document).on('click', '#nextStep', function(){
        if(validateCurrentStep()) {
            if(currentStep < totalSteps) {
                currentStep++;
                showStep(currentStep);
            }
        }
    });

    $(document).on('click', '#prevStep', function(){
        if(currentStep > 1) {
            currentStep--;
            showStep(currentStep);
        }
    });

    function validateCurrentStep() {
        let isValid = true;
        const currentStepElement = $('#step' + currentStep);

        // Check required fields in current step
        currentStepElement.find('input[required], select[required]').each(function() {
            if(!$(this).val()) {
                $(this).addClass('is-invalid');
                isValid = false;
            } else {
                $(this).removeClass('is-invalid');
            }
        });

        // Special validation for step 1 - ensure IDs are generated
        if(currentStep === 1) {
            if(!$('#biometricsID').val()) {
                showNotification('Biometric ID is required. Generating now...', 'info');
                generateEmployeeIDs();
                isValid = false;
            }
            if(!$('#employeeid').val()) {
                showNotification('Employee ID is required. Generating now...', 'info');
                generateEmployeeIDs();
                isValid = false;
            }
        }

        return isValid;
    }

    // Global search functionality
    $(document).on('keyup', '#globalSearch', function(){
        $('#accounts').DataTable().search($(this).val()).draw();
    });

    // Export functionality
    $(document).on('click', '#exportBtn', function(){
        const table = $('#accounts').DataTable();
        const data = table.buttons.exportData();

        // Create CSV content
        let csvContent = "data:text/csv;charset=utf-8,";
        csvContent += "Biometric ID,Employee ID,Full Name,Date Hired,Position,Branch\n";

        table.rows().every(function() {
            const rowData = this.data();
            // Remove HTML tags and get clean data
            const cleanData = rowData.slice(0, 6).map(cell => {
                return cell.replace(/<[^>]*>/g, '').replace(/,/g, ';');
            });
            csvContent += cleanData.join(',') + '\n';
        });

        // Download CSV
        const encodedUri = encodeURI(csvContent);
        const link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        link.setAttribute("download", "employees_" + new Date().toISOString().split('T')[0] + ".csv");
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    });

    // Schedule select handler
    $(document).ready(function(){
        setTimeout(function() {
            if(document.getElementById('Schedule')) {
                document.getElementById('Schedule').onchange = function(){
                    var schedule = document.getElementById('txtSchedule');
                    if(schedule) {
                        schedule.value = $('#Schedule').val();
                    }
                }
            }
        }, 1000);
    });

    // Edit and Delete Button Handlers - Updated approach
    $(document).on('click', '.edit-btn', function(e){
        e.preventDefault();

        // Try multiple ways to get the employee ID
        let employeeId = $(this).data('employee-id') || $(this).attr('data-employee-id');

        // If still not found, try to extract from the row data
        if(!employeeId) {
            const row = $(this).closest('tr');
            const rowData = $('#accounts').DataTable().row(row).data();
            console.log('Row data:', rowData);

            // Try to extract ID from the first column (biometric ID link)
            if(rowData && rowData[0]) {
                const match = rowData[0].match(/data-employee-id="(\d+)"/);
                if(match) {
                    employeeId = match[1];
                }
            }
        }

        console.log('Edit button clicked, Employee ID:', employeeId);

        if(employeeId) {
            loadEmployeeData(employeeId);
        } else {
            showNotification('Employee ID not found. Please check the console for debugging info.', 'error');
            console.log('Button element:', this);
            console.log('Button data attributes:', $(this).data());
        }
    });

    $(document).on('click', '.delete-btn', function(e){
        e.preventDefault();

        // Try multiple ways to get the employee ID
        let employeeId = $(this).data('employee-id') || $(this).attr('data-employee-id');

        // If still not found, try to extract from the row data
        if(!employeeId) {
            const row = $(this).closest('tr');
            const rowData = $('#accounts').DataTable().row(row).data();

            // Try to extract ID from the first column
            if(rowData && rowData[0]) {
                const match = rowData[0].match(/data-employee-id="(\d+)"/);
                if(match) {
                    employeeId = match[1];
                }
            }
        }

        console.log('Delete button clicked, Employee ID:', employeeId);

        if(employeeId) {
            $('#deleteEmployeeId').val(employeeId);
            $('#DeleteModal').modal('show');
        } else {
            showNotification('Employee ID not found. Please check the console for debugging info.', 'error');
            console.log('Button element:', this);
            console.log('Button data attributes:', $(this).data());
        }
    });

    // Load employee data for editing
    function loadEmployeeData(employeeId) {
        console.log('Loading employee data for ID:', employeeId); // Debug log

        if(!employeeId) {
            showNotification('Invalid employee ID', 'error');
            return;
        }

        // Show loading state
        showNotification('Loading employee data...', 'info');

        $.ajax({
            url: 'function/getEmployeeData.php',
            type: 'POST',
            data: { employee_id: employeeId },
            dataType: 'json',
            beforeSend: function() {
                console.log('Sending request with data:', { employee_id: employeeId });
            },
            success: function(response) {
                console.log('Response received:', response); // Debug log

                if(response.success) {
                    const data = response.data;

                    // Populate edit form
                    $('#editEmployeeId').val(data.EntryID);
                    $('#editBiometricsID').val(data.AccessID);
                    $('#editEmployeeID').val(data.EmployeeID);
                    $('#editFullname').val(data.FullName);
                    $('#editPhone').val(data.ContactNo);
                    $('#editAddress').val(data.Address);
                    $('#editDatehired').val(data.DateHired);
                    $('#editPosition').val(data.Position);
                    $('#editCompany').val(data.AgencyCompany);
                    $('#editProject').val(data.ProjectAssigned);
                    $('#editTimein').val(data.TimeIN);
                    $('#editTimeout').val(data.TimeOut);

                    // Handle branch display (read-only)
                    if(data.branch_name) {
                        $('#editBranchDisplay').val(data.branch_name);
                        $('#editBranchId').val(data.branch_id);
                    } else {
                        $('#editBranchDisplay').val('<?php echo $current_branch['branch_name']; ?>');
                        $('#editBranchId').val('<?php echo $current_branch['branch_id']; ?>');
                    }

                    // Handle schedule
                    if(data.schedule_dates) {
                        const schedules = data.schedule_dates.split(',');
                        $('#editSchedule').val(schedules);
                        $('#editSchedule').trigger('chosen:updated');
                        $('#txtEditSchedule').val(data.schedule_dates);
                    }

                    $('#EditModal').modal('show');
                    showNotification('Employee data loaded successfully', 'success');
                } else {
                    showNotification('Error loading employee data: ' + response.message, 'error');
                }
            },
            error: function(xhr, status, error) {
                console.log('AJAX Error:', xhr.responseText); // Debug log
                showNotification('Error loading employee data: ' + error, 'error');
            }
        });
    }

    // Update employee
    $(document).on('click', '#updateEmployeeBtn', function(){
        const btn = $(this);
        const originalText = btn.html();

        // Show loading state
        btn.html('<i class="fa fa-spinner fa-spin"></i> Updating...').prop('disabled', true);

        const formData = {
            employee_id: $('#editEmployeeId').val(),
            biometricsID: $('#editBiometricsID').val(),
            employeeID: $('#editEmployeeID').val(),
            fullname: $('#editFullname').val(),
            phone: $('#editPhone').val(),
            datehired: $('#editDatehired').val(),
            position: $('#editPosition').val(),
            company: $('#editCompany').val(),
            project: $('#editProject').val(),
            address: $('#editAddress').val(),
            timein: $('#editTimein').val(),
            timeout: $('#editTimeout').val(),
            branch_id: $('#editBranchId').val(),
            schedule: $('#txtEditSchedule').val()
        };

        $.ajax({
            url: 'function/updateEmployee.php',
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if(response.success) {
                    showNotification('Employee updated successfully!', 'success');
                    $('#EditModal').modal('hide');
                    $('#accounts').DataTable().ajax.reload();
                } else {
                    showNotification('Error updating employee: ' + response.message, 'error');
                }
            },
            error: function(xhr, status, error) {
                showNotification('Error updating employee: ' + error, 'error');
            },
            complete: function() {
                // Reset button state
                btn.html(originalText).prop('disabled', false);
            }
        });
    });

    // Delete employee permanently
    $(document).on('click', '#confirmDeleteBtn', function(){
        const btn = $(this);
        const originalText = btn.html();
        const employeeId = $('#deleteEmployeeId').val();
        const remarks = $('#deleteRemarks').val();

        // Validate remarks
        if(!remarks.trim()) {
            showNotification('Please enter a reason for deletion', 'error');
            return;
        }

        // Show loading state
        btn.html('<i class="fa fa-spinner fa-spin"></i> Deleting...').prop('disabled', true);

        $.ajax({
            url: 'function/deleteEmployee.php',
            type: 'POST',
            data: {
                employee_id: employeeId,
                delete_type: 'permanent',
                remarks: remarks
            },
            dataType: 'json',
            success: function(response) {
                if(response.success) {
                    showNotification('Employee deleted permanently. ID numbers reserved.', 'success');
                    $('#DeleteModal').modal('hide');
                    $('#accounts').DataTable().ajax.reload();
                    // Clear form
                    $('#deleteRemarks').val('');
                } else {
                    showNotification('Error deleting employee: ' + response.message, 'error');
                }
            },
            error: function(xhr, status, error) {
                showNotification('Error deleting employee: ' + error, 'error');
            },
            complete: function() {
                // Reset button state
                btn.html(originalText).prop('disabled', false);
            }
        });
    });

    // Notification function
    function showNotification(message, type) {
        let alertClass, iconClass;

        switch(type) {
            case 'success':
                alertClass = 'alert-success';
                iconClass = 'fa-check-circle';
                break;
            case 'error':
                alertClass = 'alert-danger';
                iconClass = 'fa-exclamation-circle';
                break;
            case 'info':
                alertClass = 'alert-info';
                iconClass = 'fa-info-circle';
                break;
            default:
                alertClass = 'alert-info';
                iconClass = 'fa-info-circle';
        }

        const notification = $(`
            <div class="alert ${alertClass} alert-dismissible" style="position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
                <button type="button" class="close" data-dismiss="alert">&times;</button>
                <i class="fa ${iconClass}"></i> ${message}
            </div>
        `);

        $('body').append(notification);

        // Auto-hide after different times based on type
        const hideTime = type === 'info' ? 2000 : 5000;
        setTimeout(function() {
            notification.fadeOut(function() {
                $(this).remove();
            });
        }, hideTime);
    }

    // Handle edit modal chosen selects
    $('#EditModal').on('shown.bs.modal', function() {
        $('#editAccessArea').chosen({width: "100%"});
        $('#editSchedule').chosen({width: "100%"});

        $('#editAccessArea').on('change', function() {
            $('#txtEditAccessArea').val($(this).val());
        });

        $('#editSchedule').on('change', function() {
            $('#txtEditSchedule').val($(this).val());
        });
    });

    // ID Generation Functions
    function generateEmployeeIDs() {
        $.ajax({
            url: 'function/generateEmployeeIDs.php',
            type: 'POST',
            data: { action: 'generate_ids' },
            dataType: 'json',
            success: function(response) {
                if(response.success) {
                    $('#biometricsID').val(response.biometric_id);
                    $('#employeeid').val(response.employee_id);
                    console.log('Generated IDs - Biometric:', response.biometric_id, 'Employee:', response.employee_id);
                } else {
                    showNotification('Error generating IDs: ' + response.message, 'error');
                }
            },
            error: function(xhr, status, error) {
                showNotification('Error generating IDs: ' + error, 'error');
                console.log('ID generation error:', xhr.responseText);
            }
        });
    }

    // Individual ID generation buttons
    $(document).on('click', '#generateBiometricID', function() {
        const btn = $(this);
        const originalHtml = btn.html();

        btn.html('<i class="fa fa-spinner fa-spin"></i>').prop('disabled', true);

        $.ajax({
            url: 'function/generateEmployeeIDs.php',
            type: 'POST',
            data: { action: 'generate_ids' },
            dataType: 'json',
            success: function(response) {
                if(response.success) {
                    $('#biometricsID').val(response.biometric_id);
                    showNotification('New Biometric ID generated: ' + response.biometric_id, 'success');
                } else {
                    showNotification('Error generating Biometric ID: ' + response.message, 'error');
                }
            },
            error: function(xhr, status, error) {
                showNotification('Error generating Biometric ID: ' + error, 'error');
            },
            complete: function() {
                btn.html(originalHtml).prop('disabled', false);
            }
        });
    });

    $(document).on('click', '#generateEmployeeID', function() {
        const btn = $(this);
        const originalHtml = btn.html();

        btn.html('<i class="fa fa-spinner fa-spin"></i>').prop('disabled', true);

        $.ajax({
            url: 'function/generateEmployeeIDs.php',
            type: 'POST',
            data: { action: 'generate_ids' },
            dataType: 'json',
            success: function(response) {
                if(response.success) {
                    $('#employeeid').val(response.employee_id);
                    showNotification('New Employee ID generated: ' + response.employee_id, 'success');
                } else {
                    showNotification('Error generating Employee ID: ' + response.message, 'error');
                }
            },
            error: function(xhr, status, error) {
                showNotification('Error generating Employee ID: ' + error, 'error');
            },
            complete: function() {
                btn.html(originalHtml).prop('disabled', false);
            }
        });
    });

    // Direct functions called from onclick events
    window.editEmployee = function(employeeId) {
        console.log('editEmployee called with ID:', employeeId);
        if(employeeId) {
            loadEmployeeData(employeeId);
        } else {
            showNotification('Invalid employee ID', 'error');
        }
    };

    window.deleteEmployee = function(employeeId) {
        console.log('deleteEmployee called with ID:', employeeId);
        if(employeeId) {
            $('#deleteEmployeeId').val(employeeId);
            $('#DeleteModal').modal('show');
        } else {
            showNotification('Invalid employee ID', 'error');
        }
    };

    // Test function to manually test edit functionality
    function testEditFunction() {
        // Try to get the first employee ID from the table
        const firstRow = $('#accounts tbody tr:first');
        if(firstRow.length > 0) {
            const editBtn = firstRow.find('.edit-btn');
            if(editBtn.length > 0) {
                const employeeId = editBtn.data('employee-id');
                console.log('Testing with employee ID:', employeeId);
                if(employeeId) {
                    loadEmployeeData(employeeId);
                } else {
                    // Try to get employee ID from a different approach
                    console.log('Trying alternative method...');
                    loadEmployeeData('1'); // Test with ID 1
                }
            } else {
                console.log('No edit button found in first row');
                // Test with a hardcoded ID
                loadEmployeeData('1');
            }
        } else {
            console.log('No rows found in table');
            // Test with a hardcoded ID
            loadEmployeeData('1');
        }
    }

    // Branch switching function
    function switchBranch(branchId) {
        if(branchId) {
            // Set the current branch in session and reload page
            $.ajax({
                url: 'function/setBranch.php',
                type: 'POST',
                data: { branch_id: branchId },
                dataType: 'json',
                success: function(response) {
                    if(response.success) {
                        // Reload the page to reflect the new branch
                        window.location.reload();
                    } else {
                        showNotification('Error switching branch: ' + response.message, 'error');
                    }
                },
                error: function() {
                    // Fallback: reload page anyway
                    window.location.reload();
                }
            });
        }
    }

    // Show bulk import modal
    $(document).on('click', '#showBulkImportModal', function(){
        console.log('=== BULK IMPORT MODAL CLICKED ===');
        console.log('Button clicked, preparing to show modal');

        // Set checkboxes before showing modal
        setTimeout(function() {
            console.log('Setting checkboxes before modal show...');

            // Force set both checkboxes with multiple methods
            const skipDuplicates = $('input[name="skip_duplicates"]');
            const autoGenerate = $('input[name="auto_generate_ids"]');

            // Method 1: prop
            skipDuplicates.prop('checked', true);
            autoGenerate.prop('checked', true);

            // Method 2: attr
            skipDuplicates.attr('checked', 'checked');
            autoGenerate.attr('checked', 'checked');

            // Method 3: direct property
            if(skipDuplicates[0]) skipDuplicates[0].checked = true;
            if(autoGenerate[0]) autoGenerate[0].checked = true;

            console.log('Checkboxes set before modal show');
        }, 100);

        console.log('Showing modal...');
        $('#BulkImportModal').modal('show');
    });

    // Ensure checkboxes are checked when modal is shown
    $('#BulkImportModal').on('shown.bs.modal', function () {
        console.log('Bulk Import Modal shown - setting default checkbox states');

        // Set default checkbox states with more specific targeting
        const skipDuplicatesCheckbox = $('input[name="skip_duplicates"]');
        const autoGenerateCheckbox = $('input[name="auto_generate_ids"]');

        console.log('Skip duplicates checkbox found:', skipDuplicatesCheckbox.length);
        console.log('Auto generate checkbox found:', autoGenerateCheckbox.length);

        // Force set the checkboxes
        skipDuplicatesCheckbox.prop('checked', true);
        autoGenerateCheckbox.prop('checked', true);

        // Alternative method - set the checked attribute
        skipDuplicatesCheckbox.attr('checked', 'checked');
        autoGenerateCheckbox.attr('checked', 'checked');

        // Trigger change event to ensure UI updates
        skipDuplicatesCheckbox.trigger('change');
        autoGenerateCheckbox.trigger('change');

        // Clear any previous file selection
        $('input[name="excel_file"]').val('');

        // Debug: Check if checkboxes are actually checked after setting
        setTimeout(function() {
            console.log('=== AFTER SETTING CHECKBOXES ===');
            console.log('Skip duplicates checked:', $('input[name="skip_duplicates"]').is(':checked'));
            console.log('Auto generate IDs checked:', $('input[name="auto_generate_ids"]').is(':checked'));
        }, 100);
    });

    // Handle bulk import form submission
    $(document).on('submit', '#bulkImportForm', function(e){
        e.preventDefault();

        const formData = new FormData(this);
        const btn = $('#bulkImportBtn');
        const originalText = btn.html();

        // Show loading state
        btn.html('<i class="fa fa-spinner fa-spin"></i> Processing...').prop('disabled', true);

        $.ajax({
            url: 'function/bulkImportEmployees.php',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            dataType: 'json',
            success: function(response) {
                if(response.success) {
                    showNotification('Bulk import completed! ' + response.imported + ' employees imported successfully.', 'success');
                    $('#BulkImportModal').modal('hide');
                    $('#accounts').DataTable().ajax.reload();

                    // Reset form but maintain default checkbox states
                    $('#bulkImportForm')[0].reset();
                    $('input[name="skip_duplicates"]').prop('checked', true);
                    $('input[name="auto_generate_ids"]').prop('checked', true);

                    // Show import summary
                    if(response.errors && response.errors.length > 0) {
                        let errorMsg = 'Import completed with some errors:\n';
                        response.errors.forEach(function(error) {
                            errorMsg += '• Row ' + error.row + ': ' + error.message + '\n';
                        });
                        alert(errorMsg);
                    }
                } else {
                    showNotification('Error during bulk import: ' + response.message, 'error');
                }
            },
            error: function(xhr, status, error) {
                console.log('Bulk Import AJAX Error Details:');
                console.log('Status:', status);
                console.log('Error:', error);
                console.log('Response Text:', xhr.responseText);
                console.log('Response Length:', xhr.responseText.length);

                let errorMessage = 'Error during bulk import: ' + error;

                // Try to identify the specific issue
                if(xhr.responseText.includes('Fatal error') || xhr.responseText.includes('Parse error')) {
                    errorMessage += ' (PHP Fatal/Parse Error - check server logs)';
                } else if(xhr.responseText.includes('<!DOCTYPE') || xhr.responseText.includes('<html')) {
                    errorMessage += ' (Server returned HTML instead of JSON)';
                } else if(xhr.responseText.trim() === '') {
                    errorMessage += ' (Empty response from server)';
                } else if(xhr.responseText.includes('SyntaxError')) {
                    errorMessage += ' (JSON parsing error - server response may contain HTML)';
                }

                showNotification(errorMessage, 'error');
            },
            complete: function() {
                // Reset button state
                btn.html(originalText).prop('disabled', false);
            }
        });
    });

    // Download template
    $(document).on('click', '#downloadTemplate', function(){
        window.location.href = 'function/downloadTemplate.php';
    });

</script>

<!-- Edit Employee Modal -->
<div class="modal fade" role="dialog" id="EditModal">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="border-radius: 15px; border: none; box-shadow: 0 10px 40px rgba(0,0,0,0.15);">
            <!-- Modal Header -->
            <div class="modal-header" style="background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%); color: white; border-radius: 15px 15px 0 0; padding: 20px 30px;">
                <h4 class="modal-title" style="margin: 0; font-weight: 600;">
                    <i class="fa fa-edit"></i> Edit Employee
                </h4>
                <button type="button" class="close" data-dismiss="modal" style="color: white; opacity: 0.8; font-size: 24px;">
                    <span>&times;</span>
                </button>
            </div>

            <div class="modal-body" style="padding: 30px;">
                <form id="editEmployeeForm">
                    <input type="hidden" id="editEmployeeId" name="employee_id">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label"><i class="fa fa-fingerprint"></i> Biometric ID *</label>
                                <input type="text" id="editBiometricsID" name="biometricsID" class="form-control modern-input" required readonly>
                                <small class="form-text text-muted">Biometric ID cannot be changed to maintain uniqueness</small>
                            </div>

                            <div class="form-group">
                                <label class="form-label"><i class="fa fa-id-card"></i> Employee ID *</label>
                                <input type="text" id="editEmployeeID" name="employeeID" class="form-control modern-input" required readonly>
                                <small class="form-text text-muted">Employee ID cannot be changed to maintain uniqueness</small>
                            </div>

                            <div class="form-group">
                                <label class="form-label"><i class="fa fa-user"></i> Full Name *</label>
                                <input type="text" id="editFullname" name="fullname" class="form-control modern-input" required>
                            </div>

                            <div class="form-group">
                                <label class="form-label"><i class="fa fa-phone"></i> Phone Number</label>
                                <input type="text" id="editPhone" name="phone" class="form-control modern-input">
                            </div>

                            <div class="form-group">
                                <label class="form-label"><i class="fa fa-calendar"></i> Date Hired *</label>
                                <input type="date" id="editDatehired" name="datehired" class="form-control modern-input" required>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label"><i class="fa fa-briefcase"></i> Position *</label>
                                <input type="text" id="editPosition" name="position" class="form-control modern-input" required>
                            </div>

                            <div class="form-group">
                                <label class="form-label"><i class="fa fa-building"></i> Company</label>
                                <input type="text" id="editCompany" name="company" class="form-control modern-input">
                            </div>

                            <div class="form-group">
                                <label class="form-label"><i class="fa fa-project-diagram"></i> Project</label>
                                <input type="text" id="editProject" name="project" class="form-control modern-input">
                            </div>

                            <div class="form-group">
                                <label class="form-label"><i class="fa fa-map-marker"></i> Address</label>
                                <input type="text" id="editAddress" name="address" class="form-control modern-input">
                            </div>

                            <div class="form-group">
                                <label class="form-label"><i class="fa fa-building"></i> Branch</label>
                                <div style="width: 100%; position: relative;">
                                    <input type="text" id="editBranchDisplay" class="form-control" readonly style="width: 100%; height: 45px; padding: 10px 15px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 14px; background-color: #f8f9fa; color: #6c757d;">
                                    <input type="hidden" id="editBranchId" name="branch_id">
                                </div>
                                <small class="form-text text-muted">Branch cannot be changed (set during login)</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label"><i class="fa fa-clock-o"></i> Time In</label>
                                <input type="time" id="editTimein" name="timein" class="form-control modern-input">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label"><i class="fa fa-clock-o"></i> Time Out</label>
                                <input type="time" id="editTimeout" name="timeout" class="form-control modern-input">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label"><i class="fa fa-calendar"></i> Schedule</label>
                                <select id="editSchedule" name="schedule" multiple class="form-control chosen chosen-select chosen-deselect modern-select">
                                    <option></option>
                                    <option>Monday</option>
                                    <option>Tuesday</option>
                                    <option>Wednesday</option>
                                    <option>Thursday</option>
                                    <option>Friday</option>
                                    <option>Saturday</option>
                                    <option>Sunday</option>
                                </select>
                                <input type="hidden" name="txtEditSchedule" id="txtEditSchedule">
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Modal Footer -->
            <div class="modal-footer" style="padding: 20px 30px; background: #f8f9fa; border-radius: 0 0 15px 15px;">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-modern btn-success-modern" id="updateEmployeeBtn">
                    <i class="fa fa-save"></i> Update Employee
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" role="dialog" id="DeleteModal">
    <div class="modal-dialog">
        <div class="modal-content" style="border-radius: 15px; border: none; box-shadow: 0 10px 40px rgba(0,0,0,0.15);">
            <!-- Modal Header -->
            <div class="modal-header" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); color: white; border-radius: 15px 15px 0 0; padding: 20px 30px;">
                <h4 class="modal-title" style="margin: 0; font-weight: 600;">
                    <i class="fa fa-trash"></i> Delete Employee Permanently
                </h4>
                <button type="button" class="close" data-dismiss="modal" style="color: white; opacity: 0.8; font-size: 24px;">
                    <span>&times;</span>
                </button>
            </div>

            <div class="modal-body" style="padding: 30px;">
                <input type="hidden" id="deleteEmployeeId">

                <div class="form-group">
                    <label class="form-label">Reason for Deletion:</label>
                    <textarea id="deleteRemarks" class="form-control modern-input" rows="3" placeholder="Enter reason for permanent deletion..." required></textarea>
                </div>

                <div class="alert alert-danger">
                    <i class="fa fa-exclamation-triangle"></i>
                    <strong>WARNING:</strong> This action will permanently delete the employee from the database.
                    The employee's ID numbers will never be reused. This action cannot be undone!
                </div>

                <div class="alert alert-info">
                    <i class="fa fa-info-circle"></i>
                    <strong>Note:</strong> The employee's Biometric ID and Employee ID will be reserved and never assigned to future employees.
                </div>
            </div>

            <!-- Modal Footer -->
            <div class="modal-footer" style="padding: 20px 30px; background: #f8f9fa; border-radius: 0 0 15px 15px;">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="fa fa-trash"></i> Delete Permanently
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Import Modal -->
<div class="modal fade" id="BulkImportModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content" style="border-radius: 15px; border: none; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
            <!-- Modal Header -->
            <div class="modal-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 15px 15px 0 0; padding: 20px 30px;">
                <h4 class="modal-title" style="margin: 0; font-weight: 600;">
                    <i class="fa fa-upload"></i> Bulk Import Employees from Excel
                </h4>
                <button type="button" class="close" data-dismiss="modal" style="color: white; opacity: 0.8; font-size: 24px;">
                    <span>&times;</span>
                </button>
            </div>

            <!-- Modal Body -->
            <div class="modal-body" style="padding: 30px;">
                <form id="bulkImportForm" enctype="multipart/form-data">
                    <!-- Instructions -->
                    <div class="alert alert-info">
                        <h5><i class="fa fa-info-circle"></i> Instructions:</h5>
                        <ol>
                            <li>Download the Excel template using the button below</li>
                            <li>Fill in the employee data in the template</li>
                            <li>Upload the completed Excel file</li>
                            <li>Review and confirm the import</li>
                        </ol>
                    </div>

                    <!-- Template Download -->
                    <div class="form-group">
                        <label class="form-label"><i class="fa fa-download"></i> Step 1: Download Template</label>
                        <div>
                            <button type="button" id="downloadTemplate" class="btn btn-success">
                                <i class="fa fa-download"></i> Download Excel Template
                            </button>
                            <small class="form-text text-muted">Download the template with the correct column headers</small>
                        </div>
                    </div>

                    <!-- File Upload -->
                    <div class="form-group">
                        <label class="form-label"><i class="fa fa-upload"></i> Step 2: Upload Completed Excel File *</label>
                        <input type="file" name="excel_file" id="excelFile" class="form-control modern-input" accept=".xlsx,.xls,.csv" required>
                        <small class="form-text text-muted">Supported formats: .xlsx, .xls, .csv (Max size: 10MB)</small>
                    </div>

                    <!-- Import Options -->
                    <div class="form-group">
                        <label class="form-label"><i class="fa fa-cogs"></i> Import Options</label>

                        <!-- Custom styled checkboxes -->
                        <div class="checkbox-container" style="margin: 10px 0;">
                            <label class="checkbox-label" style="display: flex; align-items: center; cursor: pointer; padding: 8px 0;">
                                <input type="checkbox" name="skip_duplicates" value="1" checked
                                       style="margin-right: 10px; transform: scale(1.2); cursor: pointer;">
                                <span style="font-size: 14px;">Skip duplicate Employee IDs (recommended)</span>
                            </label>
                        </div>

                        <div class="checkbox-container" style="margin: 10px 0;">
                            <label class="checkbox-label" style="display: flex; align-items: center; cursor: pointer; padding: 8px 0;">
                                <input type="checkbox" name="auto_generate_ids" value="1" checked
                                       style="margin-right: 10px; transform: scale(1.2); cursor: pointer;">
                                <span style="font-size: 14px;">Auto-generate missing Employee/Biometric IDs</span>
                            </label>
                        </div>
                    </div>

                    <!-- Branch Info -->
                    <div class="alert alert-warning">
                        <i class="fa fa-building"></i> <strong>Branch Assignment:</strong>
                        All imported employees will be assigned to your current branch: <strong><?php echo $current_branch['branch_name']; ?></strong>
                    </div>
                </form>
            </div>

            <!-- Modal Footer -->
            <div class="modal-footer" style="padding: 20px 30px; background: #f8f9fa; border-radius: 0 0 15px 15px;">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="submit" class="btn btn-modern btn-primary-modern" id="bulkImportBtn" form="bulkImportForm">
                    <i class="fa fa-upload"></i> Import Employees
                </button>
            </div>
        </div>
    </div>
</div>

</body>
</html>
