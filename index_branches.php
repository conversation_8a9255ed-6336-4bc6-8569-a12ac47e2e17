<?php
session_start();
include('proc/config.php');

// Check if user is already logged in
$is_logged_in = isset($_SESSION['SESS_USER_ID']);
$user_name = $is_logged_in ? $_SESSION['SESS_FIRST_NAME'] : '';
$access_level = $is_logged_in ? $_SESSION['ACCESSLEVEL'] : '';

// Get branches
$branches = array();
$check_table = mysql_query("SHOW TABLES LIKE 'tbl_branches'");
if($check_table && mysql_num_rows($check_table) > 0) {
    $branches_sql = "SELECT * FROM tbl_branches WHERE branch_status = 'Active' ORDER BY branch_name";
    $branches_result = mysql_query($branches_sql);
    
    if($branches_result && mysql_num_rows($branches_result) > 0) {
        while($branch = mysql_fetch_assoc($branches_result)) {
            $branches[] = $branch;
        }
    }
}

// Default branches if none found
if(empty($branches)) {
    $branches = array(
        array('branch_id' => 1, 'branch_name' => 'Ajwa Garden Multan', 'branch_code' => 'AGM', 'branch_location' => 'Multan'),
        array('branch_id' => 2, 'branch_name' => 'Ajwa Garden Bahawalpur', 'branch_code' => 'AGB', 'branch_location' => 'Bahawalpur'),
        array('branch_id' => 3, 'branch_name' => 'Ajwa Garden Sahiwal', 'branch_code' => 'AGS', 'branch_location' => 'Sahiwal')
    );
}

// Handle branch selection
if(isset($_GET['branch']) || isset($_POST['branch_id'])) {
    $selected_branch_id = isset($_GET['branch']) ? (int)$_GET['branch'] : (int)$_POST['branch_id'];

    // Set branch in session
    $_SESSION['CURRENT_BRANCH_ID'] = $selected_branch_id;

    foreach($branches as $branch) {
        if($branch['branch_id'] == $selected_branch_id) {
            $_SESSION['CURRENT_BRANCH_NAME'] = $branch['branch_name'];
            $_SESSION['CURRENT_BRANCH_CODE'] = $branch['branch_code'];
            break;
        }
    }

    // Define branch URLs - customize these for your actual branch URLs
    $branch_urls = array(
        1 => 'https://ajwagardenmultan.com/dashboard_enhanced.php',
        2 => 'https://ajwagardenbahawalpur.com/dashboard_enhanced.php',
        3 => 'https://ajwagardensahiwal.com/dashboard_enhanced.php'
    );

    // Redirect to branch URL or default dashboard
    if(isset($branch_urls[$selected_branch_id])) {
        header("Location: " . $branch_urls[$selected_branch_id]);
    } else {
        header("Location: dashboard_enhanced.php");
    }
    exit();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>Ajwa Garden - Biometric System</title>
    
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome - using multiple fallbacks -->
    <link href="https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <!-- Local fallback -->
    <link href="dist/font-awesome/css/font-awesome.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            display: flex;
            flex-direction: column;
            margin: 0;
            padding: 0;
        }
        
        .main-container {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .welcome-card {
            background: white;
            border-radius: 25px;
            box-shadow: 0 25px 80px rgba(0,0,0,0.15);
            padding: 50px;
            max-width: 900px;
            width: 100%;
            text-align: center;
        }
        
        .logo-section {
            margin-bottom: 40px;
        }

        .logo-container {
            margin-bottom: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .company-logo {
            max-height: 80px;
            max-width: 200px;
            height: auto;
            width: auto;
            object-fit: contain;
            filter: drop-shadow(0 4px 8px rgba(0,0,0,0.1));
            transition: transform 0.3s ease;
        }

        .company-logo:hover {
            transform: scale(1.05);
        }

        .logo-fallback {
            font-size: 4rem;
            color: #667eea;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .logo-section h1 {
            color: #333;
            font-weight: 800;
            font-size: 2.5rem;
            margin-bottom: 10px;
            margin-top: 10px;
        }

        .logo-section .subtitle {
            color: #666;
            font-size: 1.2rem;
            margin-bottom: 30px;
        }
        
        .user-welcome {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 15px 25px;
            border-radius: 15px;
            margin-bottom: 30px;
            display: inline-block;
        }
        
        .branches-section h2 {
            color: #333;
            font-weight: 600;
            margin-bottom: 30px;
        }
        
        .branches-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }
        
        .branch-option {
            background: #f8f9fa;
            border: 3px solid #e9ecef;
            border-radius: 20px;
            padding: 30px 20px;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        
        .branch-option:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.1);
            border-color: #667eea;
        }
        
        .branch-option.selected {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: #667eea;
            transform: translateY(-8px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.3);
        }
        
        .branch-icon {
            font-size: 3.5rem;
            margin-bottom: 20px;
            color: #667eea;
        }
        
        .branch-option.selected .branch-icon {
            color: white;
        }
        
        .branch-title {
            font-size: 1.4rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .branch-location {
            font-size: 1.1rem;
            opacity: 0.8;
            margin-bottom: 15px;
        }
        
        .branch-badge {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 600;
            display: inline-block;
        }
        
        .branch-option.selected .branch-badge {
            background: rgba(255,255,255,0.2);
            color: white;
        }

        /* Loading state */
        .branch-option.loading {
            pointer-events: none;
            opacity: 0.8;
        }

        /* Footer styling */
        .footer-section {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            padding: 20px;
            text-align: center;
            margin-top: auto;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 -5px 20px rgba(0, 0, 0, 0.1);
        }

        .footer-content {
            color: #666;
            font-size: 0.9rem;
        }

        .footer-content p {
            margin: 8px 0;
        }

        .copyright {
            font-weight: 500;
        }

        .developer-credit {
            color: #667eea;
            font-weight: 500;
        }

        .developer-credit strong {
            color: #764ba2;
            font-weight: 700;
        }

        .footer-content i {
            margin-right: 5px;
            opacity: 0.7;
        }
        

        
        .quick-actions {
            margin-top: 30px;
            padding-top: 30px;
            border-top: 1px solid #e9ecef;
        }
        
        .quick-actions a {
            color: #667eea;
            text-decoration: none;
            margin: 0 20px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .quick-actions a:hover {
            color: #764ba2;
            text-decoration: none;
        }
        
        /* Fallback for missing Font Awesome icons */
        .fa:before {
            font-family: FontAwesome, sans-serif;
        }

        /* Simple fallback if Font Awesome fails to load */
        .fa-leaf:before { content: "🌿"; font-family: sans-serif; }
        .fa-building:before { content: "🏢"; font-family: sans-serif; }
        .fa-map-marker:before { content: "📍"; font-family: sans-serif; }
        .fa-user:before { content: "👤"; font-family: sans-serif; }
        .fa-link:before { content: "🔗"; font-family: sans-serif; }
        .fa-arrow-right:before { content: "→"; font-family: sans-serif; }
        .fa-sign-in:before { content: "🔑"; font-family: sans-serif; }
        .fa-tachometer:before { content: "📊"; font-family: sans-serif; }
        .fa-cogs:before { content: "⚙️"; font-family: sans-serif; }
        .fa-users:before { content: "👥"; font-family: sans-serif; }
        .fa-sign-out:before { content: "🚪"; font-family: sans-serif; }
        .fa-spinner:before { content: "⏳"; font-family: sans-serif; }

        @media (max-width: 768px) {
            .welcome-card {
                padding: 30px 20px;
            }

            .branches-grid {
                grid-template-columns: 1fr;
            }

            .logo-section h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="welcome-card">
            <div class="logo-section">
                <div class="logo-container">
                    <img src="images/Downloads/ajwa-logo.png" alt="Ajwa Garden Logo" class="company-logo" onerror="this.src='images/Downloads/ajwa-logo (1).png'; this.onerror=function(){this.style.display='none'; document.querySelector('.logo-fallback').style.display='block';}">
                    <div class="logo-fallback" style="display: none;">
                        <i class="fa fa-leaf"></i>
                    </div>
                </div>
                <h1>Ajwa Garden</h1>
                <p class="subtitle">Advanced Biometric Management System</p>
                
                <?php// if($is_logged_in): ?>
                <!-- <div class="user-welcome">
                    <i class="fa fa-user"></i> Welcome back, <strong><?php //echo htmlspecialchars($user_name); ?></strong>
                 <?php //if($access_level == 'Admin'): ?>
                    <span class="badge badge-light ml-2">Administrator</span>
                    <?php //endif; ?>
                </div> -->
                <?php// endif; ?>
            </div>
            
            <div class="branches-section">
                <h2><i class="fa fa-building"></i> Select Your Branch Location</h2>

                <div class="branches-grid">
                    <?php foreach($branches as $index => $branch): ?>
                    <div class="branch-option" onclick="selectBranch(<?php echo $branch['branch_id']; ?>)">
                        <div class="branch-icon">
                            <i class="fa fa-building"></i>
                        </div>
                        <div class="branch-title"><?php echo htmlspecialchars($branch['branch_name']); ?></div>
                        <div class="branch-location">
                            <i class="fa fa-map-marker"></i> <?php echo htmlspecialchars($branch['branch_location']); ?>
                        </div>
                        <div class="branch-badge"><?php echo htmlspecialchars($branch['branch_code']); ?></div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
            
            <!-- <div class="quick-actions">
                <a href="dashboard_enhanced.php"><i class="fa fa-tachometer"></i> Main Dashboard</a>
                
                <?php if($is_logged_in): ?>
                    <?php if($access_level == 'Admin'): ?>
                    <a href="branch_management.php"><i class="fa fa-cogs"></i> Branch Management</a>
                    <a href="pg_accounts.php"><i class="fa fa-users"></i> Employees</a>
                    <?php endif; ?>
                    <a href="logout.php"><i class="fa fa-sign-out"></i> Logout</a>
                <?php else: ?>
                    <a href="index.php"><i class="fa fa-sign-in"></i> Login</a>
                <?php endif; ?>
            </div> -->
        </div>
    </div>

    <!-- Footer -->
    <div class="footer-section">
        <div class="footer-content">
            <p class="copyright">
                <i class="fa fa-copyright"></i> <?php echo date('Y'); ?> SOFTURE. All rights reserved.
            </p>
            <p class="developer-credit">
                <i class="fa fa-code"></i> Designed & Developed by <strong>Saqib Mahmood</strong>
            </p>
        </div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function selectBranch(branchId) {
            // Prevent multiple clicks
            if(window.isRedirecting) return;
            window.isRedirecting = true;

            // Add visual feedback
            $('.branch-option').removeClass('selected');
            const selectedBranch = $('div[onclick="selectBranch(' + branchId + ')"]');
            selectedBranch.addClass('selected');

            // Store original content
            const originalContent = selectedBranch.html();

            // Show loading state temporarily
            selectedBranch.html(
                '<div class="branch-icon"><i class="fa fa-spinner fa-spin"></i></div>' +
                '<div class="branch-title">Connecting...</div>' +
                '<div class="branch-location">Please wait</div>'
            );

            console.log('Redirecting to branch:', branchId);

            // Redirect immediately
            setTimeout(function() {
                window.location.href = '?branch=' + branchId;
            }, 800);

            // Restore original content if redirect fails (fallback)
            setTimeout(function() {
                if(!window.location.href.includes('?branch=')) {
                    selectedBranch.html(originalContent);
                    selectedBranch.removeClass('selected');
                    window.isRedirecting = false;
                }
            }, 3000);
        }

        // Reset state on page load
        $(document).ready(function() {
            window.isRedirecting = false;

            // Add hover effects
            $('.branch-option').hover(
                function() {
                    if(!$(this).hasClass('selected') && !window.isRedirecting) {
                        $(this).css('transform', 'translateY(-8px)');
                    }
                },
                function() {
                    if(!$(this).hasClass('selected') && !window.isRedirecting) {
                        $(this).css('transform', 'translateY(0)');
                    }
                }
            );

            // Reset all branch options to original state
            $('.branch-option').each(function() {
                $(this).removeClass('selected');
                $(this).css('transform', 'translateY(0)');
            });
        });

        // Handle browser back button
        $(window).on('pageshow', function(event) {
            if (event.originalEvent.persisted) {
                // Page was loaded from cache, reset state
                window.location.reload();
            }
        });
    </script>
</body>
</html>
