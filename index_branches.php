<?php
session_start();
include('proc/config.php');

// Check if user is already logged in
$is_logged_in = isset($_SESSION['SESS_USER_ID']);
$user_name = $is_logged_in ? $_SESSION['SESS_FIRST_NAME'] : '';
$access_level = $is_logged_in ? $_SESSION['ACCESSLEVEL'] : '';

// Get branches
$branches = array();
$check_table = mysql_query("SHOW TABLES LIKE 'tbl_branches'");
if($check_table && mysql_num_rows($check_table) > 0) {
    $branches_sql = "SELECT * FROM tbl_branches WHERE branch_status = 'Active' ORDER BY branch_name";
    $branches_result = mysql_query($branches_sql);
    
    if($branches_result && mysql_num_rows($branches_result) > 0) {
        while($branch = mysql_fetch_assoc($branches_result)) {
            $branches[] = $branch;
        }
    }
}

// Default branches if none found
if(empty($branches)) {
    $branches = array(
        array('branch_id' => 1, 'branch_name' => 'Ajwa Garden Multan', 'branch_code' => 'AGM', 'branch_location' => 'Multan'),
        array('branch_id' => 2, 'branch_name' => 'Ajwa Garden Bahawalpur', 'branch_code' => 'AGB', 'branch_location' => 'Bahawalpur'),
        array('branch_id' => 3, 'branch_name' => 'Ajwa Garden Sahiwal', 'branch_code' => 'AGS', 'branch_location' => 'Sahiwal')
    );
}

// Handle branch selection
if(isset($_POST['select_branch'])) {
    $selected_branch_id = (int)$_POST['branch_id'];
    $branch_url = clean($_POST['branch_url']);
    
    // Set branch in session
    $_SESSION['CURRENT_BRANCH_ID'] = $selected_branch_id;
    
    foreach($branches as $branch) {
        if($branch['branch_id'] == $selected_branch_id) {
            $_SESSION['CURRENT_BRANCH_NAME'] = $branch['branch_name'];
            $_SESSION['CURRENT_BRANCH_CODE'] = $branch['branch_code'];
            break;
        }
    }
    
    // Redirect based on URL or default
    if(!empty($branch_url)) {
        header("Location: " . $branch_url);
    } else {
        header("Location: dashboard_enhanced.php");
    }
    exit();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ajwa Garden - Biometric System</title>
    
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome - using multiple fallbacks -->
    <link href="https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <!-- Local fallback -->
    <link href="dist/font-awesome/css/font-awesome.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .main-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .welcome-card {
            background: white;
            border-radius: 25px;
            box-shadow: 0 25px 80px rgba(0,0,0,0.15);
            padding: 50px;
            max-width: 900px;
            width: 100%;
            text-align: center;
        }
        
        .logo-section {
            margin-bottom: 40px;
        }
        
        .logo-section h1 {
            color: #333;
            font-weight: 800;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .logo-section .subtitle {
            color: #666;
            font-size: 1.2rem;
            margin-bottom: 30px;
        }
        
        .user-welcome {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 15px 25px;
            border-radius: 15px;
            margin-bottom: 30px;
            display: inline-block;
        }
        
        .branches-section h2 {
            color: #333;
            font-weight: 600;
            margin-bottom: 30px;
        }
        
        .branches-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }
        
        .branch-option {
            background: #f8f9fa;
            border: 3px solid #e9ecef;
            border-radius: 20px;
            padding: 30px 20px;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        
        .branch-option:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.1);
            border-color: #667eea;
        }
        
        .branch-option.selected {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: #667eea;
            transform: translateY(-8px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.3);
        }
        
        .branch-icon {
            font-size: 3.5rem;
            margin-bottom: 20px;
            color: #667eea;
        }
        
        .branch-option.selected .branch-icon {
            color: white;
        }
        
        .branch-title {
            font-size: 1.4rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .branch-location {
            font-size: 1.1rem;
            opacity: 0.8;
            margin-bottom: 15px;
        }
        
        .branch-badge {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 600;
            display: inline-block;
        }
        
        .branch-option.selected .branch-badge {
            background: rgba(255,255,255,0.2);
            color: white;
        }
        
        .url-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            margin: 30px 0;
        }
        
        .action-section {
            margin-top: 40px;
        }
        
        .btn-access {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 15px 40px;
            border-radius: 30px;
            font-size: 1.2rem;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 10px;
        }
        
        .btn-access:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }
        
        .btn-access:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .quick-actions {
            margin-top: 30px;
            padding-top: 30px;
            border-top: 1px solid #e9ecef;
        }
        
        .quick-actions a {
            color: #667eea;
            text-decoration: none;
            margin: 0 20px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .quick-actions a:hover {
            color: #764ba2;
            text-decoration: none;
        }
        
        /* Fallback for missing Font Awesome icons */
        .fa:before {
            font-family: FontAwesome, sans-serif;
        }

        /* Simple fallback if Font Awesome fails to load */
        .fa-leaf:before { content: "🌿"; font-family: sans-serif; }
        .fa-building:before { content: "🏢"; font-family: sans-serif; }
        .fa-map-marker:before { content: "📍"; font-family: sans-serif; }
        .fa-user:before { content: "👤"; font-family: sans-serif; }
        .fa-link:before { content: "🔗"; font-family: sans-serif; }
        .fa-arrow-right:before { content: "→"; font-family: sans-serif; }
        .fa-sign-in:before { content: "🔑"; font-family: sans-serif; }
        .fa-tachometer:before { content: "📊"; font-family: sans-serif; }
        .fa-cogs:before { content: "⚙️"; font-family: sans-serif; }
        .fa-users:before { content: "👥"; font-family: sans-serif; }
        .fa-sign-out:before { content: "🚪"; font-family: sans-serif; }
        .fa-spinner:before { content: "⏳"; font-family: sans-serif; }

        @media (max-width: 768px) {
            .welcome-card {
                padding: 30px 20px;
            }

            .branches-grid {
                grid-template-columns: 1fr;
            }

            .logo-section h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <div class="welcome-card">
            <div class="logo-section">
                <h1><i class="fa fa-leaf"></i> Ajwa Garden</h1>
                <p class="subtitle">Advanced Biometric Management System</p>
                
                <?php if($is_logged_in): ?>
                <div class="user-welcome">
                    <i class="fa fa-user"></i> Welcome back, <strong><?php echo htmlspecialchars($user_name); ?></strong>
                    <?php if($access_level == 'Admin'): ?>
                    <span class="badge badge-light ml-2">Administrator</span>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>
            
            <div class="branches-section">
                <h2><i class="fa fa-building"></i> Select Your Branch Location</h2>
                
                <form method="POST" id="branchSelectionForm">
                    <div class="branches-grid">
                        <?php foreach($branches as $index => $branch): ?>
                        <div class="branch-option" onclick="selectBranch(<?php echo $branch['branch_id']; ?>)">
                            <div class="branch-icon">
                                <i class="fa fa-building"></i>
                            </div>
                            <div class="branch-title"><?php echo htmlspecialchars($branch['branch_name']); ?></div>
                            <div class="branch-location">
                                <i class="fa fa-map-marker"></i> <?php echo htmlspecialchars($branch['branch_location']); ?>
                            </div>
                            <div class="branch-badge"><?php echo htmlspecialchars($branch['branch_code']); ?></div>
                            <input type="radio" name="branch_id" value="<?php echo $branch['branch_id']; ?>" style="display: none;">
                        </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <div class="url-section">
                        <h5><i class="fa fa-link"></i> Custom Branch URL (Optional)</h5>
                        <input type="url" 
                               class="form-control form-control-lg" 
                               name="branch_url" 
                               id="branchUrl"
                               placeholder="https://your-branch-domain.com/dashboard_enhanced.php"
                               style="border-radius: 15px; border: 2px solid #e9ecef;">
                        <small class="form-text text-muted mt-2">
                            Enter your branch-specific URL, or leave empty to use the default dashboard
                        </small>
                    </div>
                    
                    <div class="action-section">
                        <button type="submit" name="select_branch" class="btn btn-access" id="accessBtn" disabled>
                            <i class="fa fa-arrow-right"></i> Access Selected Branch
                        </button>
                        
                        <?php if(!$is_logged_in): ?>
                        <a href="index.php" class="btn btn-access" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                            <i class="fa fa-sign-in"></i> Login First
                        </a>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
            
            <div class="quick-actions">
                <a href="dashboard_enhanced.php"><i class="fa fa-tachometer"></i> Main Dashboard</a>
                
                <?php if($is_logged_in): ?>
                    <?php if($access_level == 'Admin'): ?>
                    <a href="branch_management.php"><i class="fa fa-cogs"></i> Branch Management</a>
                    <a href="pg_accounts.php"><i class="fa fa-users"></i> Employees</a>
                    <?php endif; ?>
                    <a href="logout.php"><i class="fa fa-sign-out"></i> Logout</a>
                <?php else: ?>
                    <a href="index.php"><i class="fa fa-sign-in"></i> Login</a>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let selectedBranchId = null;
        
        function selectBranch(branchId) {
            // Remove previous selection
            $('.branch-option').removeClass('selected');
            $('input[name="branch_id"]').prop('checked', false);
            
            // Select current branch
            $('input[name="branch_id"][value="' + branchId + '"]').prop('checked', true);
            $('input[name="branch_id"][value="' + branchId + '"]').closest('.branch-option').addClass('selected');
            
            selectedBranchId = branchId;
            
            // Enable access button
            $('#accessBtn').prop('disabled', false);
            
            // Optional: Auto-fill branch URLs
            const branchUrls = {
                '1': 'https://ajwagardenmultan.com/dashboard_enhanced.php',
                '2': 'https://ajwagardenbahawalpur.com/dashboard_enhanced.php',
                '3': 'https://ajwagardensahiwal.com/dashboard_enhanced.php'
            };
            
            // Uncomment to auto-fill URLs
            // $('#branchUrl').val(branchUrls[branchId] || '');
            
            console.log('Selected branch:', branchId);
        }
        
        // Form submission handling
        $('#branchSelectionForm').on('submit', function(e) {
            if(!selectedBranchId) {
                e.preventDefault();
                alert('Please select a branch location first');
                return false;
            }
            
            // Show loading state
            $('#accessBtn').html('<i class="fa fa-spinner fa-spin"></i> Connecting...').prop('disabled', true);
        });
        
        // Auto-select first branch on page load (optional)
        $(document).ready(function() {
            // Uncomment to auto-select first branch
            // selectBranch(<?php echo $branches[0]['branch_id']; ?>);
        });
    </script>
</body>
</html>
