<?php
session_start();
include('proc/config.php');

echo "<h2>🔍 Detailed Branch Filtering Debug</h2>";

// Step 1: Check session
echo "<h3>1. Session Check</h3>";
echo "<p><strong>Current Branch ID in Session:</strong> " . (isset($_SESSION['CURRENT_BRANCH_ID']) ? $_SESSION['CURRENT_BRANCH_ID'] : 'NOT SET') . "</p>";
echo "<p><strong>Current Branch Name in Session:</strong> " . (isset($_SESSION['CURRENT_BRANCH_NAME']) ? $_SESSION['CURRENT_BRANCH_NAME'] : 'NOT SET') . "</p>";

// Step 2: Check what getAccounts.php is actually doing
echo "<h3>2. Simulating getAccounts.php Logic</h3>";

$current_branch_id = isset($_SESSION['CURRENT_BRANCH_ID']) ? $_SESSION['CURRENT_BRANCH_ID'] : 1;
echo "<p><strong>Branch ID being used for filtering:</strong> $current_branch_id</p>";

// Check if branch_id column exists
$check_column = mysql_query("SHOW COLUMNS FROM tbl_personnel LIKE 'branch_id'");
$has_branch_id_column = mysql_num_rows($check_column) > 0;

echo "<p><strong>branch_id column exists:</strong> " . ($has_branch_id_column ? 'YES' : 'NO') . "</p>";

if($has_branch_id_column) {
    $sql = "SELECT p.*, b.branch_name FROM tbl_personnel p LEFT JOIN tbl_branches b ON p.branch_id = b.branch_id WHERE p.branch_id = '$current_branch_id' ORDER BY p.EntryID DESC";
} else {
    $sql = "SELECT p.*, 'No Branch' as branch_name FROM tbl_personnel p ORDER BY p.EntryID DESC";
}

echo "<p><strong>SQL Query being executed:</strong></p>";
echo "<code style='background: #f0f0f0; padding: 10px; display: block;'>$sql</code>";

$result = mysql_query($sql);
if($result) {
    $count = mysql_num_rows($result);
    echo "<p><strong>Number of employees returned:</strong> $count</p>";
    
    if($count > 0) {
        echo "<h4>Employees being returned:</h4>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr style='background: #f0f0f0;'><th>Entry ID</th><th>Employee ID</th><th>Full Name</th><th>Branch ID</th><th>Branch Name</th></tr>";
        
        while($row = mysql_fetch_assoc($result)) {
            echo "<tr>";
            echo "<td>" . $row['EntryID'] . "</td>";
            echo "<td>" . $row['EmployeeID'] . "</td>";
            echo "<td>" . $row['FullName'] . "</td>";
            echo "<td>" . $row['branch_id'] . "</td>";
            echo "<td>" . ($row['branch_name'] ?? 'Unknown') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} else {
    echo "<p style='color: red;'><strong>SQL Error:</strong> " . mysql_error() . "</p>";
}

// Step 3: Check all branches
echo "<h3>3. All Branches in Database</h3>";
$branches_sql = "SELECT * FROM tbl_branches";
$branches_result = mysql_query($branches_sql);

if($branches_result) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr style='background: #f0f0f0;'><th>Branch ID</th><th>Branch Name</th><th>Branch Code</th></tr>";
    
    while($branch = mysql_fetch_assoc($branches_result)) {
        $highlight = ($branch['branch_id'] == $current_branch_id) ? 'style="background: yellow;"' : '';
        echo "<tr $highlight>";
        echo "<td>" . $branch['branch_id'] . "</td>";
        echo "<td>" . $branch['branch_name'] . "</td>";
        echo "<td>" . ($branch['branch_code'] ?? 'N/A') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

// Step 4: Check all employees and their branches
echo "<h3>4. All Employees and Their Branch Assignments</h3>";
$all_emp_sql = "SELECT p.EntryID, p.EmployeeID, p.FullName, p.branch_id, b.branch_name FROM tbl_personnel p LEFT JOIN tbl_branches b ON p.branch_id = b.branch_id ORDER BY p.branch_id, p.EntryID";
$all_emp_result = mysql_query($all_emp_sql);

if($all_emp_result) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr style='background: #f0f0f0;'><th>Entry ID</th><th>Employee ID</th><th>Full Name</th><th>Branch ID</th><th>Branch Name</th><th>Should Show?</th></tr>";
    
    while($emp = mysql_fetch_assoc($all_emp_result)) {
        $should_show = ($emp['branch_id'] == $current_branch_id);
        $highlight = $should_show ? 'style="background: lightgreen;"' : 'style="background: lightcoral;"';
        $show_text = $should_show ? 'YES' : 'NO';
        
        echo "<tr $highlight>";
        echo "<td>" . $emp['EntryID'] . "</td>";
        echo "<td>" . $emp['EmployeeID'] . "</td>";
        echo "<td>" . $emp['FullName'] . "</td>";
        echo "<td>" . ($emp['branch_id'] ?? 'NULL') . "</td>";
        echo "<td>" . ($emp['branch_name'] ?? 'Unknown') . "</td>";
        echo "<td><strong>$show_text</strong></td>";
        echo "</tr>";
    }
    echo "</table>";
}

// Step 5: Test the actual AJAX call
echo "<h3>5. Test Actual AJAX Response</h3>";
echo "<p>This is what the DataTable is actually receiving:</p>";
echo "<iframe src='function/getAccounts.php' width='100%' height='400' style='border: 1px solid #ccc;'></iframe>";

// Step 6: Force session update
echo "<h3>6. Force Session Update</h3>";

if(isset($_POST['force_update'])) {
    $new_branch_id = (int)$_POST['branch_id'];
    $_SESSION['CURRENT_BRANCH_ID'] = $new_branch_id;
    
    // Get branch details
    $branch_sql = "SELECT * FROM tbl_branches WHERE branch_id = '$new_branch_id'";
    $branch_result = mysql_query($branch_sql);
    if($branch_result && mysql_num_rows($branch_result) > 0) {
        $branch_data = mysql_fetch_assoc($branch_result);
        $_SESSION['CURRENT_BRANCH_NAME'] = $branch_data['branch_name'];
        $_SESSION['CURRENT_BRANCH_CODE'] = $branch_data['branch_code'] ?? 'BRANCH' . $new_branch_id;
    }
    
    echo "<p style='color: green;'><strong>✅ Session updated! Reloading...</strong></p>";
    echo "<script>setTimeout(() => window.location.reload(), 1000);</script>";
}

echo "<form method='POST' style='background: #f0f0f0; padding: 15px; margin: 20px 0;'>";
echo "<h4>Force Update Session Branch:</h4>";
echo "<select name='branch_id'>";

// Reset branches result
mysql_data_seek($branches_result, 0);
while($branch = mysql_fetch_assoc($branches_result)) {
    $selected = ($branch['branch_id'] == $current_branch_id) ? 'selected' : '';
    echo "<option value='" . $branch['branch_id'] . "' $selected>" . $branch['branch_name'] . " (ID: " . $branch['branch_id'] . ")</option>";
}

echo "</select>";
echo "<button type='submit' name='force_update' style='margin-left: 10px; padding: 8px 15px;'>Update Session</button>";
echo "</form>";

// Step 7: Check if there's caching
echo "<h3>7. Cache Check</h3>";
echo "<p><strong>Current Time:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<p><strong>Session ID:</strong> " . session_id() . "</p>";
echo "<p><strong>Clear browser cache and try again if the issue persists.</strong></p>";

echo "<hr>";
echo "<p><a href='pg_accounts.php' target='_blank'>🔗 Open Accounts Page</a> | <a href='function/getAccounts.php' target='_blank'>🔗 View Raw AJAX Response</a></p>";

echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; }";
echo "table { border-collapse: collapse; margin: 10px 0; width: 100%; }";
echo "th, td { padding: 8px; border: 1px solid #ccc; text-align: left; font-size: 12px; }";
echo "th { background: #f0f0f0; }";
echo "code { background: #f0f0f0; padding: 10px; display: block; margin: 10px 0; }";
echo "</style>";
?>
