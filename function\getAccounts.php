<?php
// Prevent any output before JSO<PERSON>
ob_start();

// Error handling
error_reporting(0); // Suppress errors for JSON output
ini_set('display_errors', 0);

try {
    session_start();
    include('../proc/config.php');

    // Check if user is logged in
    if(!isset($_SESSION['SESS_USER_ID'])) {
        echo json_encode(array('data' => array(), 'error' => 'User not logged in'));
        exit();
    }

    $data = array();

    // Get current branch ID from session
$current_branch_id = isset($_SESSION['CURRENT_BRANCH_ID']) ? $_SESSION['CURRENT_BRANCH_ID'] : 1;

// Debug: Log session info (remove this in production)
error_log("getAccounts.php - Session User ID: " . (isset($_SESSION['SESS_USER_ID']) ? $_SESSION['SESS_USER_ID'] : 'NOT SET'));
error_log("getAccounts.php - Current Branch ID: " . $current_branch_id);
error_log("getAccounts.php - Current Branch Name: " . (isset($_SESSION['CURRENT_BRANCH_NAME']) ? $_SESSION['CURRENT_BRANCH_NAME'] : 'NOT SET'));

// Check if branch_id column exists in tbl_personnel
$check_column = mysql_query("SHOW COLUMNS FROM tbl_personnel LIKE 'branch_id'");
$has_branch_id_column = mysql_num_rows($check_column) > 0;

if($has_branch_id_column) {
    // Filter employees by current branch only
    $sql = "SELECT p.*, b.branch_name FROM tbl_personnel p LEFT JOIN tbl_branches b ON p.branch_id = b.branch_id WHERE p.branch_id = '$current_branch_id' ORDER BY p.EntryID DESC";
} else {
    // Fallback: show all employees if branch_id column doesn't exist
    $sql = "SELECT p.*, 'No Branch' as branch_name FROM tbl_personnel p ORDER BY p.EntryID DESC";
    error_log("getAccounts.php - WARNING: branch_id column not found, showing all employees");
}

// Debug: Log the SQL query (remove this in production)
error_log("getAccounts.php - SQL Query: " . $sql);

$res = mysql_query($sql);

    if(!$res) {
        throw new Exception('Database query failed: ' . mysql_error());
    }

    if(mysql_num_rows($res) > 0) {
        while($row = mysql_fetch_assoc($res)) {
            // Safely handle date formatting
            $dateHired = '';
            if($row['DateHired'] && $row['DateHired'] != '0000-00-00') {
                $dateHired = date('M d, Y', strtotime($row['DateHired']));
            } else {
                $dateHired = 'Not set';
            }

            // Safely handle null values
            $accessID = htmlspecialchars($row['AccessID'] ?? '');
            $employeeID = htmlspecialchars($row['EmployeeID'] ?? '');
            $fullName = htmlspecialchars($row['FullName'] ?? '');
            $position = htmlspecialchars($row['Position'] ?? '');
            $branchName = htmlspecialchars($row['branch_name'] ?? 'No Branch');
            $entryID = intval($row['EntryID']);

            $data[] = array(
                '<span class="badge badge-primary" data-employee-id="'.$entryID.'">'.$accessID.'</span>',
                '<strong>'.$employeeID.'</strong>',
                '<div class="employee-info">
                    <div class="employee-name">'.$fullName.'</div>
                    <small class="text-muted">ID: '.$employeeID.'</small>
                </div>',
                '<span class="date-badge">'.$dateHired.'</span>',
                '<span class="position-badge">'.$position.'</span>',
                '<span class="branch-badge">'.$branchName.'</span>',
                '<div class="action-buttons" data-employee-id="'.$entryID.'">
                    <button class="btn btn-sm btn-info edit-btn" data-employee-id="'.$entryID.'" onclick="editEmployee('.$entryID.')" title="Edit">
                        <i class="fa fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-danger delete-btn" data-employee-id="'.$entryID.'" onclick="deleteEmployee('.$entryID.')" title="Delete">
                        <i class="fa fa-trash"></i>
                    </button>
                </div>'
            );
        }
    }

    $output = array(
        'aaData' => $data,
        'recordsTotal' => count($data),
        'recordsFiltered' => count($data)
    );

    // Clear any unwanted output
    ob_clean();

    // Set proper headers
    header('Content-Type: application/json');
    echo json_encode($output);

} catch(Exception $e) {
    // Clear any output
    ob_clean();

    // Return error as JSON
    header('Content-Type: application/json');
    echo json_encode(array(
        'aaData' => array(),
        'error' => $e->getMessage()
    ));
}
?>