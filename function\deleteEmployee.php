<?php
include('../proc/config.php');

if(isset($_POST['employee_id'])) {
    $employee_id = clean($_POST['employee_id']);
    $delete_type = isset($_POST['delete_type']) ? clean($_POST['delete_type']) : 'delete';
    $remarks = isset($_POST['remarks']) ? clean($_POST['remarks']) : '';
    
    $response = array();
    
    try {
        // Get employee data before deletion for ID tracking
        $getEmployeeSql = "SELECT EmployeeID, AccessID, FullName FROM tbl_personnel WHERE EntryID='$employee_id'";
        $getEmployeeResult = mysql_query($getEmployeeSql);

        if(!$getEmployeeResult || mysql_num_rows($getEmployeeResult) == 0) {
            throw new Exception('Employee not found');
        }

        $employeeData = mysql_fetch_assoc($getEmployeeResult);
        $deletedEmployeeID = $employeeData['EmployeeID'];
        $deletedBiometricID = $employeeData['AccessID'];
        $deletedEmployeeName = $employeeData['FullName'];

        // Create deleted IDs tracking table if it doesn't exist
        $createTableSql = "CREATE TABLE IF NOT EXISTS tbl_deleted_ids (
            id INT AUTO_INCREMENT PRIMARY KEY,
            employee_id VARCHAR(50),
            biometric_id VARCHAR(50),
            employee_name VARCHAR(255),
            deleted_date DATETIME,
            remarks TEXT
        )";
        mysql_query($createTableSql);

        // Store the deleted IDs to prevent reuse
        $insertDeletedIdSql = "INSERT INTO tbl_deleted_ids (employee_id, biometric_id, employee_name, deleted_date, remarks)
                              VALUES ('$deletedEmployeeID', '$deletedBiometricID', '$deletedEmployeeName', NOW(), '$remarks')";
        mysql_query($insertDeletedIdSql);

        // Permanently delete the employee
        $sql = "DELETE FROM tbl_personnel WHERE EntryID='$employee_id'";
        $result = mysql_query($sql) or die(mysql_error());
        
        if($result) {
            $response['success'] = true;
            $response['message'] = 'Employee status updated successfully';
        } else {
            $response['success'] = false;
            $response['message'] = 'Failed to update employee status';
        }
        
    } catch(Exception $e) {
        $response['success'] = false;
        $response['message'] = 'Error: ' . $e->getMessage();
    }
    
    header('Content-Type: application/json');
    echo json_encode($response);
} else {
    $response = array(
        'success' => false,
        'message' => 'Invalid request'
    );
    header('Content-Type: application/json');
    echo json_encode($response);
}
?>
