<?php
echo "<h2>Direct PHP Test</h2>";

echo "<h3>1. Session Test</h3>";
session_start();
$_SESSION['SESS_USER_ID'] = 1;
$_SESSION['CURRENT_BRANCH_ID'] = 1;
echo "Session started, user ID set to: " . $_SESSION['SESS_USER_ID'] . "<br>";

echo "<h3>2. Config Include Test</h3>";
try {
    include('proc/config.php');
    echo "✅ Config included successfully<br>";
} catch(Exception $e) {
    echo "❌ Config error: " . $e->getMessage() . "<br>";
}

echo "<h3>3. Database Test</h3>";
try {
    $result = mysql_query("SELECT COUNT(*) as count FROM tbl_personnel");
    if($result) {
        $row = mysql_fetch_assoc($result);
        echo "✅ Database query successful, " . $row['count'] . " personnel records<br>";
    } else {
        echo "❌ Database query failed: " . mysql_error() . "<br>";
    }
} catch(Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

echo "<h3>4. Generate IDs Test</h3>";
try {
    include('function/generateEmployeeIDs.php');
    $biometricID = generateUniqueBiometricID();
    $employeeID = generateUniqueEmployeeID();
    echo "✅ ID generation successful<br>";
    echo "Next Biometric ID: $biometricID<br>";
    echo "Next Employee ID: $employeeID<br>";
} catch(Exception $e) {
    echo "❌ ID generation error: " . $e->getMessage() . "<br>";
}

echo "<h3>5. File System Test</h3>";
if(is_dir('temp')) {
    echo "✅ Temp directory exists<br>";
    if(is_writable('temp')) {
        echo "✅ Temp directory is writable<br>";
    } else {
        echo "❌ Temp directory is not writable<br>";
    }
} else {
    echo "❌ Temp directory does not exist<br>";
}

echo "<h3>6. CSV Creation Test</h3>";
$csvContent = "Full Name,Position,Phone Number,Address,Date Hired,Employee ID,Biometric ID,Company,Project Assignment,Time In,Time Out,Schedule\n";
$csvContent .= "John Doe,Manager,+1234567890,123 Main St,2024-01-15,,,ABC Corp,Project A,09:00,17:00,Monday,Tuesday,Wednesday,Thursday,Friday\n";

$csvFile = 'temp/direct_test.csv';
if(file_put_contents($csvFile, $csvContent)) {
    echo "✅ CSV file created: $csvFile<br>";
    echo "File size: " . filesize($csvFile) . " bytes<br>";
    echo "<a href='$csvFile' download>Download Test CSV</a><br>";
} else {
    echo "❌ Failed to create CSV file<br>";
}

echo "<h3>7. JSON Response Test</h3>";
$testResponse = array(
    'success' => true,
    'message' => 'Direct test completed',
    'timestamp' => date('Y-m-d H:i:s')
);

echo "JSON Response: " . json_encode($testResponse) . "<br>";

echo "<h3>8. Error Log Check</h3>";
$errorFiles = glob('temp/*_errors.log');
if(empty($errorFiles)) {
    echo "✅ No error log files found<br>";
} else {
    foreach($errorFiles as $errorFile) {
        echo "Error log found: $errorFile<br>";
        $content = file_get_contents($errorFile);
        if(!empty($content)) {
            echo "<pre>" . htmlspecialchars($content) . "</pre>";
        }
    }
}
?>
