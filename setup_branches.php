<?php
require_once('proc/config.php');

echo "<h2>Setting up Multi-Branch System</h2>";
echo "<p>This will update your database to support multiple branches.</p>";

// Disable strict mode temporarily to avoid issues with existing data
mysql_query("SET SESSION sql_mode = ''");
echo "<p style='color: blue;'>ℹ️ Temporarily disabled strict SQL mode for setup...</p>";

// Check if branches table already exists
$check_sql = "SHOW TABLES LIKE 'tbl_branches'";
$result = mysql_query($check_sql);
$branches_table_exists = mysql_num_rows($result) > 0;

if($branches_table_exists) {
    echo "<p style='color: orange;'>⚠️ Branches table already exists. Checking for missing columns...</p>";
} else {
    echo "<p style='color: blue;'>ℹ️ Creating branches table...</p>";
}

// Array of SQL commands to execute
$sql_commands = array();

// Create branches table if it doesn't exist
if(!$branches_table_exists) {
    $sql_commands[] = "CREATE TABLE IF NOT EXISTS `tbl_branches` (
        `branch_id` int(11) NOT NULL AUTO_INCREMENT,
        `branch_name` varchar(100) NOT NULL,
        `branch_code` varchar(10) NOT NULL,
        `branch_location` varchar(200) NOT NULL,
        `branch_status` enum('Active','Inactive') DEFAULT 'Active',
        `created_date` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`branch_id`),
        UNIQUE KEY `branch_code` (`branch_code`)
    ) ENGINE=InnoDB DEFAULT CHARSET=latin1";

    // Insert the three branches
    $sql_commands[] = "INSERT INTO `tbl_branches` (`branch_name`, `branch_code`, `branch_location`, `branch_status`) VALUES
        ('Ajwa Garden Multan', 'AGM', 'Multan', 'Active'),
        ('Ajwa Garden Bahawalpur', 'AGB', 'Bahawalpur', 'Active'),
        ('Ajwa Garden Sahiwal', 'AGS', 'Sahiwal', 'Active')";
}

// Check and add missing columns
$tables_to_check = array(
    'tbl_personnel' => 'EntryID',
    'tbl_in_out' => 'IDno',
    'tbl_arealist' => 'ID',
    'tb_user' => 'IDno'
);

foreach($tables_to_check as $table => $after_column) {
    // Check if branch_id column exists
    $check_column = "SHOW COLUMNS FROM `$table` LIKE 'branch_id'";
    $result = mysql_query($check_column);
    if(mysql_num_rows($result) == 0) {
        // Special handling for tbl_in_out due to TimeRecord column issues
        if($table == 'tbl_in_out') {
            $sql_commands[] = "ALTER TABLE `$table` ADD COLUMN `branch_id` int(11) DEFAULT 1";
        } else {
            $sql_commands[] = "ALTER TABLE `$table` ADD COLUMN `branch_id` int(11) DEFAULT 1 AFTER `$after_column`";
        }
        echo "<p style='color: blue;'>ℹ️ Adding branch_id column to $table</p>";
    }
}

// Check if assigned_branches column exists in tb_user
$check_assigned = "SHOW COLUMNS FROM `tb_user` LIKE 'assigned_branches'";
$result = mysql_query($check_assigned);
if(mysql_num_rows($result) == 0) {
    $sql_commands[] = "ALTER TABLE `tb_user` ADD COLUMN `assigned_branches` text AFTER `branch_id`";
    echo "<p style='color: blue;'>ℹ️ Adding assigned_branches column to tb_user</p>";
}

// Update existing data
$sql_commands[] = "UPDATE `tbl_personnel` SET `branch_id` = 1 WHERE `branch_id` IS NULL OR `branch_id` = 0";
$sql_commands[] = "UPDATE `tbl_in_out` SET `branch_id` = 1 WHERE `branch_id` IS NULL OR `branch_id` = 0";
$sql_commands[] = "UPDATE `tbl_arealist` SET `branch_id` = 1 WHERE `branch_id` IS NULL OR `branch_id` = 0";
$sql_commands[] = "UPDATE `tb_user` SET `branch_id` = 1, `assigned_branches` = '1,2,3' WHERE `branch_id` IS NULL OR `branch_id` = 0 OR `assigned_branches` IS NULL";

// Create branch-specific access areas (only if branches table was just created)
if(!$branches_table_exists) {
    $sql_commands[] = "INSERT INTO `tbl_arealist` (`AreaName`, `AreaType`, `branch_id`) VALUES
        ('Main Entrance - Multan', 'Entry Point', 1),
        ('Office Area - Multan', 'Work Area', 1),
        ('Garden Area - Multan', 'Outdoor', 1),
        ('Main Entrance - Bahawalpur', 'Entry Point', 2),
        ('Office Area - Bahawalpur', 'Work Area', 2),
        ('Garden Area - Bahawalpur', 'Outdoor', 2),
        ('Main Entrance - Sahiwal', 'Entry Point', 3),
        ('Office Area - Sahiwal', 'Work Area', 3),
        ('Garden Area - Sahiwal', 'Outdoor', 3)";
}

// Execute all commands
$success_count = 0;
$error_count = 0;

foreach($sql_commands as $sql) {
    $result = mysql_query($sql);
    if($result) {
        $success_count++;
        echo "<p style='color: green;'>✅ Command executed successfully</p>";
    } else {
        $error_count++;
        echo "<p style='color: red;'>❌ Error: " . mysql_error() . "</p>";
        echo "<p style='color: red;'>SQL: " . htmlspecialchars($sql) . "</p>";
    }
}

echo "<hr>";
echo "<h3>Setup Summary:</h3>";
echo "<p><strong>Successful operations:</strong> $success_count</p>";
echo "<p><strong>Errors:</strong> $error_count</p>";

if($error_count == 0) {
    echo "<p style='color: green; font-size: 1.2em;'>🎉 <strong>Multi-branch setup completed successfully!</strong></p>";
    echo "<p><a href='dashboard_enhanced.php' class='btn btn-primary'>Go to Enhanced Dashboard</a></p>";
    echo "<p><a href='branch_management.php' class='btn btn-info'>View Branch Management</a></p>";
} else {
    echo "<p style='color: red;'>⚠️ Some errors occurred. Please check the errors above and try again.</p>";
}

// Display current branch information
echo "<hr>";
echo "<h3>Current Branches:</h3>";
$branches_sql = "SELECT * FROM tbl_branches ORDER BY branch_name";
$branches_result = mysql_query($branches_sql);
if($branches_result && mysql_num_rows($branches_result) > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Name</th><th>Code</th><th>Location</th><th>Status</th></tr>";
    while($branch = mysql_fetch_assoc($branches_result)) {
        echo "<tr>";
        echo "<td>" . $branch['branch_id'] . "</td>";
        echo "<td>" . $branch['branch_name'] . "</td>";
        echo "<td>" . $branch['branch_code'] . "</td>";
        echo "<td>" . $branch['branch_location'] . "</td>";
        echo "<td>" . $branch['branch_status'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>No branches found or error retrieving branches.</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.btn { 
    display: inline-block; 
    padding: 10px 20px; 
    margin: 5px; 
    text-decoration: none; 
    border-radius: 5px; 
    color: white; 
}
.btn-primary { background-color: #007bff; }
.btn-info { background-color: #17a2b8; }
table { margin-top: 10px; }
th, td { padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
</style>
