<!DOCTYPE html>
<html>
<head>
    <title>Test Fixed Dashboard API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        button { padding: 10px 15px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h3>Testing Fixed Dashboard API</h3>
    
    <div>
        <button onclick="testAPI()">Test API</button>
        <button onclick="testWithBranchId()">Test with Branch ID 2</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>
    
    <div id="results"></div>

    <script>
        function testAPI() {
            addResult('Testing API without branch ID...', 'info');
            
            fetch('api/dashboard_data.php')
                .then(response => {
                    addResult('Response Status: ' + response.status + ' ' + response.statusText, 
                             response.ok ? 'success' : 'error');
                    
                    if (!response.ok) {
                        return response.text().then(text => {
                            throw new Error('HTTP ' + response.status + ': ' + text);
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    addResult('✅ API Success!', 'success');
                    addResult('Data received:\n' + JSON.stringify(data, null, 2), 'info');
                    
                    // Show key metrics
                    addResult('Key Metrics:\n' +
                             '• Total Personnel: ' + (data.total_personnel || 0) + '\n' +
                             '• Active Personnel: ' + (data.active_personnel || 0) + '\n' +
                             '• Total Users: ' + (data.total_users || 0) + '\n' +
                             '• Current Branch ID: ' + (data.current_branch_id || 'N/A'), 'success');
                })
                .catch(error => {
                    addResult('❌ API Error: ' + error.message, 'error');
                });
        }

        function testWithBranchId() {
            addResult('Testing API with branch_id=2...', 'info');
            
            fetch('api/dashboard_data.php?branch_id=2')
                .then(response => {
                    addResult('Response Status: ' + response.status + ' ' + response.statusText, 
                             response.ok ? 'success' : 'error');
                    
                    if (!response.ok) {
                        return response.text().then(text => {
                            throw new Error('HTTP ' + response.status + ': ' + text);
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    addResult('✅ API Success with Branch ID!', 'success');
                    addResult('Branch ID in response: ' + data.current_branch_id, 'success');
                    addResult('Data received:\n' + JSON.stringify(data, null, 2), 'info');
                })
                .catch(error => {
                    addResult('❌ API Error with Branch ID: ' + error.message, 'error');
                });
        }

        function addResult(message, type) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = 'test-result ' + type;
            
            if (message.includes('\n')) {
                const pre = document.createElement('pre');
                pre.textContent = message;
                div.appendChild(pre);
            } else {
                div.textContent = message;
            }
            
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        // Auto-test on page load
        window.onload = function() {
            addResult('Page loaded. Click "Test API" to begin testing.', 'info');
        };
    </script>
</body>
</html>
