<?php
session_start();

// Set test session
$_SESSION['SESS_USER_ID'] = 1;
$_SESSION['CURRENT_BRANCH_ID'] = 1;

header('Content-Type: application/json');

try {
    // Create sample CSV content
    $csvContent = "Full Name,Position,Phone Number,Address,Date Hired,Employee ID,Biometric ID,Company,Project Assignment,Time In,Time Out,Schedule\n";
    $csvContent .= "<PERSON>,Manager,+1234567890,123 Main St,2024-01-15,,,ABC Corp,Project A,09:00,17:00,Monday,Tuesday,Wednesday,Thursday,Friday\n";
    $csvContent .= "<PERSON>,Assistant,+1234567891,456 Oak Ave,2024-01-16,,,XYZ Inc,Project B,08:30,16:30,Monday,Tuesday,Wednesday,Thursday,Friday\n";
    $csvContent .= "<PERSON>,<PERSON>,+1234567892,789 Pine St,2024-01-17,,,Tech Corp,Project C,10:00,18:00,Monday,Tuesday,Wednesday,Thursday,Friday\n";

    // Ensure temp directory exists
    if(!is_dir('temp')) {
        mkdir('temp', 0777, true);
    }

    $csvFile = 'temp/sample_import_' . date('Y-m-d_H-i-s') . '.csv';
    
    if(file_put_contents($csvFile, $csvContent)) {
        echo json_encode(array(
            'success' => true,
            'message' => 'Sample CSV created successfully',
            'file' => $csvFile,
            'size' => filesize($csvFile)
        ));
    } else {
        throw new Exception('Failed to create CSV file');
    }

} catch(Exception $e) {
    echo json_encode(array(
        'success' => false,
        'message' => $e->getMessage()
    ));
}
?>
