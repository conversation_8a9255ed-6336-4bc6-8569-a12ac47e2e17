<?php
// Debug version of getAccounts.php to see what's being returned
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h3>Debug: getAccounts.php Output</h3>";

echo "<h4>Testing Direct Include:</h4>";
echo "<pre>";
ob_start();
include('function/getAccounts.php');
$output = ob_get_clean();
echo "Raw output:\n";
echo htmlspecialchars($output);
echo "</pre>";

echo "<h4>Testing JSON Validity:</h4>";
$json = json_decode($output, true);
if($json === null) {
    echo "<p style='color: red;'>❌ Invalid JSON! Error: " . json_last_error_msg() . "</p>";
    
    // Try to find the issue
    echo "<h5>Checking for common issues:</h5>";
    if(strpos($output, 'Warning:') !== false) {
        echo "- PHP Warning found in output<br>";
    }
    if(strpos($output, 'Notice:') !== false) {
        echo "- PHP Notice found in output<br>";
    }
    if(strpos($output, 'Error:') !== false) {
        echo "- PHP Error found in output<br>";
    }
    if(strpos($output, '<') !== false) {
        echo "- HTML tags found in output<br>";
    }
} else {
    echo "<p style='color: green;'>✅ Valid JSON!</p>";
    echo "<p>Records found: " . count($json['aaData']) . "</p>";
}

echo "<h4>Testing Database Connection:</h4>";
include('proc/config.php');

$sql = "SELECT COUNT(*) as count FROM tbl_personnel";
$result = mysql_query($sql);

if($result) {
    $row = mysql_fetch_assoc($result);
    echo "<p>✅ Database connection OK. Total records: " . $row['count'] . "</p>";
} else {
    echo "<p style='color: red;'>❌ Database error: " . mysql_error() . "</p>";
}

echo "<h4>Testing Individual Record:</h4>";
$sql = "SELECT * FROM tbl_personnel LIMIT 1";
$result = mysql_query($sql);

if($result && mysql_num_rows($result) > 0) {
    $row = mysql_fetch_assoc($result);
    echo "<p>✅ Sample record found:</p>";
    echo "<pre>";
    print_r($row);
    echo "</pre>";
    
    // Test date formatting
    if($row['DateHired']) {
        echo "<p>Date formatting test: " . date('M d, Y', strtotime($row['DateHired'])) . "</p>";
    }
} else {
    echo "<p style='color: red;'>❌ No records found or database error</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
</style>
