<!DOCTYPE html>
<html>
<head>
    <title>Debug Branch Filtering</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .debug-container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #e9ecef; border-radius: 8px; }
        .success { background: #d4edda; color: #155724; border-color: #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border-color: #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border-color: #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border-color: #ffeaa7; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { padding: 8px; border: 1px solid #ccc; text-align: left; font-size: 12px; }
        th { background: #f0f0f0; font-weight: bold; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; }
    </style>
</head>
<body>
    <div class="debug-container">
        <h3>🔍 Branch Filtering Debug</h3>
        
        <?php
        session_start();
        include('proc/config.php');
        
        // Debug 1: Session Information
        echo "<div class='debug-section info'>";
        echo "<h4>1. Current Session Information</h4>";
        echo "<p><strong>Session ID:</strong> " . session_id() . "</p>";
        echo "<p><strong>User ID:</strong> " . (isset($_SESSION['SESS_USER_ID']) ? $_SESSION['SESS_USER_ID'] : 'Not set') . "</p>";
        echo "<p><strong>User Name:</strong> " . (isset($_SESSION['SESS_USER_FULL_NAME']) ? $_SESSION['SESS_USER_FULL_NAME'] : 'Not set') . "</p>";
        echo "<p><strong>Current Branch ID:</strong> " . (isset($_SESSION['CURRENT_BRANCH_ID']) ? $_SESSION['CURRENT_BRANCH_ID'] : 'Not set') . "</p>";
        echo "<p><strong>Current Branch Name:</strong> " . (isset($_SESSION['CURRENT_BRANCH_NAME']) ? $_SESSION['CURRENT_BRANCH_NAME'] : 'Not set') . "</p>";
        echo "<p><strong>Current Branch Code:</strong> " . (isset($_SESSION['CURRENT_BRANCH_CODE']) ? $_SESSION['CURRENT_BRANCH_CODE'] : 'Not set') . "</p>";
        echo "</div>";
        
        // Debug 2: All Branches
        echo "<div class='debug-section'>";
        echo "<h4>2. All Branches in Database</h4>";
        
        $branches_sql = "SELECT * FROM tbl_branches ORDER BY branch_id";
        $branches_result = mysql_query($branches_sql);
        
        if($branches_result && mysql_num_rows($branches_result) > 0) {
            echo "<table>";
            echo "<tr><th>Branch ID</th><th>Branch Name</th><th>Branch Code</th><th>Current?</th></tr>";
            
            while($branch = mysql_fetch_assoc($branches_result)) {
                $is_current = (isset($_SESSION['CURRENT_BRANCH_ID']) && $branch['branch_id'] == $_SESSION['CURRENT_BRANCH_ID']);
                $current_indicator = $is_current ? '✅ YES' : '❌ No';
                $row_class = $is_current ? 'style="background: #d4edda;"' : '';
                
                echo "<tr $row_class>";
                echo "<td>" . $branch['branch_id'] . "</td>";
                echo "<td>" . $branch['branch_name'] . "</td>";
                echo "<td>" . ($branch['branch_code'] ?? 'N/A') . "</td>";
                echo "<td>$current_indicator</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='error'>❌ No branches found in database</p>";
        }
        echo "</div>";
        
        // Debug 3: All Employees by Branch
        echo "<div class='debug-section'>";
        echo "<h4>3. All Employees by Branch</h4>";
        
        $all_employees_sql = "SELECT p.EntryID, p.EmployeeID, p.AccessID, p.FullName, p.branch_id, b.branch_name 
                             FROM tbl_personnel p 
                             LEFT JOIN tbl_branches b ON p.branch_id = b.branch_id 
                             ORDER BY p.branch_id, p.EntryID";
        $all_employees_result = mysql_query($all_employees_sql);
        
        if($all_employees_result && mysql_num_rows($all_employees_result) > 0) {
            echo "<table>";
            echo "<tr><th>Entry ID</th><th>Employee ID</th><th>Biometric ID</th><th>Full Name</th><th>Branch ID</th><th>Branch Name</th><th>Should Show?</th></tr>";
            
            $current_branch_id = isset($_SESSION['CURRENT_BRANCH_ID']) ? $_SESSION['CURRENT_BRANCH_ID'] : 1;
            
            while($emp = mysql_fetch_assoc($all_employees_result)) {
                $should_show = ($emp['branch_id'] == $current_branch_id);
                $show_indicator = $should_show ? '✅ YES' : '❌ No';
                $row_class = $should_show ? 'style="background: #d4edda;"' : 'style="background: #f8d7da;"';
                
                echo "<tr $row_class>";
                echo "<td>" . $emp['EntryID'] . "</td>";
                echo "<td>" . $emp['EmployeeID'] . "</td>";
                echo "<td>" . $emp['AccessID'] . "</td>";
                echo "<td>" . $emp['FullName'] . "</td>";
                echo "<td>" . $emp['branch_id'] . "</td>";
                echo "<td>" . ($emp['branch_name'] ?? 'Unknown') . "</td>";
                echo "<td>$show_indicator</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='warning'>⚠️ No employees found in database</p>";
        }
        echo "</div>";
        
        // Debug 4: Current Filter Query Test
        echo "<div class='debug-section'>";
        echo "<h4>4. Current Filter Query Test</h4>";
        
        $current_branch_id = isset($_SESSION['CURRENT_BRANCH_ID']) ? $_SESSION['CURRENT_BRANCH_ID'] : 1;
        $filter_sql = "SELECT p.*, b.branch_name FROM tbl_personnel p LEFT JOIN tbl_branches b ON p.branch_id = b.branch_id WHERE p.branch_id = '$current_branch_id' ORDER BY p.EntryID DESC";
        
        echo "<p><strong>Current Filter Query:</strong></p>";
        echo "<div class='code'>$filter_sql</div>";
        
        $filter_result = mysql_query($filter_sql);
        
        if($filter_result) {
            $count = mysql_num_rows($filter_result);
            echo "<p class='success'>✅ Query executed successfully</p>";
            echo "<p><strong>Employees found for current branch:</strong> $count</p>";
            
            if($count > 0) {
                echo "<table>";
                echo "<tr><th>Entry ID</th><th>Employee ID</th><th>Full Name</th><th>Branch Name</th></tr>";
                
                while($emp = mysql_fetch_assoc($filter_result)) {
                    echo "<tr>";
                    echo "<td>" . $emp['EntryID'] . "</td>";
                    echo "<td>" . $emp['EmployeeID'] . "</td>";
                    echo "<td>" . $emp['FullName'] . "</td>";
                    echo "<td>" . ($emp['branch_name'] ?? 'Unknown') . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
        } else {
            echo "<p class='error'>❌ Query failed: " . mysql_error() . "</p>";
        }
        echo "</div>";
        
        // Debug 5: DataTables AJAX Test
        echo "<div class='debug-section'>";
        echo "<h4>5. DataTables AJAX Response Test</h4>";
        
        echo "<p>Test the actual AJAX response that DataTables receives:</p>";
        echo "<iframe src='function/getAccounts.php' width='100%' height='300' style='border: 1px solid #ccc; border-radius: 4px;'></iframe>";
        echo "<p><a href='function/getAccounts.php' target='_blank'>Open in new tab</a></p>";
        echo "</div>";
        
        // Debug 6: Manual Session Update
        echo "<div class='debug-section'>";
        echo "<h4>6. Manual Session Update (for testing)</h4>";
        
        if(isset($_POST['update_branch'])) {
            $new_branch_id = (int)$_POST['new_branch_id'];
            $_SESSION['CURRENT_BRANCH_ID'] = $new_branch_id;
            
            // Get branch name
            $branch_name_sql = "SELECT branch_name, branch_code FROM tbl_branches WHERE branch_id = '$new_branch_id'";
            $branch_name_result = mysql_query($branch_name_sql);
            if($branch_name_result && mysql_num_rows($branch_name_result) > 0) {
                $branch_row = mysql_fetch_assoc($branch_name_result);
                $_SESSION['CURRENT_BRANCH_NAME'] = $branch_row['branch_name'];
                $_SESSION['CURRENT_BRANCH_CODE'] = $branch_row['branch_code'];
            }
            
            echo "<p class='success'>✅ Session updated to branch ID: $new_branch_id</p>";
            echo "<script>setTimeout(() => window.location.reload(), 1000);</script>";
        }
        
        echo "<form method='POST' style='background: #f8f9fa; padding: 15px; border-radius: 4px;'>";
        echo "<h5>Force Branch Change (for debugging):</h5>";
        echo "<select name='new_branch_id' required>";
        
        // Reset result pointer
        mysql_data_seek($branches_result, 0);
        while($branch = mysql_fetch_assoc($branches_result)) {
            $selected = (isset($_SESSION['CURRENT_BRANCH_ID']) && $branch['branch_id'] == $_SESSION['CURRENT_BRANCH_ID']) ? 'selected' : '';
            echo "<option value='" . $branch['branch_id'] . "' $selected>" . $branch['branch_name'] . " (ID: " . $branch['branch_id'] . ")</option>";
        }
        
        echo "</select>";
        echo "<button type='submit' name='update_branch'>Update Session</button>";
        echo "<small style='display: block; margin-top: 5px;'>This will force update your session to the selected branch</small>";
        echo "</form>";
        echo "</div>";
        
        // Debug 7: Recommendations
        echo "<div class='debug-section warning'>";
        echo "<h4>7. Troubleshooting Steps</h4>";
        echo "<ol>";
        echo "<li><strong>Check Session:</strong> Verify CURRENT_BRANCH_ID is set correctly</li>";
        echo "<li><strong>Check Database:</strong> Ensure employees have correct branch_id values</li>";
        echo "<li><strong>Clear Cache:</strong> Refresh the accounts page and clear browser cache</li>";
        echo "<li><strong>Re-login:</strong> Logout and login again with correct branch</li>";
        echo "<li><strong>Check Query:</strong> Verify the filter query is working correctly</li>";
        echo "</ol>";
        echo "</div>";
        ?>
    </div>
</body>
</html>
