<?php
// Test the permanent deletion system and ID tracking
include('proc/config.php');

echo "<h3>Testing Permanent Deletion System</h3>";

// Check if deleted IDs table exists
echo "<h4>Database Setup Check:</h4>";
$checkTable = mysql_query("SHOW TABLES LIKE 'tbl_deleted_ids'");
if(mysql_num_rows($checkTable) > 0) {
    echo "<p>✅ tbl_deleted_ids table exists</p>";
    
    // Show table structure
    $describe = mysql_query("DESCRIBE tbl_deleted_ids");
    if($describe) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'><th>Field</th><th>Type</th><th>Null</th><th>Key</th></tr>";
        while($row = mysql_fetch_assoc($describe)) {
            echo "<tr>";
            echo "<td>" . $row['Field'] . "</td>";
            echo "<td>" . $row['Type'] . "</td>";
            echo "<td>" . $row['Null'] . "</td>";
            echo "<td>" . $row['Key'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} else {
    echo "<p>❌ tbl_deleted_ids table does not exist</p>";
    echo "<p>The table will be created automatically when the first employee is deleted.</p>";
}

// Show current employees
echo "<h4>Current Active Employees:</h4>";
$sql = "SELECT EntryID, EmployeeID, AccessID, FullName FROM tbl_personnel ORDER BY EntryID";
$result = mysql_query($sql);

if($result && mysql_num_rows($result) > 0) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr style='background: #f0f0f0;'><th>Entry ID</th><th>Employee ID</th><th>Biometric ID</th><th>Full Name</th></tr>";
    
    while($row = mysql_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['EntryID'] . "</td>";
        echo "<td>" . $row['EmployeeID'] . "</td>";
        echo "<td>" . $row['AccessID'] . "</td>";
        echo "<td>" . $row['FullName'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No active employees found.</p>";
}

// Show deleted IDs if any
echo "<h4>Deleted/Reserved IDs:</h4>";
$deletedSql = "SELECT * FROM tbl_deleted_ids ORDER BY deleted_date DESC";
$deletedResult = mysql_query($deletedSql);

if($deletedResult && mysql_num_rows($deletedResult) > 0) {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr style='background: #f0f0f0;'><th>Employee ID</th><th>Biometric ID</th><th>Employee Name</th><th>Deleted Date</th><th>Remarks</th></tr>";
    
    while($row = mysql_fetch_assoc($deletedResult)) {
        echo "<tr>";
        echo "<td>" . $row['employee_id'] . "</td>";
        echo "<td>" . $row['biometric_id'] . "</td>";
        echo "<td>" . $row['employee_name'] . "</td>";
        echo "<td>" . $row['deleted_date'] . "</td>";
        echo "<td>" . $row['remarks'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No deleted IDs found.</p>";
}

// Test ID generation
echo "<h4>ID Generation Test:</h4>";
include('function/generateEmployeeIDs.php');

echo "<p><strong>Next Biometric ID:</strong> " . generateUniqueBiometricID() . "</p>";
echo "<p><strong>Next Employee ID:</strong> " . generateUniqueEmployeeID() . "</p>";

// Test uniqueness check
echo "<h4>ID Uniqueness Test:</h4>";
$testBiometricID = 'B001';
$testEmployeeID = '1001';

echo "<p>Testing Biometric ID '$testBiometricID': " . (isBiometricIDUnique($testBiometricID) ? '✅ Available' : '❌ Already used/reserved') . "</p>";
echo "<p>Testing Employee ID '$testEmployeeID': " . (isEmployeeIDUnique($testEmployeeID) ? '✅ Available' : '❌ Already used/reserved') . "</p>";

// Show all used IDs (active + deleted)
echo "<h4>All Used IDs Summary:</h4>";

// Get all biometric IDs
$allBiometricIDs = array();

// From active personnel
$activeBioSql = "SELECT AccessID FROM tbl_personnel WHERE AccessID REGEXP '^B[0-9]+$'";
$activeBioResult = mysql_query($activeBioSql);
if($activeBioResult) {
    while($row = mysql_fetch_assoc($activeBioResult)) {
        $allBiometricIDs[] = $row['AccessID'] . ' (Active)';
    }
}

// From deleted records
$deletedBioSql = "SELECT biometric_id FROM tbl_deleted_ids WHERE biometric_id REGEXP '^B[0-9]+$'";
$deletedBioResult = mysql_query($deletedBioSql);
if($deletedBioResult) {
    while($row = mysql_fetch_assoc($deletedBioResult)) {
        $allBiometricIDs[] = $row['biometric_id'] . ' (Deleted/Reserved)';
    }
}

echo "<p><strong>All Biometric IDs:</strong> " . (empty($allBiometricIDs) ? 'None' : implode(', ', $allBiometricIDs)) . "</p>";

// Get all employee IDs
$allEmployeeIDs = array();

// From active personnel
$activeEmpSql = "SELECT EmployeeID FROM tbl_personnel WHERE EmployeeID REGEXP '^[0-9]+$' AND CAST(EmployeeID AS UNSIGNED) >= 1001";
$activeEmpResult = mysql_query($activeEmpSql);
if($activeEmpResult) {
    while($row = mysql_fetch_assoc($activeEmpResult)) {
        $allEmployeeIDs[] = $row['EmployeeID'] . ' (Active)';
    }
}

// From deleted records
$deletedEmpSql = "SELECT employee_id FROM tbl_deleted_ids WHERE employee_id REGEXP '^[0-9]+$' AND CAST(employee_id AS UNSIGNED) >= 1001";
$deletedEmpResult = mysql_query($deletedEmpSql);
if($deletedEmpResult) {
    while($row = mysql_fetch_assoc($deletedEmpResult)) {
        $allEmployeeIDs[] = $row['employee_id'] . ' (Deleted/Reserved)';
    }
}

echo "<p><strong>All Employee IDs:</strong> " . (empty($allEmployeeIDs) ? 'None' : implode(', ', $allEmployeeIDs)) . "</p>";

// Manual test deletion (for testing purposes)
echo "<h4>Manual Test (For Testing Only):</h4>";
if(isset($_POST['test_delete']) && isset($_POST['test_employee_id'])) {
    $testEmployeeId = clean($_POST['test_employee_id']);
    $testRemarks = clean($_POST['test_remarks']);
    
    // Simulate the deletion process
    $_POST['employee_id'] = $testEmployeeId;
    $_POST['delete_type'] = 'permanent';
    $_POST['remarks'] = $testRemarks;
    
    echo "<p><strong>Test deletion result:</strong></p>";
    ob_start();
    include('function/deleteEmployee.php');
    $result = ob_get_clean();
    echo "<pre>" . htmlspecialchars($result) . "</pre>";
}
?>

<form method="POST" style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
    <h5>Test Employee Deletion (Use with caution!)</h5>
    <label>Employee Entry ID to Delete:</label>
    <input type="number" name="test_employee_id" required style="margin: 5px;">
    <br>
    <label>Remarks:</label>
    <input type="text" name="test_remarks" placeholder="Test deletion" required style="margin: 5px;">
    <br>
    <button type="submit" name="test_delete" style="background: #dc3545; color: white; padding: 8px 15px; border: none; border-radius: 4px; margin: 5px;">
        Test Delete Employee
    </button>
    <small style="color: red; display: block;">⚠️ This will permanently delete the employee!</small>
</form>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { border-collapse: collapse; margin: 10px 0; }
th, td { padding: 8px; border: 1px solid #ccc; text-align: left; }
th { background: #f0f0f0; }
</style>
