<?php
include('auth.php');
require_once('proc/config.php');

// Get current branch
$current_branch_id = isset($_SESSION['CURRENT_BRANCH_ID']) ? $_SESSION['CURRENT_BRANCH_ID'] : 1;

// Get branches for dropdown
$branches = array();
$sql = "SELECT * FROM tbl_branches ORDER BY branch_name";
$result = mysql_query($sql);
if($result && mysql_num_rows($result) > 0) {
    while($row = mysql_fetch_assoc($result)) {
        $branches[] = $row;
    }
}

// Get current branch info
$current_branch = array('branch_name' => 'All Branches', 'branch_location' => '');
foreach($branches as $branch) {
    if($branch['branch_id'] == $current_branch_id) {
        $current_branch = $branch;
        break;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Comprehensive Reports - Biometric System</title>
    
    <link href="assets/css/bootstrap.css" rel="stylesheet">
    <!-- Font Awesome CDN for better performance -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet" crossorigin="anonymous">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Preload critical fonts -->
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/webfonts/fa-solid-900.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/webfonts/fa-regular-400.woff2" as="font" type="font/woff2" crossorigin>
    
    <style>
        /* Font loading optimization */
        @font-face {
            font-family: 'FontAwesome';
            font-display: swap; /* Use fallback font while loading */
        }

        /* Fallback for Font Awesome icons */
        .fa, .fas, .far, .fab {
            font-family: 'Font Awesome 6 Free', 'Font Awesome 5 Free', 'FontAwesome', sans-serif;
            font-display: swap;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }
        
        .reports-container {
            padding: 20px;
            margin-top: 70px;
        }
        
        .glass-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        
        .glass-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }
        
        .report-card {
            text-align: center;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        
        .report-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.8;
        }
        
        .report-title {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .report-description {
            color: #666;
            font-size: 0.9rem;
        }
        
        .welcome-header {
            color: white;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .welcome-header h1 {
            font-size: 2.5rem;
            font-weight: 300;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .filter-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .btn-report {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .btn-report:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
        
        .report-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .navbar-brand {
            font-weight: bold;
            font-size: 1.3rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-inverse navbar-fixed-top" role="navigation">
        <div class="container-fluid">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-collapse">
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="dashboard_enhanced.php">
                    <i class="fa fa-fingerprint"></i> Biometric Reports
                </a>
            </div>
            <div class="navbar-collapse collapse">
                <ul class="nav navbar-nav navbar-right">
                    <li><a href="dashboard_enhanced.php"><i class="fa fa-dashboard"></i> Dashboard</a></li>
                    <?php if($_SESSION['ACCESSLEVEL']=='Admin') { ?>
                        <li><a href="branch_management.php"><i class="fa fa-building"></i> Branches</a></li>
                        <li><a href="pg_accounts.php"><i class="fa fa-users"></i> Employees</a></li>
                        <li><a href="pg_settings.php"><i class="fa fa-cog"></i> Settings</a></li>
                    <?php } ?>
                    <li><a href="logout.php"><i class="fa fa-sign-out"></i> Logout</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="reports-container">
        <!-- Welcome Header -->
        <div class="welcome-header">
            <h1><i class="fa fa-bar-chart"></i> Comprehensive Reports</h1>
            <p>Generate detailed reports for <?php echo $current_branch['branch_name']; ?></p>
            <p style="font-size: 1rem; opacity: 0.8;">
                <i class="fa fa-map-marker"></i> <?php echo $current_branch['branch_location']; ?>
            </p>
        </div>

        <div class="container-fluid">
            <!-- Quick Filters -->
            <div class="glass-card">
                <h3><i class="fa fa-filter"></i> Report Filters</h3>
                <div class="filter-section">
                    <div class="row">
                        <div class="col-md-3">
                            <label>Branch:</label>
                            <select class="form-control" id="branch-filter">
                                <option value="all">All Branches</option>
                                <?php foreach($branches as $branch): ?>
                                    <option value="<?php echo $branch['branch_id']; ?>" 
                                            <?php echo ($branch['branch_id'] == $current_branch_id) ? 'selected' : ''; ?>>
                                        <?php echo $branch['branch_name']; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label>Date From:</label>
                            <input type="date" class="form-control" id="date-from" value="<?php echo date('Y-m-01'); ?>">
                        </div>
                        <div class="col-md-3">
                            <label>Date To:</label>
                            <input type="date" class="form-control" id="date-to" value="<?php echo date('Y-m-d'); ?>">
                        </div>
                        <div class="col-md-3">
                            <label>Report Format:</label>
                            <select class="form-control" id="format-filter">
                                <option value="html">HTML View</option>
                                <option value="pdf">PDF Download</option>
                                <option value="excel">Excel Download</option>
                                <option value="csv">CSV Download</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Report Types -->
            <div class="glass-card">
                <h3><i class="fa fa-file-text"></i> Available Reports</h3>
                <div class="report-grid">
                    <!-- Attendance Reports -->
                    <div class="glass-card report-card" onclick="generateReport('attendance-summary')">
                        <div class="report-icon text-primary">
                            <i class="fa fa-clock-o"></i>
                        </div>
                        <div class="report-title">Attendance Summary</div>
                        <div class="report-description">
                            Daily, weekly, and monthly attendance summaries with statistics
                        </div>
                    </div>

                    <div class="glass-card report-card" onclick="generateReport('attendance-detailed')">
                        <div class="report-icon text-success">
                            <i class="fa fa-list-alt"></i>
                        </div>
                        <div class="report-title">Detailed Attendance</div>
                        <div class="report-description">
                            Complete attendance logs with in/out times for all employees
                        </div>
                    </div>

                    <div class="glass-card report-card" onclick="generateReport('late-arrivals')">
                        <div class="report-icon text-warning">
                            <i class="fa fa-exclamation-triangle"></i>
                        </div>
                        <div class="report-title">Late Arrivals</div>
                        <div class="report-description">
                            Employees who arrived late with time differences
                        </div>
                    </div>

                    <!-- Employee Reports -->
                    <div class="glass-card report-card" onclick="generateReport('employee-list')">
                        <div class="report-icon text-info">
                            <i class="fa fa-users"></i>
                        </div>
                        <div class="report-title">Employee Directory</div>
                        <div class="report-description">
                            Complete list of employees with contact details and positions
                        </div>
                    </div>

                    <div class="glass-card report-card" onclick="generateReport('employee-performance')">
                        <div class="report-icon text-primary">
                            <i class="fa fa-trophy"></i>
                        </div>
                        <div class="report-title">Employee Performance</div>
                        <div class="report-description">
                            Attendance performance metrics and punctuality analysis
                        </div>
                    </div>

                    <div class="glass-card report-card" onclick="generateReport('department-wise')">
                        <div class="report-icon text-success">
                            <i class="fa fa-building-o"></i>
                        </div>
                        <div class="report-title">Department Analysis</div>
                        <div class="report-description">
                            Department-wise attendance and employee distribution
                        </div>
                    </div>

                    <!-- Branch Reports -->
                    <div class="glass-card report-card" onclick="generateReport('branch-comparison')">
                        <div class="report-icon text-warning">
                            <i class="fa fa-balance-scale"></i>
                        </div>
                        <div class="report-title">Branch Comparison</div>
                        <div class="report-description">
                            Compare attendance and performance across all branches
                        </div>
                    </div>

                    <div class="glass-card report-card" onclick="generateReport('access-logs')">
                        <div class="report-icon text-danger">
                            <i class="fa fa-key"></i>
                        </div>
                        <div class="report-title">Access Logs</div>
                        <div class="report-description">
                            Detailed access logs with entry/exit times and locations
                        </div>
                    </div>

                    <!-- Analytics Reports -->
                    <div class="glass-card report-card" onclick="generateReport('monthly-analytics')">
                        <div class="report-icon text-info">
                            <i class="fa fa-line-chart"></i>
                        </div>
                        <div class="report-title">Monthly Analytics</div>
                        <div class="report-description">
                            Monthly trends, patterns, and statistical analysis
                        </div>
                    </div>

                    <div class="glass-card report-card" onclick="generateReport('attendance-status')">
                        <div class="report-icon text-success">
                            <i class="fa fa-check-circle"></i>
                        </div>
                        <div class="report-title">Attendance Status</div>
                        <div class="report-description">
                            Daily attendance status: Present, Absent, or Not Punched for each employee
                        </div>
                    </div>
                </div>
            </div>

            <!-- Report Preview Area -->
            <div class="glass-card" id="report-preview" style="display: none;">
                <div class="row">
                    <div class="col-md-10">
                        <h3 id="report-title"><i class="fa fa-file-text"></i> Report Preview</h3>
                    </div>
                    <div class="col-md-2 text-right">
                        <button class="btn btn-report" onclick="downloadReport()">
                            <i class="fa fa-download"></i> Download
                        </button>
                    </div>
                </div>
                <div id="report-content">
                    <!-- Report content will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="assets/js/jquery.js"></script>
    <script src="assets/js/bootstrap.min.js"></script>
    <script>
        let currentReportType = '';
        
        function generateReport(reportType) {
            currentReportType = reportType;
            const branch = $('#branch-filter').val();
            const dateFrom = $('#date-from').val();
            const dateTo = $('#date-to').val();
            const format = $('#format-filter').val();
            
            // Show loading
            $('#report-preview').show();
            $('#report-content').html('<div class="text-center"><i class="fa fa-spinner fa-spin fa-3x"></i><p>Generating report...</p></div>');
            
            // Update report title
            const reportTitles = {
                'attendance-summary': 'Attendance Summary Report',
                'attendance-detailed': 'Detailed Attendance Report',
                'late-arrivals': 'Late Arrivals Report',
                'employee-list': 'Employee Directory Report',
                'employee-performance': 'Employee Performance Report',
                'department-wise': 'Department Analysis Report',
                'branch-comparison': 'Branch Comparison Report',
                'access-logs': 'Access Logs Report',
                'monthly-analytics': 'Monthly Analytics Report',
                'attendance-status': 'Employee Attendance Status Report'
            };
            
            $('#report-title').html('<i class="fa fa-file-text"></i> ' + reportTitles[reportType]);
            
            // Generate report via AJAX
            $.ajax({
                url: 'api/generate_report.php',
                method: 'POST',
                data: {
                    report_type: reportType,
                    branch_id: branch,
                    date_from: dateFrom,
                    date_to: dateTo,
                    format: format
                },
                success: function(response) {
                    if(format === 'html') {
                        $('#report-content').html(response);
                    } else {
                        // Handle file downloads
                        window.location.href = 'api/generate_report.php?' + $.param({
                            report_type: reportType,
                            branch_id: branch,
                            date_from: dateFrom,
                            date_to: dateTo,
                            format: format
                        });
                        $('#report-content').html('<div class="alert alert-success"><i class="fa fa-check"></i> Report download started!</div>');
                    }
                },
                error: function(xhr, status, error) {
                    $('#report-content').html('<div class="alert alert-danger"><i class="fa fa-exclamation-triangle"></i> Error generating report: ' + error + '</div>');
                }
            });
        }
        
        function downloadReport() {
            const branch = $('#branch-filter').val();
            const dateFrom = $('#date-from').val();
            const dateTo = $('#date-to').val();
            const format = $('#format-filter').val();
            
            window.location.href = 'api/generate_report.php?' + $.param({
                report_type: currentReportType,
                branch_id: branch,
                date_from: dateFrom,
                date_to: dateTo,
                format: format
            });
        }
        
        // Auto-scroll to report when generated
        function scrollToReport() {
            $('html, body').animate({
                scrollTop: $("#report-preview").offset().top - 100
            }, 1000);
        }
    </script>
</body>
</html>
