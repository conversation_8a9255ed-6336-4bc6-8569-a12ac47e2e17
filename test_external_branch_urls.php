<!DOCTYPE html>
<html>
<head>
    <title>Test External Branch URLs</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .test-container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin: 30px 0; padding: 20px; border: 1px solid #e9ecef; border-radius: 8px; }
        .success { background: #d4edda; color: #155724; border-color: #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border-color: #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border-color: #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border-color: #ffeaa7; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { padding: 12px; border: 1px solid #ccc; text-align: left; }
        th { background: #f0f0f0; font-weight: bold; }
        .btn { padding: 10px 15px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; text-decoration: none; display: inline-block; }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #218838; }
        .btn-danger { background: #dc3545; }
        .btn-danger:hover { background: #c82333; }
        .url-display { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; word-break: break-all; }
    </style>
</head>
<body>
    <div class="test-container">
        <h3>🌐 External Branch URLs System Test</h3>
        
        <?php
        include('proc/config.php');
        
        // Test 1: Check if branch_url column exists
        echo "<div class='test-section'>";
        echo "<h4>1. Database Structure Check</h4>";
        
        $check_column = mysql_query("SHOW COLUMNS FROM tbl_branches LIKE 'branch_url'");
        if($check_column && mysql_num_rows($check_column) > 0) {
            echo "<p class='success'>✅ branch_url column exists in tbl_branches table</p>";
        } else {
            echo "<p class='error'>❌ branch_url column missing from tbl_branches table</p>";
            echo "<p class='warning'>⚠️ Run <a href='update_branches_table.php'>update_branches_table.php</a> to add the column</p>";
        }
        echo "</div>";
        
        // Test 2: Show current branches with URLs
        echo "<div class='test-section'>";
        echo "<h4>2. Current Branches Configuration</h4>";
        
        $branches_sql = "SELECT * FROM tbl_branches ORDER BY branch_id";
        $branches_result = mysql_query($branches_sql);
        
        if($branches_result && mysql_num_rows($branches_result) > 0) {
            echo "<table>";
            echo "<tr><th>ID</th><th>Branch Name</th><th>Code</th><th>URL</th><th>Type</th><th>Test</th></tr>";
            
            while($branch = mysql_fetch_assoc($branches_result)) {
                $url = $branch['branch_url'] ?: 'Not set';
                $is_external = (strpos($url, 'http') === 0);
                $url_type = $is_external ? 'External' : 'Local';
                $url_class = $is_external ? 'warning' : 'info';
                
                echo "<tr>";
                echo "<td>" . $branch['branch_id'] . "</td>";
                echo "<td>" . $branch['branch_name'] . "</td>";
                echo "<td>" . $branch['branch_code'] . "</td>";
                echo "<td><div class='url-display'>" . $url . "</div></td>";
                echo "<td><span class='$url_class' style='padding: 4px 8px; border-radius: 4px;'>$url_type</span></td>";
                echo "<td>";
                if($url !== 'Not set') {
                    echo "<a href='$url' target='_blank' class='btn btn-success'>Test URL</a>";
                }
                echo "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='error'>❌ No branches found</p>";
        }
        echo "</div>";
        
        // Test 3: Login simulation test
        echo "<div class='test-section'>";
        echo "<h4>3. Login Response Simulation</h4>";
        
        if(isset($_POST['test_login'])) {
            $test_branch_id = (int)$_POST['test_branch_id'];
            
            // Simulate login processing
            $branch_sql = "SELECT * FROM tbl_branches WHERE branch_id = '$test_branch_id' LIMIT 1";
            $branch_result = mysql_query($branch_sql);
            
            if($branch_result && mysql_num_rows($branch_result) > 0) {
                $branch_info = mysql_fetch_assoc($branch_result);
                
                $response = array(
                    'success' => true,
                    'branch_id' => $branch_info['branch_id'],
                    'branch_name' => $branch_info['branch_name'],
                    'branch_code' => $branch_info['branch_code'],
                    'branch_url' => $branch_info['branch_url']
                );
                
                echo "<p class='success'>✅ Login simulation successful!</p>";
                echo "<p><strong>Response JSON:</strong></p>";
                echo "<pre style='background: #f8f9fa; padding: 15px; border-radius: 4px;'>" . json_encode($response, JSON_PRETTY_PRINT) . "</pre>";
                echo "<p><strong>Redirect URL:</strong> <span class='url-display'>" . $branch_info['branch_url'] . "</span></p>";
                
                if(strpos($branch_info['branch_url'], 'http') === 0) {
                    echo "<p class='warning'>⚠️ This is an external URL - user will be redirected to a different domain</p>";
                } else {
                    echo "<p class='info'>ℹ️ This is a local URL - user will stay on current domain</p>";
                }
            } else {
                echo "<p class='error'>❌ Branch not found</p>";
            }
        }
        
        echo "<form method='POST' style='background: #f8f9fa; padding: 15px; border-radius: 4px;'>";
        echo "<h5>Simulate Login for Branch:</h5>";
        echo "<select name='test_branch_id' required>";
        
        $branches_result = mysql_query($branches_sql);
        if($branches_result) {
            mysql_data_seek($branches_result, 0); // Reset result pointer
            while($branch = mysql_fetch_assoc($branches_result)) {
                echo "<option value='" . $branch['branch_id'] . "'>" . $branch['branch_name'] . " (" . $branch['branch_code'] . ")</option>";
            }
        }
        
        echo "</select>";
        echo "<button type='submit' name='test_login' class='btn'>Simulate Login</button>";
        echo "</form>";
        echo "</div>";
        
        // Test 4: JavaScript redirect test
        echo "<div class='test-section'>";
        echo "<h4>4. JavaScript Redirect Test</h4>";
        echo "<p>Test the actual JavaScript redirect functionality:</p>";
        
        echo "<div id='redirect-test-area'>";
        echo "<button onclick='testRedirect()' class='btn'>Test Redirect Logic</button>";
        echo "<div id='redirect-result' style='margin-top: 10px;'></div>";
        echo "</div>";
        
        echo "<script>";
        echo "function testRedirect() {";
        echo "  const testResponse = {";
        echo "    success: true,";
        echo "    branch_name: 'AJ Wagar Garden Branch',";
        echo "    branch_url: 'https://ajwagardenbwp.com/dashboard_enhanced.php'";
        echo "  };";
        echo "  ";
        echo "  let branchName = 'Selected Branch';";
        echo "  let branchUrl = 'dashboard_enhanced.php';";
        echo "  ";
        echo "  if(testResponse && typeof testResponse === 'object') {";
        echo "    branchName = testResponse.branch_name || 'Selected Branch';";
        echo "    branchUrl = testResponse.branch_url || 'dashboard_enhanced.php';";
        echo "  }";
        echo "  ";
        echo "  document.getElementById('redirect-result').innerHTML = ";
        echo "    '<p class=\"success\">✅ Redirect would go to: <strong>' + branchName + '</strong></p>' +";
        echo "    '<p>URL: <span class=\"url-display\">' + branchUrl + '</span></p>' +";
        echo "    '<p><a href=\"' + branchUrl + '\" target=\"_blank\" class=\"btn btn-success\">Test This URL</a></p>';";
        echo "}";
        echo "</script>";
        echo "</div>";
        
        // Test 5: Add/Edit branch URLs
        echo "<div class='test-section'>";
        echo "<h4>5. Manage Branch URLs</h4>";
        
        if(isset($_POST['update_branch_url'])) {
            $branch_id = (int)$_POST['branch_id'];
            $new_url = clean($_POST['new_url']);
            
            $update_sql = "UPDATE tbl_branches SET branch_url = '$new_url' WHERE branch_id = '$branch_id'";
            if(mysql_query($update_sql)) {
                echo "<p class='success'>✅ Branch URL updated successfully!</p>";
                echo "<script>setTimeout(() => window.location.reload(), 1000);</script>";
            } else {
                echo "<p class='error'>❌ Error updating branch URL: " . mysql_error() . "</p>";
            }
        }
        
        echo "<form method='POST' style='background: #f8f9fa; padding: 15px; border-radius: 4px;'>";
        echo "<h5>Update Branch URL:</h5>";
        echo "<div style='margin: 10px 0;'>";
        echo "<label>Select Branch:</label><br>";
        echo "<select name='branch_id' required style='width: 300px; padding: 8px;'>";
        
        $branches_result = mysql_query($branches_sql);
        if($branches_result) {
            mysql_data_seek($branches_result, 0);
            while($branch = mysql_fetch_assoc($branches_result)) {
                echo "<option value='" . $branch['branch_id'] . "'>" . $branch['branch_name'] . "</option>";
            }
        }
        
        echo "</select>";
        echo "</div>";
        echo "<div style='margin: 10px 0;'>";
        echo "<label>New URL:</label><br>";
        echo "<input type='url' name='new_url' required style='width: 500px; padding: 8px;' placeholder='https://yourdomain.com/dashboard_enhanced.php'>";
        echo "</div>";
        echo "<button type='submit' name='update_branch_url' class='btn'>Update URL</button>";
        echo "</form>";
        echo "</div>";
        
        // Test 6: Quick setup for your example
        echo "<div class='test-section'>";
        echo "<h4>6. Quick Setup - Your Example</h4>";
        echo "<p>Set up the AJ Wagar Garden branch with your URL:</p>";
        
        if(isset($_POST['quick_setup'])) {
            $setup_sql = "INSERT INTO tbl_branches (branch_id, branch_name, branch_code, branch_url) 
                         VALUES (2, 'AJ Wagar Garden Branch', 'AJWAGAR', 'https://ajwagardenbwp.com/dashboard_enhanced.php')
                         ON DUPLICATE KEY UPDATE 
                         branch_name = VALUES(branch_name),
                         branch_code = VALUES(branch_code),
                         branch_url = VALUES(branch_url)";
            
            if(mysql_query($setup_sql)) {
                echo "<p class='success'>✅ AJ Wagar Garden branch configured successfully!</p>";
                echo "<script>setTimeout(() => window.location.reload(), 1000);</script>";
            } else {
                echo "<p class='error'>❌ Error setting up branch: " . mysql_error() . "</p>";
            }
        }
        
        echo "<form method='POST'>";
        echo "<button type='submit' name='quick_setup' class='btn btn-success'>Setup AJ Wagar Garden Branch</button>";
        echo "</form>";
        echo "<p class='info'>This will create/update branch ID 2 with:<br>";
        echo "• Name: AJ Wagar Garden Branch<br>";
        echo "• Code: AJWAGAR<br>";
        echo "• URL: https://ajwagardenbwp.com/dashboard_enhanced.php</p>";
        echo "</div>";
        ?>
        
        <!-- Instructions -->
        <div class="test-section info">
            <h4>📋 How to Use External Branch URLs</h4>
            <ol>
                <li><strong>Setup Branches:</strong> Run <code>update_branches_table.php</code> to add the branch_url column</li>
                <li><strong>Configure URLs:</strong> Set each branch's URL (can be external domains)</li>
                <li><strong>Login Process:</strong> Users select branch → login → redirect to branch URL</li>
                <li><strong>External Domains:</strong> Each branch can have its own domain/subdomain</li>
                <li><strong>Local Branches:</strong> Use relative URLs like <code>dashboard_enhanced.php</code></li>
            </ol>
            
            <h5>Example URLs:</h5>
            <ul>
                <li><code>dashboard_enhanced.php</code> - Local main branch</li>
                <li><code>https://ajwagardenbwp.com/dashboard_enhanced.php</code> - External branch</li>
                <li><code>https://branch2.yourdomain.com/dashboard_enhanced.php</code> - Subdomain branch</li>
            </ul>
        </div>
    </div>
</body>
</html>
