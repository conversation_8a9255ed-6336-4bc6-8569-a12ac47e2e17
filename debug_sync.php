<?php
// Debug script to test the sync functionality
require_once('proc/config.php');

// Test data (simulating what comes from online API)
$test_employees = [
    [
        'EmployeeID' => '12345',
        'AccessID' => 'B001',
        'FullName' => 'Test Employee',
        'Position' => 'Developer',
        'AgencyCompany' => 'Test Company',
        'ContactNo' => '*********',
        'DateHired' => '2024-01-01',
        'Status' => 'Active',
        'TimeIN' => '09:00:00',
        'TimeOut' => '17:00:00'
    ]
];

echo "<h2>Debug Sync Test</h2>";

// Test 1: Check if we can connect to database
echo "<h3>Test 1: Database Connection</h3>";
$result = mysqli_query($link, "SELECT COUNT(*) as count FROM tbl_personnel");
if ($result) {
    $row = mysqli_fetch_assoc($result);
    echo "✅ Database connected. Current employees: " . $row['count'] . "<br>";
} else {
    echo "❌ Database connection failed: " . mysqli_error($link) . "<br>";
}

// Test 2: Test the save function directly
echo "<h3>Test 2: Save Function Test</h3>";
echo "Testing saveEmployeesToLocal function...<br>";

// Capture output
ob_start();

// Simulate the save function
$created = 0;
$updated = 0;
$errors = 0;

foreach ($test_employees as $employee) {
    if (!isset($employee['AccessID']) || !$employee['AccessID']) {
        $errors++;
        continue;
    }

    // Clean the AccessID
    $access_id = mysqli_real_escape_string($link, trim($employee['AccessID']));

    // Check if exists
    $check_sql = "SELECT COUNT(*) as count FROM tbl_personnel WHERE AccessID = '$access_id'";
    $check_result = mysqli_query($link, $check_sql);
    $exists = false;

    if ($check_result) {
        $check_row = mysqli_fetch_assoc($check_result);
        $exists = $check_row['count'] > 0;
    } else {
        $errors++;
        continue;
    }

    // Prepare data
    $employee_id = mysqli_real_escape_string($link, $employee['EmployeeID'] ?? '');
    $full_name = mysqli_real_escape_string($link, $employee['FullName'] ?? '');
    $position = mysqli_real_escape_string($link, $employee['Position'] ?? '');
    $agency = mysqli_real_escape_string($link, $employee['AgencyCompany'] ?? '');
    $contact = mysqli_real_escape_string($link, $employee['ContactNo'] ?? '');
    $date_hired = mysqli_real_escape_string($link, $employee['DateHired'] ?? '');
    $status = mysqli_real_escape_string($link, $employee['Status'] ?? '');
    $time_in = mysqli_real_escape_string($link, $employee['TimeIN'] ?? '');
    $time_out = mysqli_real_escape_string($link, $employee['TimeOut'] ?? '');

    if ($exists) {
        $sql = "UPDATE tbl_personnel SET
            EmployeeID = '$employee_id',
            FullName = '$full_name',
            Position = '$position',
            AgencyCompany = '$agency',
            ContactNo = '$contact',
            DateHired = '$date_hired',
            Status = '$status',
            TimeIN = '$time_in',
            TimeOut = '$time_out'
            WHERE AccessID = '$access_id'";

        if (mysqli_query($link, $sql)) {
            $updated++;
            echo "✅ Updated employee: $access_id<br>";
        } else {
            $errors++;
            echo "❌ Failed to update employee: $access_id - " . mysqli_error($link) . "<br>";
        }
    } else {
        $sql = "INSERT INTO tbl_personnel (EmployeeID, AccessID, FullName, Position, AgencyCompany, ContactNo, DateHired, Status, TimeIN, TimeOut)
            VALUES ('$employee_id', '$access_id', '$full_name', '$position', '$agency', '$contact', '$date_hired', '$status', '$time_in', '$time_out')";

        if (mysqli_query($link, $sql)) {
            $created++;
            echo "✅ Created employee: $access_id<br>";
        } else {
            $errors++;
            echo "❌ Failed to create employee: $access_id - " . mysqli_error($link) . "<br>";
        }
    }
}

$output = ob_get_clean();
echo $output;

$result = [
    'success' => $errors === 0,
    'created' => $created,
    'updated' => $updated,
    'errors' => $errors
];

echo "<h3>Test 3: JSON Output Test</h3>";
echo "Result array:<br>";
echo "<pre>" . print_r($result, true) . "</pre>";

echo "JSON output:<br>";
echo "<pre>" . json_encode($result) . "</pre>";

// Test 4: Test the actual AJAX endpoint
echo "<h3>Test 4: AJAX Endpoint Test</h3>";
echo "Testing the actual save_employees endpoint...<br>";

$test_data = json_encode(['data' => $test_employees]);
echo "Test data: <pre>$test_data</pre>";

// Simulate POST request
$_SERVER['REQUEST_METHOD'] = 'POST';
$_GET['action'] = 'save_employees';

// Capture the output
ob_start();

// Simulate the POST input
$GLOBALS['HTTP_RAW_POST_DATA'] = $test_data;

// Test what the endpoint would return
header('Content-Type: application/json');
$input = json_decode($test_data, true);

if (!$input || !isset($input['data'])) {
    echo json_encode(['success' => false, 'message' => 'Invalid input data']);
} else {
    // This would call saveEmployeesToLocal($input['data'])
    echo json_encode($result); // Use our test result
}

$ajax_output = ob_get_clean();
echo "AJAX endpoint output:<br>";
echo "<pre>" . htmlspecialchars($ajax_output) . "</pre>";

// Test 5: Check for any PHP errors
echo "<h3>Test 5: Error Check</h3>";
$error = error_get_last();
if ($error) {
    echo "Last PHP error:<br>";
    echo "<pre>" . print_r($error, true) . "</pre>";
} else {
    echo "✅ No PHP errors detected<br>";
}

echo "<hr>";
echo "<p><a href='simple_sync_client.php'>← Back to Sync Client</a></p>";
?>
