<?php
// Debug the ID generation issue
include('proc/config.php');

echo "<h3>Debug: ID Generation Issue</h3>";

// Show all existing Employee IDs
echo "<h4>All Existing Employee IDs:</h4>";
$sql = "SELECT EmployeeID, FullName FROM tbl_personnel ORDER BY CAST(EmployeeID AS UNSIGNED) DESC";
$result = mysql_query($sql);

if($result) {
    echo "<table border='1'>";
    echo "<tr><th>Employee ID</th><th>Full Name</th><th>Is Numeric</th><th>Value as Number</th></tr>";
    
    while($row = mysql_fetch_assoc($result)) {
        $isNumeric = is_numeric($row['EmployeeID']) ? 'Yes' : 'No';
        $asNumber = is_numeric($row['EmployeeID']) ? intval($row['EmployeeID']) : 'N/A';
        
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['EmployeeID']) . "</td>";
        echo "<td>" . htmlspecialchars($row['FullName']) . "</td>";
        echo "<td>$isNumeric</td>";
        echo "<td>$asNumber</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "Error: " . mysql_error();
}

// Test the specific query used in generation
echo "<h4>Testing Generation Query:</h4>";
$sql = "SELECT EmployeeID FROM tbl_personnel WHERE EmployeeID REGEXP '^[1-9][0-9]{3,}$' AND CAST(EmployeeID AS UNSIGNED) >= 1001 ORDER BY CAST(EmployeeID AS UNSIGNED) DESC LIMIT 1";
$result = mysql_query($sql);

echo "<strong>Query:</strong> " . htmlspecialchars($sql) . "<br><br>";

if($result) {
    if(mysql_num_rows($result) > 0) {
        $row = mysql_fetch_assoc($result);
        echo "<strong>Highest ID found:</strong> " . $row['EmployeeID'] . "<br>";
        echo "<strong>Next ID should be:</strong> " . (intval($row['EmployeeID']) + 1) . "<br>";
    } else {
        echo "<strong>No matching IDs found, should start with:</strong> 1001<br>";
    }
} else {
    echo "Query error: " . mysql_error();
}

// Test the generation functions directly
echo "<h4>Direct Function Test:</h4>";
include('function/generateEmployeeIDs.php');

echo "<strong>Generated Biometric ID:</strong> " . generateUniqueBiometricID() . "<br>";
echo "<strong>Generated Employee ID:</strong> " . generateUniqueEmployeeID() . "<br>";

// Show what the AJAX endpoint returns
echo "<h4>AJAX Endpoint Test:</h4>";
echo "<button onclick='testAjax()'>Test AJAX Generation</button>";
echo "<div id='ajaxResult'></div>";

// Manual fix option
echo "<h4>Manual Fix:</h4>";
echo "<p>If you want to reset the Employee ID sequence to start from 1001, click below:</p>";
echo "<button onclick='resetSequence()' style='background: #dc3545;'>Reset Employee ID Sequence to 1001</button>";
echo "<div id='resetResult'></div>";
?>

<script>
function testAjax() {
    fetch('function/generateEmployeeIDs.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'action=debug_ids'
    })
    .then(response => response.json())
    .then(data => {
        let html = '<strong>AJAX Debug Result:</strong><br>';
        html += 'Success: ' + data.success + '<br>';
        if(data.existing_employee_ids) {
            html += 'Existing Employee IDs: ' + data.existing_employee_ids.join(', ') + '<br>';
        }
        html += 'Next Biometric ID: ' + (data.next_biometric || 'N/A') + '<br>';
        html += 'Next Employee ID: ' + (data.next_employee || 'N/A') + '<br>';
        
        document.getElementById('ajaxResult').innerHTML = html;
    })
    .catch(error => {
        document.getElementById('ajaxResult').innerHTML = 'Error: ' + error;
    });
}

function resetSequence() {
    if(confirm('This will update all existing numeric Employee IDs to ensure the sequence starts properly. Continue?')) {
        // This would need a separate PHP script to safely update the sequence
        document.getElementById('resetResult').innerHTML = 'Reset functionality would need to be implemented carefully to avoid data conflicts.';
    }
}
</script>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { border-collapse: collapse; margin: 10px 0; }
th, td { padding: 8px; border: 1px solid #ccc; text-align: left; }
th { background: #f0f0f0; }
button { padding: 10px 15px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
button:hover { background: #0056b3; }
#ajaxResult, #resetResult { margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 4px; }
</style>
