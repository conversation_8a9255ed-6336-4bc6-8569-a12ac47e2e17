<?php
require_once('proc/config.php');

echo "<h2>Simple Multi-Branch Setup</h2>";
echo "<p>This will safely update your database to support multiple branches.</p>";

// Disable strict mode and foreign key checks
mysql_query("SET SESSION sql_mode = ''");
mysql_query("SET FOREIGN_KEY_CHECKS = 0");
echo "<p style='color: blue;'>ℹ️ Disabled strict mode and foreign key checks for safe setup...</p>";

$steps = array();
$errors = array();

// Step 1: Create branches table
echo "<h3>Step 1: Creating branches table</h3>";
$sql = "CREATE TABLE IF NOT EXISTS `tbl_branches` (
    `branch_id` int(11) NOT NULL AUTO_INCREMENT,
    `branch_name` varchar(100) NOT NULL,
    `branch_code` varchar(10) NOT NULL,
    `branch_location` varchar(200) NOT NULL,
    `branch_status` enum('Active','Inactive') DEFAULT 'Active',
    `created_date` timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`branch_id`),
    UNIQUE KEY `branch_code` (`branch_code`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1";

if(mysql_query($sql)) {
    echo "<p style='color: green;'>✅ Branches table created successfully</p>";
    $steps[] = "Branches table created";
} else {
    echo "<p style='color: red;'>❌ Error creating branches table: " . mysql_error() . "</p>";
    $errors[] = "Failed to create branches table";
}

// Step 2: Insert branch data
echo "<h3>Step 2: Inserting branch data</h3>";
$sql = "INSERT IGNORE INTO `tbl_branches` (`branch_name`, `branch_code`, `branch_location`, `branch_status`) VALUES
    ('Ajwa Garden Multan', 'AGM', 'Multan', 'Active'),
    ('Ajwa Garden Bahawalpur', 'AGB', 'Bahawalpur', 'Active'),
    ('Ajwa Garden Sahiwal', 'AGS', 'Sahiwal', 'Active')";

if(mysql_query($sql)) {
    echo "<p style='color: green;'>✅ Branch data inserted successfully</p>";
    $steps[] = "Branch data inserted";
} else {
    echo "<p style='color: red;'>❌ Error inserting branch data: " . mysql_error() . "</p>";
    $errors[] = "Failed to insert branch data";
}

// Step 3: Add branch_id columns
echo "<h3>Step 3: Adding branch_id columns</h3>";

$tables = array('tbl_personnel', 'tbl_in_out', 'tbl_arealist', 'tb_user');

foreach($tables as $table) {
    // Check if column already exists
    $check_sql = "SHOW COLUMNS FROM `$table` LIKE 'branch_id'";
    $result = mysql_query($check_sql);
    
    if(mysql_num_rows($result) == 0) {
        $sql = "ALTER TABLE `$table` ADD COLUMN `branch_id` int(11) DEFAULT 1";
        if(mysql_query($sql)) {
            echo "<p style='color: green;'>✅ Added branch_id column to $table</p>";
            $steps[] = "Added branch_id to $table";
        } else {
            echo "<p style='color: red;'>❌ Error adding branch_id to $table: " . mysql_error() . "</p>";
            $errors[] = "Failed to add branch_id to $table";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ branch_id column already exists in $table</p>";
    }
}

// Step 4: Add assigned_branches column to tb_user
echo "<h3>Step 4: Adding assigned_branches column</h3>";
$check_sql = "SHOW COLUMNS FROM `tb_user` LIKE 'assigned_branches'";
$result = mysql_query($check_sql);

if(mysql_num_rows($result) == 0) {
    $sql = "ALTER TABLE `tb_user` ADD COLUMN `assigned_branches` text";
    if(mysql_query($sql)) {
        echo "<p style='color: green;'>✅ Added assigned_branches column to tb_user</p>";
        $steps[] = "Added assigned_branches column";
    } else {
        echo "<p style='color: red;'>❌ Error adding assigned_branches column: " . mysql_error() . "</p>";
        $errors[] = "Failed to add assigned_branches column";
    }
} else {
    echo "<p style='color: orange;'>⚠️ assigned_branches column already exists</p>";
}

// Step 5: Update existing data
echo "<h3>Step 5: Updating existing data</h3>";

$update_queries = array(
    "UPDATE `tbl_personnel` SET `branch_id` = 1 WHERE `branch_id` IS NULL OR `branch_id` = 0",
    "UPDATE `tbl_in_out` SET `branch_id` = 1 WHERE `branch_id` IS NULL OR `branch_id` = 0", 
    "UPDATE `tbl_arealist` SET `branch_id` = 1 WHERE `branch_id` IS NULL OR `branch_id` = 0",
    "UPDATE `tb_user` SET `branch_id` = 1 WHERE `branch_id` IS NULL OR `branch_id` = 0",
    "UPDATE `tb_user` SET `assigned_branches` = '1,2,3' WHERE `assigned_branches` IS NULL OR `assigned_branches` = ''"
);

foreach($update_queries as $sql) {
    if(mysql_query($sql)) {
        echo "<p style='color: green;'>✅ Data updated successfully</p>";
        $steps[] = "Updated existing data";
    } else {
        echo "<p style='color: red;'>❌ Error updating data: " . mysql_error() . "</p>";
        $errors[] = "Failed to update data";
    }
}

// Step 6: Add branch-specific access areas
echo "<h3>Step 6: Adding branch-specific access areas</h3>";
$sql = "INSERT IGNORE INTO `tbl_arealist` (`AreaName`, `AreaType`, `branch_id`) VALUES
    ('Main Entrance - Multan', 'Entry Point', 1),
    ('Office Area - Multan', 'Work Area', 1),
    ('Garden Area - Multan', 'Outdoor', 1),
    ('Main Entrance - Bahawalpur', 'Entry Point', 2),
    ('Office Area - Bahawalpur', 'Work Area', 2),
    ('Garden Area - Bahawalpur', 'Outdoor', 2),
    ('Main Entrance - Sahiwal', 'Entry Point', 3),
    ('Office Area - Sahiwal', 'Work Area', 3),
    ('Garden Area - Sahiwal', 'Outdoor', 3)";

if(mysql_query($sql)) {
    echo "<p style='color: green;'>✅ Branch-specific access areas added</p>";
    $steps[] = "Added branch-specific access areas";
} else {
    echo "<p style='color: red;'>❌ Error adding access areas: " . mysql_error() . "</p>";
    $errors[] = "Failed to add access areas";
}

// Re-enable foreign key checks
mysql_query("SET FOREIGN_KEY_CHECKS = 1");

// Summary
echo "<hr>";
echo "<h3>Setup Summary</h3>";
echo "<p><strong>Successful steps:</strong> " . count($steps) . "</p>";
echo "<p><strong>Errors:</strong> " . count($errors) . "</p>";

if(count($errors) == 0) {
    echo "<div style='background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>🎉 Setup Completed Successfully!</h4>";
    echo "<p>Your multi-branch system is now ready to use.</p>";
    echo "<p><a href='dashboard_enhanced.php' class='btn btn-primary'>Go to Enhanced Dashboard</a></p>";
    echo "<p><a href='branch_management.php' class='btn btn-info'>View Branch Management</a></p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>⚠️ Setup completed with some errors</h4>";
    echo "<p>Some steps failed, but the system might still work. Check the errors above.</p>";
    echo "</div>";
}

// Display current branches
echo "<hr>";
echo "<h3>Current Branches</h3>";
$sql = "SELECT * FROM tbl_branches ORDER BY branch_name";
$result = mysql_query($sql);

if($result && mysql_num_rows($result) > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin-top: 10px;'>";
    echo "<tr style='background: #f2f2f2;'><th>ID</th><th>Name</th><th>Code</th><th>Location</th><th>Status</th></tr>";
    while($row = mysql_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['branch_id'] . "</td>";
        echo "<td>" . $row['branch_name'] . "</td>";
        echo "<td>" . $row['branch_code'] . "</td>";
        echo "<td>" . $row['branch_location'] . "</td>";
        echo "<td>" . $row['branch_status'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>Could not retrieve branch information.</p>";
}
?>

<style>
body { 
    font-family: Arial, sans-serif; 
    margin: 20px; 
    background: #f8f9fa;
}
.btn { 
    display: inline-block; 
    padding: 10px 20px; 
    margin: 5px; 
    text-decoration: none; 
    border-radius: 5px; 
    color: white; 
    font-weight: bold;
}
.btn-primary { background-color: #007bff; }
.btn-info { background-color: #17a2b8; }
table { 
    margin-top: 10px; 
    background: white;
}
th, td { 
    padding: 8px; 
    text-align: left; 
    border: 1px solid #ddd;
}
th { 
    background-color: #f2f2f2; 
    font-weight: bold;
}
h2, h3 {
    color: #333;
}
</style>
