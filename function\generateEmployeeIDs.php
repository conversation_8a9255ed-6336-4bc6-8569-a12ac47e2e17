<?php
// Prevent any output before JSON response
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', '../temp/generate_ids_errors.log');

include('../proc/config.php');

function generateBiometricID() {
    // Get all biometric IDs from active personnel
    $sql = "SELECT AccessID FROM tbl_personnel WHERE AccessID REGEXP '^B[0-9]+$' ORDER BY CAST(SUBSTRING(AccessID, 2) AS UNSIGNED) ASC";
    $result = mysql_query($sql);

    $existingNumbers = array();
    if($result && mysql_num_rows($result) > 0) {
        while($row = mysql_fetch_assoc($result)) {
            $number = intval(substr($row['AccessID'], 1)); // Remove 'B' and convert to int
            if($number > 0) {
                $existingNumbers[] = $number;
            }
        }
    }

    // Get all deleted biometric IDs to prevent reuse
    $deletedSql = "SELECT biometric_id FROM tbl_deleted_ids WHERE biometric_id REGEXP '^B[0-9]+$'";
    $deletedResult = mysql_query($deletedSql);

    if($deletedResult && mysql_num_rows($deletedResult) > 0) {
        while($row = mysql_fetch_assoc($deletedResult)) {
            $number = intval(substr($row['biometric_id'], 1)); // Remove 'B' and convert to int
            if($number > 0) {
                $existingNumbers[] = $number;
            }
        }
    }

    // Remove duplicates and sort
    $existingNumbers = array_unique($existingNumbers);
    sort($existingNumbers);

    // Find the next available number (no gaps, always increment)
    $nextNumber = 1;
    if(!empty($existingNumbers)) {
        $nextNumber = max($existingNumbers) + 1;
    }

    // Format with leading zeros (B001, B002, etc.)
    return 'B' . str_pad($nextNumber, 3, '0', STR_PAD_LEFT);
}

function generateEmployeeID() {
    // Get all employee IDs from active personnel
    $sql = "SELECT EmployeeID FROM tbl_personnel WHERE EmployeeID REGEXP '^[0-9]+$' AND CAST(EmployeeID AS UNSIGNED) >= 1001 ORDER BY CAST(EmployeeID AS UNSIGNED) ASC";
    $result = mysql_query($sql);

    $existingIDs = array();
    if($result && mysql_num_rows($result) > 0) {
        while($row = mysql_fetch_assoc($result)) {
            $existingIDs[] = intval($row['EmployeeID']);
        }
    }

    // Get all deleted employee IDs to prevent reuse
    $deletedSql = "SELECT employee_id FROM tbl_deleted_ids WHERE employee_id REGEXP '^[0-9]+$' AND CAST(employee_id AS UNSIGNED) >= 1001";
    $deletedResult = mysql_query($deletedSql);

    if($deletedResult && mysql_num_rows($deletedResult) > 0) {
        while($row = mysql_fetch_assoc($deletedResult)) {
            $existingIDs[] = intval($row['employee_id']);
        }
    }

    // Remove duplicates and sort
    $existingIDs = array_unique($existingIDs);
    sort($existingIDs);

    // Find the next available number (no gaps, always increment)
    $nextNumber = 1001;
    if(!empty($existingIDs)) {
        $nextNumber = max($existingIDs) + 1;
    }

    return strval($nextNumber);
}

// Check if IDs are unique (including deleted IDs)
function isBiometricIDUnique($biometricID) {
    $cleanID = clean($biometricID);

    // Check active personnel
    $sql = "SELECT COUNT(*) as count FROM tbl_personnel WHERE AccessID = '$cleanID'";
    $result = mysql_query($sql);
    if($result) {
        $row = mysql_fetch_assoc($result);
        if($row['count'] > 0) {
            return false; // ID exists in active personnel
        }
    }

    // Check deleted IDs
    $deletedSql = "SELECT COUNT(*) as count FROM tbl_deleted_ids WHERE biometric_id = '$cleanID'";
    $deletedResult = mysql_query($deletedSql);
    if($deletedResult) {
        $row = mysql_fetch_assoc($deletedResult);
        if($row['count'] > 0) {
            return false; // ID exists in deleted records
        }
    }

    return true; // ID is unique
}

function isEmployeeIDUnique($employeeID) {
    $cleanID = clean($employeeID);

    // Check active personnel
    $sql = "SELECT COUNT(*) as count FROM tbl_personnel WHERE EmployeeID = '$cleanID'";
    $result = mysql_query($sql);
    if($result) {
        $row = mysql_fetch_assoc($result);
        if($row['count'] > 0) {
            return false; // ID exists in active personnel
        }
    }

    // Check deleted IDs
    $deletedSql = "SELECT COUNT(*) as count FROM tbl_deleted_ids WHERE employee_id = '$cleanID'";
    $deletedResult = mysql_query($deletedSql);
    if($deletedResult) {
        $row = mysql_fetch_assoc($deletedResult);
        if($row['count'] > 0) {
            return false; // ID exists in deleted records
        }
    }

    return true; // ID is unique
}

// Generate unique IDs with collision checking
function generateUniqueBiometricID() {
    do {
        $id = generateBiometricID();
    } while (!isBiometricIDUnique($id));
    return $id;
}

function generateUniqueEmployeeID() {
    do {
        $id = generateEmployeeID();
    } while (!isEmployeeIDUnique($id));
    return $id;
}

// AJAX endpoint
if(isset($_POST['action'])) {
    $response = array();

    try {
        if($_POST['action'] === 'generate_ids') {
            $biometricID = generateUniqueBiometricID();
            $employeeID = generateUniqueEmployeeID();

            $response['success'] = true;
            $response['biometric_id'] = $biometricID;
            $response['employee_id'] = $employeeID;

            // Add debug info
            $response['debug'] = array(
                'biometric_generated' => $biometricID,
                'employee_generated' => $employeeID,
                'timestamp' => date('Y-m-d H:i:s')
            );

        } else if($_POST['action'] === 'debug_ids') {
            // Debug endpoint to see current IDs
            $sql = "SELECT EmployeeID FROM tbl_personnel ORDER BY CAST(EmployeeID AS UNSIGNED) DESC LIMIT 10";
            $result = mysql_query($sql);
            $existing_ids = array();

            if($result) {
                while($row = mysql_fetch_assoc($result)) {
                    $existing_ids[] = $row['EmployeeID'];
                }
            }

            $response['success'] = true;
            $response['existing_employee_ids'] = $existing_ids;
            $response['next_biometric'] = generateUniqueBiometricID();
            $response['next_employee'] = generateUniqueEmployeeID();

        } else {
            $response['success'] = false;
            $response['message'] = 'Invalid action';
        }
    } catch(Exception $e) {
        $response['success'] = false;
        $response['message'] = 'Error: ' . $e->getMessage();
    }

    header('Content-Type: application/json');
    echo json_encode($response);
    exit;
}

// If called directly, return the IDs
if(!isset($_POST['action'])) {
    $biometricID = generateUniqueBiometricID();
    $employeeID = generateUniqueEmployeeID();
    
    echo json_encode(array(
        'success' => true,
        'biometric_id' => $biometricID,
        'employee_id' => $employeeID
    ));
}
?>
