-- Add branches table
CREATE TABLE IF NOT EXISTS `tbl_branches` (
  `branch_id` int(11) NOT NULL AUTO_INCREMENT,
  `branch_name` varchar(100) NOT NULL,
  `branch_code` varchar(10) NOT NULL,
  `branch_location` varchar(200) NOT NULL,
  `branch_status` enum('Active','Inactive') DEFAULT 'Active',
  `created_date` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`branch_id`),
  UNIQUE KEY `branch_code` (`branch_code`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- Insert the three branches
INSERT INTO `tbl_branches` (`branch_name`, `branch_code`, `branch_location`, `branch_status`) VALUES
('Ajwa Garden Multan', 'AGM', 'Multan', 'Active'),
('Ajwa Garden Bahawalpur', 'AGB', 'Bahawalpur', 'Active'),
('Ajwa Garden Sahiwal', 'AGS', 'Sahiwal', 'Active');

-- Add branch_id column to existing tables
ALTER TABLE `tbl_personnel` ADD COLUMN `branch_id` int(11) DEFAULT 1 AFTER `EntryID`;
ALTER TABLE `tbl_in_out` ADD COLUMN `branch_id` int(11) DEFAULT 1 AFTER `IDno`;
ALTER TABLE `tbl_arealist` ADD COLUMN `branch_id` int(11) DEFAULT 1 AFTER `ID`;
ALTER TABLE `tb_user` ADD COLUMN `branch_id` int(11) DEFAULT 1 AFTER `IDno`;
ALTER TABLE `tb_user` ADD COLUMN `assigned_branches` text AFTER `branch_id`;

-- Add foreign key constraints
ALTER TABLE `tbl_personnel` ADD FOREIGN KEY (`branch_id`) REFERENCES `tbl_branches`(`branch_id`);
ALTER TABLE `tbl_in_out` ADD FOREIGN KEY (`branch_id`) REFERENCES `tbl_branches`(`branch_id`);
ALTER TABLE `tbl_arealist` ADD FOREIGN KEY (`branch_id`) REFERENCES `tbl_branches`(`branch_id`);
ALTER TABLE `tb_user` ADD FOREIGN KEY (`branch_id`) REFERENCES `tbl_branches`(`branch_id`);

-- Update existing data to assign to first branch (Ajwa Garden Multan)
UPDATE `tbl_personnel` SET `branch_id` = 1 WHERE `branch_id` IS NULL;
UPDATE `tbl_in_out` SET `branch_id` = 1 WHERE `branch_id` IS NULL;
UPDATE `tbl_arealist` SET `branch_id` = 1 WHERE `branch_id` IS NULL;
UPDATE `tb_user` SET `branch_id` = 1, `assigned_branches` = '1,2,3' WHERE `branch_id` IS NULL;

-- Create branch-specific access areas for each branch
INSERT INTO `tbl_arealist` (`AreaName`, `AreaType`, `branch_id`) VALUES
('Main Entrance - Multan', 'Entry Point', 1),
('Office Area - Multan', 'Work Area', 1),
('Garden Area - Multan', 'Outdoor', 1),
('Main Entrance - Bahawalpur', 'Entry Point', 2),
('Office Area - Bahawalpur', 'Work Area', 2),
('Garden Area - Bahawalpur', 'Outdoor', 2),
('Main Entrance - Sahiwal', 'Entry Point', 3),
('Office Area - Sahiwal', 'Work Area', 3),
('Garden Area - Sahiwal', 'Outdoor', 3);
