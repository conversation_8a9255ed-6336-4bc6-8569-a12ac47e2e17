<?php
// Minimal test version of bulk import to isolate the issue
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', 'temp/minimal_test_errors.log');

session_start();

// Set test session
$_SESSION['SESS_USER_ID'] = 1;
$_SESSION['CURRENT_BRANCH_ID'] = 1;

header('Content-Type: application/json');
ob_start();

try {
    // Test 1: Basic response
    if(!isset($_FILES['excel_file'])) {
        ob_clean();
        echo json_encode(array(
            'success' => false,
            'message' => 'No file uploaded',
            'test' => 'minimal_import',
            'session_user' => isset($_SESSION['SESS_USER_ID']) ? $_SESSION['SESS_USER_ID'] : 'not_set',
            'post_data' => $_POST
        ));
        exit();
    }

    // Test 2: Include config
    include('proc/config.php');
    
    // Test 3: Database connection test
    $testQuery = mysql_query("SELECT COUNT(*) as count FROM tbl_personnel");
    if(!$testQuery) {
        throw new Exception('Database query failed: ' . mysql_error());
    }
    
    $testRow = mysql_fetch_assoc($testQuery);
    
    // Test 4: File processing
    $uploadedFile = $_FILES['excel_file'];
    $fileName = $uploadedFile['name'];
    $fileTmpName = $uploadedFile['tmp_name'];
    $fileSize = $uploadedFile['size'];
    $fileError = $uploadedFile['error'];
    
    if($fileError !== UPLOAD_ERR_OK) {
        throw new Exception('File upload error: ' . $fileError);
    }
    
    // Test 5: File extension check
    $fileExtension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
    if(!in_array($fileExtension, array('csv', 'xlsx', 'xls'))) {
        throw new Exception('Invalid file extension: ' . $fileExtension);
    }
    
    // Test 6: Move file
    $uploadDir = 'temp/';
    if(!is_dir($uploadDir)) {
        mkdir($uploadDir, 0777, true);
    }
    
    $tempFilePath = $uploadDir . 'test_' . uniqid() . '.' . $fileExtension;
    if(!move_uploaded_file($fileTmpName, $tempFilePath)) {
        throw new Exception('Failed to move uploaded file');
    }
    
    // Test 7: Read file (CSV only for now)
    $data = array();
    if($fileExtension == 'csv') {
        if(($handle = fopen($tempFilePath, "r")) !== FALSE) {
            $rowCount = 0;
            while(($row = fgetcsv($handle, 1000, ",")) !== FALSE && $rowCount < 5) {
                $data[] = $row;
                $rowCount++;
            }
            fclose($handle);
        }
    }
    
    // Clean up
    unlink($tempFilePath);
    
    // Success response
    ob_clean();
    echo json_encode(array(
        'success' => true,
        'message' => 'Minimal test completed successfully',
        'file_info' => array(
            'name' => $fileName,
            'size' => $fileSize,
            'extension' => $fileExtension,
            'temp_path' => $tempFilePath
        ),
        'database_info' => array(
            'personnel_count' => $testRow['count']
        ),
        'sample_data' => $data,
        'session_info' => array(
            'user_id' => $_SESSION['SESS_USER_ID'],
            'branch_id' => $_SESSION['CURRENT_BRANCH_ID']
        )
    ));

} catch(Exception $e) {
    ob_clean();
    echo json_encode(array(
        'success' => false,
        'message' => $e->getMessage(),
        'test' => 'minimal_import_error',
        'file' => __FILE__,
        'line' => __LINE__
    ));
}
?>
