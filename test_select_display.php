<!DOCTYPE html>
<html>
<head>
    <title>Test Select Display</title>
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f8f9fa;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .form-group {
            margin-bottom: 20px;
            position: relative;
        }
        
        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .modern-select {
            width: 100%;
            height: 45px;
            padding: 10px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            background-color: white;
            box-sizing: border-box;
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 16px;
            padding-right: 40px;
        }
        
        .modern-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            outline: none;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
        }
        
        .test-section h4 {
            color: #495057;
            margin-bottom: 15px;
        }
        
        .form-text {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }
        
        .row {
            margin-left: -15px;
            margin-right: -15px;
        }
        
        .col-md-6 {
            padding-left: 15px;
            padding-right: 15px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h3><i class="fa fa-test"></i> Branch Select Display Test</h3>
        
        <div class="test-section">
            <h4>Test 1: Single Column Branch Select</h4>
            <div class="form-group">
                <label class="form-label"><i class="fa fa-building"></i> Branch *</label>
                <select class="modern-select" required>
                    <option value="">Select Branch</option>
                    <?php
                        include('proc/config.php');
                        $sql = "SELECT branch_id, branch_name FROM tbl_branches ORDER BY branch_name";
                        $result = mysql_query($sql);
                        if($result && mysql_num_rows($result) > 0) {
                            while($row = mysql_fetch_assoc($result)) {
                                echo "<option value='" . $row['branch_id'] . "'>" . $row['branch_name'] . "</option>";
                            }
                        } else {
                            echo "<option value='1'>Main Branch (Sample)</option>";
                            echo "<option value='2'>Secondary Branch (Sample)</option>";
                            echo "<option value='3'>Third Branch (Sample)</option>";
                        }
                    ?>
                </select>
                <small class="form-text">This should display the full dropdown properly</small>
            </div>
        </div>
        
        <div class="test-section">
            <h4>Test 2: Two Column Layout (Modal Simulation)</h4>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label"><i class="fa fa-user"></i> Employee Name</label>
                        <input type="text" class="modern-select" placeholder="Enter name" style="padding-right: 15px;">
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="form-label"><i class="fa fa-building"></i> Branch *</label>
                        <select class="modern-select" required>
                            <option value="">Select Branch</option>
                            <option value="1">Main Branch</option>
                            <option value="2">Secondary Branch</option>
                            <option value="3">Third Branch with a Very Long Name</option>
                            <option value="4">Another Branch</option>
                        </select>
                        <small class="form-text">Branch select in two-column layout</small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h4>Test 3: Standard Bootstrap Select</h4>
            <div class="form-group">
                <label class="form-label"><i class="fa fa-building"></i> Standard Bootstrap Select</label>
                <select class="form-control">
                    <option value="">Select Branch</option>
                    <option value="1">Main Branch</option>
                    <option value="2">Secondary Branch</option>
                    <option value="3">Third Branch</option>
                </select>
                <small class="form-text">Standard Bootstrap styling for comparison</small>
            </div>
        </div>
        
        <div class="test-section">
            <h4>Test 4: Inline Style Override</h4>
            <div class="form-group">
                <label class="form-label"><i class="fa fa-building"></i> Inline Style Select</label>
                <select style="width: 100% !important; height: 45px !important; padding: 10px 15px !important; border: 2px solid #e9ecef !important; border-radius: 8px !important; font-size: 14px !important; background-color: white !important;">
                    <option value="">Select Branch</option>
                    <option value="1">Main Branch</option>
                    <option value="2">Secondary Branch</option>
                    <option value="3">Third Branch</option>
                </select>
                <small class="form-text">Using inline styles (same as in modal)</small>
            </div>
        </div>
        
        <div class="test-section">
            <h4>Current Status Check</h4>
            <p><strong>Browser:</strong> <span id="browserInfo"></span></p>
            <p><strong>Screen Width:</strong> <span id="screenWidth"></span>px</p>
            <p><strong>Container Width:</strong> <span id="containerWidth"></span>px</p>
            
            <button onclick="checkSelectWidths()" class="btn btn-primary">Check Select Widths</button>
            <div id="widthResults" style="margin-top: 10px;"></div>
        </div>
    </div>

    <script>
        // Display browser info
        document.getElementById('browserInfo').textContent = navigator.userAgent.split(' ').pop();
        document.getElementById('screenWidth').textContent = window.innerWidth;
        document.getElementById('containerWidth').textContent = document.querySelector('.test-container').offsetWidth;
        
        function checkSelectWidths() {
            const selects = document.querySelectorAll('select');
            let results = '<h5>Select Element Widths:</h5>';
            
            selects.forEach((select, index) => {
                const computedStyle = window.getComputedStyle(select);
                results += `<p>Select ${index + 1}: ${select.offsetWidth}px (computed: ${computedStyle.width})</p>`;
            });
            
            document.getElementById('widthResults').innerHTML = results;
        }
        
        // Auto-check on load
        window.onload = function() {
            setTimeout(checkSelectWidths, 500);
        };
    </script>
</body>
</html>
