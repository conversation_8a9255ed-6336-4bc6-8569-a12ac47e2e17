<?php
//import database & sanitize function
require_once('config.php');
	

//Sanitize the POST values
$username = clean($_POST['username']);
//$password = md5(clean($_POST['password']));
$password = md5(clean($_POST['password']));
// $password = clean($_POST['password']);
$branch_id = isset($_POST['branch_id']) ? (int)$_POST['branch_id'] : 1;

//Create query
$qry = "SELECT * FROM tb_user WHERE user_name='$username' AND pass_word='$password'";
$result = mysql_query($qry);

// Debug: Let's see what we're comparing
$debug_qry = "SELECT user_name, pass_word FROM tb_user WHERE user_name='$username'";
$debug_result = mysql_query($debug_qry);
if(mysql_num_rows($debug_result) > 0) {
	$debug_row = mysql_fetch_assoc($debug_result);
	error_log("DEBUG - Username: $username, Input Password Hash: $password, DB Password: " . $debug_row['pass_word']);
}

if(mysql_num_rows($result) > 0) {

	$row = mysql_fetch_assoc($result);

	// Get branch information including URL
	$branch_info = array(
		'branch_id' => $branch_id,
		'branch_name' => 'Main Branch',
		'branch_code' => 'MAIN',
		'branch_url' => 'dashboard_enhanced.php'
	);

	// Check if branches table exists and get branch details
	$check_branches_table = mysql_query("SHOW TABLES LIKE 'tbl_branches'");
	if($check_branches_table && mysql_num_rows($check_branches_table) > 0) {
		$branch_sql = "SELECT * FROM tbl_branches WHERE branch_id = '$branch_id' LIMIT 1";
		$branch_result = mysql_query($branch_sql);
		if($branch_result && mysql_num_rows($branch_result) > 0) {
			$branch_info = mysql_fetch_assoc($branch_result);
			// Ensure branch_url is set
			if(empty($branch_info['branch_url'])) {
				$branch_info['branch_url'] = 'dashboard_enhanced.php';
			}
		}
	}

	//CREATE SESSION AND START SESSION
	session_start();
	$_SESSION['SESS_USER_FULL_NAME'] = $row['full_name'];
	$_SESSION['SESS_USER_ID'] = $row['IDno'];
	$_SESSION['SESS_USER_NAME'] = $row['user_name'];
	$_SESSION['ACCESSLEVEL'] = $row['userlevel'];
	$_SESSION['LAST_ACTIVITY'] = time();//START SESSION

	// Set branch information in session
	$_SESSION['CURRENT_BRANCH_ID'] = $branch_info['branch_id'];
	$_SESSION['CURRENT_BRANCH_NAME'] = $branch_info['branch_name'];
	$_SESSION['CURRENT_BRANCH_CODE'] = isset($branch_info['branch_code']) ? $branch_info['branch_code'] : 'MAIN';

	session_write_close();

	// Return branch info for redirect including URL
	echo json_encode(array(
		'success' => true,
		'branch_id' => $branch_info['branch_id'],
		'branch_name' => $branch_info['branch_name'],
		'branch_code' => $_SESSION['CURRENT_BRANCH_CODE'],
		'branch_url' => $branch_info['branch_url']
	));

}else{
	echo json_encode(array('success' => false));
}



?>

