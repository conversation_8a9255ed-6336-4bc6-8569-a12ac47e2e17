<?php
// Test branch setup and database structure
include('proc/config.php');

echo "<h3>Testing Branch Setup</h3>";

// Check if branch_id column exists in tbl_personnel
echo "<h4>Database Structure Check:</h4>";
$sql = "DESCRIBE tbl_personnel";
$result = mysql_query($sql);

$columns = array();
if($result) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr style='background: #f0f0f0;'><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    
    while($row = mysql_fetch_assoc($result)) {
        $columns[] = $row['Field'];
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . ($row['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h4>Column Status:</h4>";
    echo "<p><strong>branch_id column exists:</strong> " . (in_array('branch_id', $columns) ? '✅ YES' : '❌ NO') . "</p>";
    echo "<p><strong>AccessArea column exists:</strong> " . (in_array('AccessArea', $columns) ? '✅ YES' : '❌ NO') . "</p>";
    
    if(!in_array('branch_id', $columns)) {
        echo "<h4>SQL to Add branch_id Column:</h4>";
        echo "<code>ALTER TABLE tbl_personnel ADD COLUMN branch_id INT;</code><br>";
        echo "<p style='color: orange;'>⚠️ You need to add the branch_id column to your database!</p>";
    }
} else {
    echo "Error: " . mysql_error();
}

// Check tbl_branches table
echo "<h4>Branches Table Check:</h4>";
$sql = "SELECT * FROM tbl_branches";
$result = mysql_query($sql);

if($result) {
    echo "<p>✅ tbl_branches table exists</p>";
    echo "<p>Branches found: " . mysql_num_rows($result) . "</p>";
    
    if(mysql_num_rows($result) > 0) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr style='background: #f0f0f0;'><th>Branch ID</th><th>Branch Name</th></tr>";
        
        while($row = mysql_fetch_assoc($result)) {
            echo "<tr>";
            echo "<td>" . $row['branch_id'] . "</td>";
            echo "<td>" . $row['branch_name'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ No branches found. You may need to add some branches first.</p>";
    }
} else {
    echo "<p style='color: red;'>❌ tbl_branches table not found or error: " . mysql_error() . "</p>";
}

// Test form data
echo "<h4>Test Form Submission:</h4>";
if(isset($_POST['test_branch'])) {
    echo "<p><strong>Selected Branch ID:</strong> " . $_POST['branch_id'] . "</p>";
    echo "<p><strong>Form data received correctly!</strong> ✅</p>";
}
?>

<form method="POST">
    <label>Test Branch Selection:</label>
    <select name="branch_id" required>
        <option value="">Select Branch</option>
        <?php
            $sql = "SELECT branch_id, branch_name FROM tbl_branches ORDER BY branch_name";
            $result = mysql_query($sql);
            if($result && mysql_num_rows($result) > 0) {
                while($row = mysql_fetch_assoc($result)) {
                    echo "<option value='" . $row['branch_id'] . "'>" . $row['branch_name'] . "</option>";
                }
            }
        ?>
    </select>
    <button type="submit" name="test_branch">Test Branch Selection</button>
</form>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { border-collapse: collapse; margin: 10px 0; }
th, td { padding: 8px; border: 1px solid #ccc; text-align: left; }
th { background: #f0f0f0; }
code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; font-family: monospace; }
form { margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 5px; }
select, button { padding: 8px; margin: 5px; }
button { background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
</style>
