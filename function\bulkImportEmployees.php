<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 0); // Don't display errors to browser
ini_set('log_errors', 1);
ini_set('error_log', '../temp/bulk_import_errors.log');

session_start();

// Set JSON header first
header('Content-Type: application/json');

// Start output buffering to catch any unexpected output
ob_start();

try {
    include('../proc/config.php');
    include('generateEmployeeIDs.php');
} catch(Exception $e) {
    // Clear any output and return error
    ob_clean();
    echo json_encode(array('success' => false, 'message' => 'Configuration error: ' . $e->getMessage()));
    exit();
}

// Check if user is logged in
if(!isset($_SESSION['SESS_USER_ID'])) {
    ob_clean();
    echo json_encode(array('success' => false, 'message' => 'User not logged in'));
    exit();
}

// Get current branch
$current_branch_id = isset($_SESSION['CURRENT_BRANCH_ID']) ? $_SESSION['CURRENT_BRANCH_ID'] : 1;

try {
    // Check if file was uploaded
    if(!isset($_FILES['excel_file']) || $_FILES['excel_file']['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('No file uploaded or upload error occurred');
    }

    $uploadedFile = $_FILES['excel_file'];
    $fileName = $uploadedFile['name'];
    $fileTmpName = $uploadedFile['tmp_name'];
    $fileSize = $uploadedFile['size'];
    $fileError = $uploadedFile['error'];

    // Validate file size (10MB max)
    if($fileSize > 10 * 1024 * 1024) {
        throw new Exception('File size too large. Maximum 10MB allowed.');
    }

    // Validate file extension
    $allowedExtensions = array('xlsx', 'xls', 'csv');
    $fileExtension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
    if(!in_array($fileExtension, $allowedExtensions)) {
        throw new Exception('Invalid file format. Only .xlsx, .xls, and .csv files are allowed.');
    }

    // Move uploaded file to temporary location
    $uploadDir = '../temp/';
    if(!is_dir($uploadDir)) {
        mkdir($uploadDir, 0777, true);
    }
    
    $tempFilePath = $uploadDir . uniqid() . '.' . $fileExtension;
    if(!move_uploaded_file($fileTmpName, $tempFilePath)) {
        throw new Exception('Failed to move uploaded file');
    }

    // Read Excel file using SimpleXLSX (lightweight Excel reader)
    $excelData = readExcelFile($tempFilePath);
    
    // Clean up temp file
    unlink($tempFilePath);

    if(empty($excelData)) {
        throw new Exception('No data found in Excel file');
    }

    // Get import options
    $skipDuplicates = isset($_POST['skip_duplicates']) && $_POST['skip_duplicates'] == '1';
    $autoGenerateIds = isset($_POST['auto_generate_ids']) && $_POST['auto_generate_ids'] == '1';

    // Process the data
    $imported = 0;
    $errors = array();
    $duplicates = 0;

    // Skip header row
    $headerRow = array_shift($excelData);
    
    foreach($excelData as $rowIndex => $row) {
        $actualRowNumber = $rowIndex + 2; // +2 because we removed header and Excel is 1-indexed
        
        try {
            // Skip empty rows
            if(empty(array_filter($row))) {
                continue;
            }

            // Map Excel columns to database fields
            $employeeData = mapExcelRowToEmployee($row, $autoGenerateIds, $current_branch_id);
            
            // Check for duplicates if option is enabled
            if($skipDuplicates) {
                $duplicateCheck = mysql_query("SELECT COUNT(*) as count FROM tbl_personnel WHERE EmployeeID='{$employeeData['EmployeeID']}' OR AccessID='{$employeeData['AccessID']}'");
                $duplicateRow = mysql_fetch_assoc($duplicateCheck);
                
                if($duplicateRow['count'] > 0) {
                    $duplicates++;
                    continue;
                }
            }

            // Insert employee
            $success = insertEmployee($employeeData);
            
            if($success) {
                $imported++;
            } else {
                $errors[] = array(
                    'row' => $actualRowNumber,
                    'message' => 'Failed to insert employee: ' . mysql_error()
                );
            }

        } catch(Exception $e) {
            $errors[] = array(
                'row' => $actualRowNumber,
                'message' => $e->getMessage()
            );
        }
    }

    // Return results
    $response = array(
        'success' => true,
        'imported' => $imported,
        'duplicates' => $duplicates,
        'errors' => $errors,
        'message' => "Import completed. $imported employees imported successfully."
    );

    if($duplicates > 0) {
        $response['message'] .= " $duplicates duplicates skipped.";
    }

    if(count($errors) > 0) {
        $response['message'] .= " " . count($errors) . " errors occurred.";
    }

    // Clear any buffered output and send JSON response
    ob_clean();
    echo json_encode($response);

} catch(Exception $e) {
    // Clear any buffered output and send error response
    ob_clean();
    echo json_encode(array(
        'success' => false,
        'message' => $e->getMessage()
    ));
}

// Function to read Excel file (simple CSV-like approach)
function readExcelFile($filePath) {
    $data = array();

    $fileExtension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));

    if($fileExtension == 'csv') {
        // Handle CSV files
        if(($handle = fopen($filePath, "r")) !== FALSE) {
            while(($row = fgetcsv($handle, 1000, ",")) !== FALSE) {
                $data[] = $row;
            }
            fclose($handle);
        }
    } else if($fileExtension == 'xlsx' || $fileExtension == 'xls') {
        // Try to convert Excel to CSV using a simple approach
        // This is a basic implementation - for production use PHPSpreadsheet

        // For now, we'll ask users to save as CSV
        throw new Exception('Please save your Excel file as CSV format (.csv) and try again. Excel files require additional libraries.');
    } else {
        throw new Exception('Unsupported file format. Please use CSV (.csv) format.');
    }

    return $data;
}

// Function to map Excel row to employee data
function mapExcelRowToEmployee($row, $autoGenerateIds, $branchId) {
    // Expected columns: Full Name, Position, Phone, Address, Date Hired, Employee ID, Biometric ID, Company, Project, Time In, Time Out, Schedule
    
    $employeeData = array();
    
    // Map columns (adjust indices based on your Excel template)
    $employeeData['FullName'] = isset($row[0]) ? clean($row[0]) : '';
    $employeeData['Position'] = isset($row[1]) ? clean($row[1]) : '';
    $employeeData['ContactNo'] = isset($row[2]) ? clean($row[2]) : '';
    $employeeData['Address'] = isset($row[3]) ? clean($row[3]) : '';
    $employeeData['DateHired'] = isset($row[4]) ? clean($row[4]) : date('Y-m-d');
    $employeeData['EmployeeID'] = isset($row[5]) ? clean($row[5]) : '';
    $employeeData['AccessID'] = isset($row[6]) ? clean($row[6]) : '';
    $employeeData['AgencyCompany'] = isset($row[7]) ? clean($row[7]) : '';
    $employeeData['ProjectAssigned'] = isset($row[8]) ? clean($row[8]) : '';
    $employeeData['TimeIN'] = isset($row[9]) ? clean($row[9]) : '';
    $employeeData['TimeOut'] = isset($row[10]) ? clean($row[10]) : '';
    $employeeData['schedule_dates'] = isset($row[11]) ? clean($row[11]) : '';
    $employeeData['branch_id'] = $branchId;

    // Validate required fields
    if(empty($employeeData['FullName'])) {
        throw new Exception('Full Name is required');
    }

    // Auto-generate IDs if needed
    if($autoGenerateIds) {
        if(empty($employeeData['EmployeeID'])) {
            $employeeData['EmployeeID'] = generateUniqueEmployeeID();
        }
        if(empty($employeeData['AccessID'])) {
            $employeeData['AccessID'] = generateUniqueBiometricID();
        }
    } else {
        if(empty($employeeData['EmployeeID']) || empty($employeeData['AccessID'])) {
            throw new Exception('Employee ID and Biometric ID are required');
        }
    }

    // Validate date format
    if(!empty($employeeData['DateHired'])) {
        $date = DateTime::createFromFormat('Y-m-d', $employeeData['DateHired']);
        if(!$date) {
            $date = DateTime::createFromFormat('m/d/Y', $employeeData['DateHired']);
            if($date) {
                $employeeData['DateHired'] = $date->format('Y-m-d');
            } else {
                $employeeData['DateHired'] = date('Y-m-d');
            }
        }
    }

    return $employeeData;
}

// Function to insert employee
function insertEmployee($data) {
    // Check if Address column exists
    $checkColumn = mysql_query("SHOW COLUMNS FROM tbl_personnel LIKE 'Address'");
    $hasAddressColumn = mysql_num_rows($checkColumn) > 0;

    if($hasAddressColumn) {
        $sql = "INSERT INTO tbl_personnel (EmployeeID, AccessID, FullName, DateHired, Position, AgencyCompany, ProjectAssigned, branch_id, TimeIN, TimeOut, schedule_dates, ContactNo, Address) 
                VALUES ('{$data['EmployeeID']}', '{$data['AccessID']}', '{$data['FullName']}', '{$data['DateHired']}', '{$data['Position']}', '{$data['AgencyCompany']}', '{$data['ProjectAssigned']}', '{$data['branch_id']}', '{$data['TimeIN']}', '{$data['TimeOut']}', '{$data['schedule_dates']}', '{$data['ContactNo']}', '{$data['Address']}')";
    } else {
        $sql = "INSERT INTO tbl_personnel (EmployeeID, AccessID, FullName, DateHired, Position, AgencyCompany, ProjectAssigned, branch_id, TimeIN, TimeOut, schedule_dates, ContactNo) 
                VALUES ('{$data['EmployeeID']}', '{$data['AccessID']}', '{$data['FullName']}', '{$data['DateHired']}', '{$data['Position']}', '{$data['AgencyCompany']}', '{$data['ProjectAssigned']}', '{$data['branch_id']}', '{$data['TimeIN']}', '{$data['TimeOut']}', '{$data['schedule_dates']}', '{$data['ContactNo']}')";
    }

    return mysql_query($sql);
}
?>
