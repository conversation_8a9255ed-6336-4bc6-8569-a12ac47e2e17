<!DOCTYPE html>
<html>
<head>
    <title>Final Bulk Import Test</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; border: 1px solid #ddd; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
    </style>
</head>
<body>
    <h1>Final Bulk Import Test</h1>
    
    <div class="test-section">
        <h2>Step 1: Download Template</h2>
        <p>First, download the official CSV template:</p>
        <button onclick="downloadTemplate()">Download CSV Template</button>
        <div id="template-result"></div>
    </div>
    
    <div class="test-section">
        <h2>Step 2: Create Test CSV</h2>
        <p>Or create a test CSV with sample data:</p>
        <button onclick="createTestCSV()">Create Test CSV</button>
        <div id="csv-result"></div>
    </div>
    
    <div class="test-section">
        <h2>Step 3: Test Bulk Import</h2>
        <form id="bulkImportForm" enctype="multipart/form-data">
            <p>Select CSV file to import:</p>
            <input type="file" id="csvFile" name="excel_file" accept=".csv,.xlsx,.xls" required><br><br>
            
            <label><input type="checkbox" name="auto_generate_ids" value="1" checked> Auto-generate Employee and Biometric IDs</label><br>
            <label><input type="checkbox" name="skip_duplicates" value="1" checked> Skip duplicate entries</label><br><br>
            
            <button type="button" onclick="testBulkImport()" id="importBtn">Test Bulk Import</button>
        </form>
        
        <div id="import-result"></div>
    </div>
    
    <div class="test-section">
        <h2>Step 4: Check Results</h2>
        <button onclick="checkEmployees()">Check Imported Employees</button>
        <div id="employees-result"></div>
    </div>

    <script>
    function downloadTemplate() {
        $('#template-result').html('<span class="info">Downloading template...</span>');
        
        // Create a temporary link to download the template
        const link = document.createElement('a');
        link.href = 'function/downloadTemplate.php';
        link.download = 'employee_import_template.csv';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        $('#template-result').html('<span class="success">✅ Template download initiated</span>');
    }
    
    function createTestCSV() {
        $('#csv-result').html('<span class="info">Creating test CSV...</span>');
        
        $.ajax({
            url: 'create_sample_csv.php',
            type: 'GET',
            dataType: 'json',
            success: function(data) {
                if(data.success) {
                    $('#csv-result').html('<span class="success">✅ Test CSV created: <a href="' + data.file + '" download>' + data.file + '</a></span>');
                } else {
                    $('#csv-result').html('<span class="error">❌ Error: ' + data.message + '</span>');
                }
            },
            error: function(xhr, status, error) {
                $('#csv-result').html('<span class="error">❌ AJAX Error: ' + error + '<br>Response: ' + xhr.responseText + '</span>');
            }
        });
    }
    
    function testBulkImport() {
        const fileInput = document.getElementById('csvFile');
        if(!fileInput.files.length) {
            alert('Please select a CSV file first');
            return;
        }
        
        const formData = new FormData(document.getElementById('bulkImportForm'));
        const btn = $('#importBtn');
        const originalText = btn.text();
        
        // Show loading state
        btn.text('Processing...').prop('disabled', true);
        $('#import-result').html('<span class="info">Testing bulk import...</span>');
        
        $.ajax({
            url: 'function/bulkImportEmployees.php',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            dataType: 'json',
            success: function(response) {
                if(response.success) {
                    let message = '<span class="success">✅ Bulk import successful!</span><br>';
                    message += '<strong>Imported:</strong> ' + response.imported + ' employees<br>';
                    
                    if(response.duplicates > 0) {
                        message += '<strong>Duplicates skipped:</strong> ' + response.duplicates + '<br>';
                    }
                    
                    if(response.errors && response.errors.length > 0) {
                        message += '<strong>Errors:</strong> ' + response.errors.length + '<br>';
                        message += '<details><summary>View Errors</summary><pre>' + JSON.stringify(response.errors, null, 2) + '</pre></details>';
                    }
                    
                    $('#import-result').html(message);
                } else {
                    $('#import-result').html('<span class="error">❌ Import failed: ' + response.message + '</span>');
                }
            },
            error: function(xhr, status, error) {
                let errorMessage = '<span class="error">❌ AJAX Error: ' + error + '</span><br>';
                errorMessage += '<strong>Status:</strong> ' + status + '<br>';
                errorMessage += '<strong>Response:</strong><br><pre>' + xhr.responseText + '</pre>';
                
                // Identify common issues
                if(xhr.responseText.includes('Fatal error') || xhr.responseText.includes('Parse error')) {
                    errorMessage += '<p><strong>Issue:</strong> PHP Fatal/Parse Error - check server configuration</p>';
                } else if(xhr.responseText.includes('<!DOCTYPE') || xhr.responseText.includes('<html')) {
                    errorMessage += '<p><strong>Issue:</strong> Server returned HTML instead of JSON - likely an error page</p>';
                } else if(xhr.responseText.trim() === '') {
                    errorMessage += '<p><strong>Issue:</strong> Empty response from server</p>';
                }
                
                $('#import-result').html(errorMessage);
            },
            complete: function() {
                btn.text(originalText).prop('disabled', false);
            }
        });
    }
    
    function checkEmployees() {
        $('#employees-result').html('<span class="info">Checking employees...</span>');
        
        $.ajax({
            url: 'function/getEmployees.php',
            type: 'GET',
            dataType: 'json',
            success: function(data) {
                if(data.data && data.data.length > 0) {
                    let message = '<span class="success">✅ Found ' + data.data.length + ' employees</span><br>';
                    message += '<strong>Recent employees:</strong><br>';
                    
                    // Show last 5 employees
                    const recent = data.data.slice(-5);
                    recent.forEach(function(emp) {
                        message += '• ' + emp.FullName + ' (ID: ' + emp.EmployeeID + ', Biometric: ' + emp.AccessID + ')<br>';
                    });
                    
                    $('#employees-result').html(message);
                } else {
                    $('#employees-result').html('<span class="info">No employees found</span>');
                }
            },
            error: function(xhr, status, error) {
                $('#employees-result').html('<span class="error">❌ Error checking employees: ' + error + '</span>');
            }
        });
    }
    </script>
</body>
</html>
